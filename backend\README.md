# Q5XT1 Backend API

Enterprise-grade Node.js/Express backend for the Q5XT1 Vehicle Registration Management System.

## 🏗️ Architecture Overview

```
backend/
├── config/           # Configuration files
├── controllers/      # Route controllers
├── middleware/       # Custom middleware
├── models/          # MongoDB/Mongoose models
├── routes/          # API route definitions
├── services/        # Business logic services
├── utils/           # Utility functions
├── docs/            # Documentation
├── scripts/         # Maintenance scripts
├── logs/            # Application logs
└── uploads/         # File storage
```

## 🚀 Quick Start

### Prerequisites
- **Node.js** v18+ (LTS recommended)
- **MongoDB Atlas** account
- **npm** or **yarn**

### Installation

```bash
# Clone and navigate to backend
cd backend

# Install dependencies
npm install

# Copy environment template
cp .env.example .env

# Configure your environment variables
nano .env
```

### Environment Configuration

Create `.env` file with these variables:

```bash
# Server Configuration
NODE_ENV=development
PORT=5000

# Database
MONGODB_URI=mongodb+srv://username:<EMAIL>/q5xt1?retryWrites=true&w=majority

# Authentication
JWT_SECRET=your-super-secure-jwt-secret-key
JWT_EXPIRE=7d
JWT_COOKIE_EXPIRE=7

# File Storage (S3 Cloud Storage)
USE_S3_STORAGE=true
AWS_REGION=eu-north-1
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_S3_BUCKET_NAME=your-s3-bucket-name

# CORS & Security
FRONTEND_URL=http://localhost:5173
CORS_ORIGINS=http://localhost:5173,http://localhost:3000

# CSV Export
CSV_ENABLED=true
```

### Development Server

```bash
# Start development server with hot reload
npm run dev

# Start production server
npm start

# Run with specific environment
NODE_ENV=production npm start
```

## 📊 Database Setup

### MongoDB Atlas Configuration

1. **Create Account**: [MongoDB Atlas](https://www.mongodb.com/cloud/atlas/register)
2. **Create Cluster**: Free tier (M0) sufficient for development
3. **Database Access**: Create user with `readWrite` privileges
4. **Network Access**: Add your IP or `0.0.0.0/0` for development
5. **Connection String**: Get from "Connect" → "Connect your application"

## 🚀 Deployment

### Production Environment Variables

```bash
# Production Configuration
NODE_ENV=production
PORT=5000

# Database (Production)
MONGODB_URI=mongodb+srv://prod-user:<EMAIL>/q5xt1-prod?retryWrites=true&w=majority

# Security (Use strong values)
JWT_SECRET=your-super-secure-production-jwt-secret-256-bit-key
JWT_EXPIRE=7d

# CORS (Production domains)
FRONTEND_URL=https://your-domain.com
CORS_ORIGINS=https://your-domain.com,https://www.your-domain.com

# File Storage (S3 Cloud Storage)
USE_S3_STORAGE=true
AWS_REGION=eu-north-1
AWS_ACCESS_KEY_ID=your-production-aws-access-key
AWS_SECRET_ACCESS_KEY=your-production-aws-secret-key
AWS_S3_BUCKET_NAME=your-production-s3-bucket

# CSV Export
CSV_ENABLED=true
```

### Deploy to Render

1. **Create Web Service** on [Render](https://render.com)
2. **Connect Repository** from GitHub
3. **Configure Service**:
   ```
   Name: q5xt1-backend
   Environment: Node
   Build Command: npm install
   Start Command: npm start
   ```
4. **Add Environment Variables** from production config above
5. **Deploy** - Automatic deployments on git push

### Deploy to Railway

```bash
# Install Railway CLI
npm install -g @railway/cli

# Login and deploy
railway login
railway init
railway up
```

### Deploy to Heroku

```bash
# Install Heroku CLI and login
heroku login

# Create app
heroku create q5xt1-backend

# Set environment variables
heroku config:set NODE_ENV=production
heroku config:set MONGODB_URI="your-production-mongodb-uri"
heroku config:set JWT_SECRET="your-production-jwt-secret"

# Deploy
git push heroku main
```

### Deploy to DigitalOcean App Platform

1. **Create App** on DigitalOcean
2. **Connect Repository**
3. **Configure**:
   ```
   Name: q5xt1-backend
   Source: GitHub repository
   Build Command: npm install
   Run Command: npm start
   ```
4. **Add Environment Variables**
5. **Deploy**

## 🔧 Features

### Core Features
- **RESTful API** - Clean, predictable API design
- **MongoDB Integration** - Mongoose ODM with schema validation
- **JWT Authentication** - Secure token-based authentication
- **File Upload** - Multer-based document upload system
- **CSV Export** - Automated export for admin auditing
- **Request Validation** - Comprehensive input validation
- **Error Handling** - Centralized error management
- **CORS Support** - Cross-origin resource sharing
- **Rate Limiting** - API abuse prevention
- **Security Headers** - Helmet.js security middleware

### Logging System
- **Winston Logger** - Professional logging with multiple transports
- **Structured Logs** - JSON-formatted logs with metadata
- **Request Tracing** - Unique request IDs for tracking
- **Automatic Rotation** - Daily log rotation with compression
- **Separate Log Files** - Different files for errors, HTTP, and combined logs

### Log Files Location: `backend/logs/`
```
logs/
├── combined-YYYY-MM-DD.log    # All log levels
├── error-YYYY-MM-DD.log       # Error-level logs only
├── http-YYYY-MM-DD.log        # HTTP request/response logs
├── exceptions-YYYY-MM-DD.log  # Uncaught exceptions
└── rejections-YYYY-MM-DD.log  # Unhandled promise rejections
```

## 📡 API Documentation

### Base URL
- **Development**: `http://localhost:5000/api`
- **Production**: `https://your-domain.com/api`

### Authentication
```bash
# Login to get JWT token
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password"
}

# Use token in subsequent requests
Authorization: Bearer <jwt-token>
```

### Vehicle Management

#### Create Vehicle
```bash
POST /api/vehicle
Content-Type: application/json
Authorization: Bearer <token>

{
  "customer": {
    "name": "John Doe",
    "phone": "1234567890",
    "email": "<EMAIL>"
  },
  "vehicleDetails": {
    "model": "Honda City",
    "colour": "White"
  }
}
```

#### Get All Vehicles
```bash
GET /api/vehicle
Authorization: Bearer <token>

# Query parameters
?status=pending&limit=10&page=1&sortBy=createdAt&sortOrder=desc
```

#### Get Vehicle by ID
```bash
GET /api/vehicle/:id
Authorization: Bearer <token>
```

#### Update Vehicle
```bash
PUT /api/vehicle/:id
Content-Type: application/json
Authorization: Bearer <token>

{
  "status": "completed",
  "customer": { ... },
  "vehicleDetails": { ... }
}
```

### Form-Specific Updates

#### Update Customer Information
```bash
PUT /api/vehicle/:id/customer
Content-Type: application/json

{
  "name": "John Doe",
  "phone": "1234567890",
  "email": "<EMAIL>",
  "altPhone": "0987654321",
  "address": {
    "houseNo": "123",
    "street": "Main Street",
    "city": "Mumbai",
    "state": "Maharashtra",
    "pincode": "400001"
  }
}
```

#### Update Vehicle Details
```bash
PUT /api/vehicle/:id/vehicle-details
Content-Type: application/json

{
  "model": "Honda City",
  "colour": "White",
  "financeMode": "loan"
}
```

#### Update Exchange Vehicle
```bash
PUT /api/vehicle/:id/exchange-vehicle
Content-Type: application/json

{
  "hasExchange": "yes",
  "model": "Maruti Swift",
  "year": "2018",
  "kmsDriven": "50000"
}
```

#### Update Registration
```bash
PUT /api/vehicle/:id/registration
Content-Type: application/json

{
  "type": "permanent",
  "hasSpecialNumber": "yes",
  "expectedSpecialNumber": "MH01AB1234"
}
```

#### Update Insurance
```bash
PUT /api/vehicle/:id/insurance
Content-Type: application/json

{
  "type": "comprehensive",
  "nominee": {
    "name": "Jane Doe",
    "age": "30",
    "relationship": "spouse"
  }
}
```

### File Upload

#### Upload Documents
```bash
POST /api/vehicle/:id/documents
Content-Type: multipart/form-data

# Form fields:
aadharCard: <file>
panCard: <file>
drivingLicense: <file>
# ... other document types
```

### CSV Export

#### Export Single Vehicle
```bash
POST /api/csv/export/:vehicleId
Authorization: Bearer <token>
```

#### Bulk Export
```bash
POST /api/csv/bulk-export
Authorization: Bearer <token>

# Query parameters
?startDate=2025-01-01&endDate=2025-01-31&limit=1000
```

#### List CSV Files
```bash
GET /api/csv/files
Authorization: Bearer <token>
```

#### Download CSV File
```bash
GET /api/csv/download/:filename
Authorization: Bearer <token>
```

## 🗄️ Database Schema

### Vehicle Model
```javascript
{
  _id: ObjectId,
  status: String, // 'pending', 'completed'

  customer: {
    name: String,
    phone: String,
    email: String,
    altPhone: String,
    address: {
      houseNo: String,
      street: String,
      city: String,
      state: String,
      pincode: String
    }
  },

  vehicleDetails: {
    model: String,
    colour: String,
    financeMode: String // 'cash', 'loan'
  },

  exchangeVehicle: {
    hasExchange: String, // 'yes', 'no'
    model: String,
    year: String,
    kmsDriven: String
  },

  registration: {
    type: String, // 'temporary', 'permanent'
    hasSpecialNumber: String, // 'yes', 'no'
    expectedSpecialNumber: String
  },

  insurance: {
    type: String, // 'thirdParty', 'comprehensive'
    nominee: {
      name: String,
      age: String,
      relationship: String
    }
  },

  documents: {
    aadharCard: { hasFilePath: Boolean, filePath: String },
    panCard: { hasFilePath: Boolean, filePath: String },
    drivingLicense: { hasFilePath: Boolean, filePath: String },
    // ... other document types
  },

  createdAt: Date,
  updatedAt: Date,
  completedAt: Date
}
```

## 🛠️ Maintenance Scripts

### Available Scripts
```bash
# Clean up stale pending records
npm run cleanup

# Clean up records older than specific hours
npm run cleanup 48

# Test logging system
node scripts/testLogging.js

# Verify Atlas connection
npm run verify-atlas
```

### Cleanup Script
Removes incomplete registrations older than specified time:
```bash
# Default: 24 hours
npm run cleanup

# Custom: 48 hours
npm run cleanup 48
```

## 🔗 Integration

### Frontend Integration
```javascript
// Frontend API service example
import axios from 'axios';

const API_BASE_URL = process.env.VITE_API_BASE_URL || 'http://localhost:5000/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

export default api;
```

## 📊 Monitoring & Health

### Health Check
```bash
GET /health
```

### Monitoring Endpoints
```bash
GET /api/csv/stats    # CSV system statistics
GET /api/vehicle/stats # Vehicle statistics
```

## 🔒 Security Features

- **JWT Authentication** - Secure token-based auth
- **Rate Limiting** - Prevent API abuse
- **CORS Protection** - Cross-origin security
- **Input Validation** - Prevent injection attacks
- **Security Headers** - Helmet.js protection
- **File Upload Security** - Type and size validation
- **Environment Variables** - Secure configuration

## 📚 Documentation

- **API Documentation**: This README
- **CSV System**: `docs/SIMPLE_CSV_SYSTEM.md`
- **Logging System**: `docs/LOGGING.md`
- **Deployment Guide**: See deployment section above

## 🐛 Troubleshooting

### Common Issues

1. **MongoDB Connection Failed**
   ```bash
   # Check connection string in .env
   # Verify network access in MongoDB Atlas
   # Ensure correct username/password
   ```

2. **File Upload Issues**
   ```bash
   # Check UPLOAD_PATH directory exists
   # Verify file permissions
   # Check file size limits
   ```

3. **CORS Errors**
   ```bash
   # Update CORS_ORIGINS in .env
   # Verify frontend URL is allowed
   ```

### Debug Mode
```bash
# Enable debug logging
DEBUG=* npm run dev

# Check logs
tail -f logs/combined-$(date +%Y-%m-%d).log
```

## 📞 Support

For technical support or questions:
- Check the documentation in `docs/` directory
- Review the logs in `logs/` directory
- Verify environment configuration
- Test with health check endpoint

---

**🚀 Production Ready** | **🔒 Secure** | **📊 Monitored** | **📚 Documented**
