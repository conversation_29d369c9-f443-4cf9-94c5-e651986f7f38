import React from 'react';
import { AlertCircle, X } from 'lucide-react';

interface ValidationAlertProps {
  isVisible: boolean;
  errors: string[];
  onClose: () => void;
}

export const ValidationAlert: React.FC<ValidationAlertProps> = ({ 
  isVisible, 
  errors, 
  onClose 
}) => {
  if (!isVisible || errors.length === 0) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-[#1a1a1a] border border-red-500 rounded-lg p-6 max-w-md w-full mx-4 shadow-2xl">
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center">
            <AlertCircle className="w-6 h-6 text-red-500 mr-2 flex-shrink-0" />
            <h3 className="text-lg font-semibold text-red-400">
              Please Fix the Following Errors
            </h3>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
        
        <div className="space-y-2 mb-6">
          {errors.map((error, index) => (
            <div key={index} className="flex items-start">
              <div className="w-2 h-2 bg-red-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
              <p className="text-red-300 text-sm">{error}</p>
            </div>
          ))}
        </div>
        
        <div className="flex justify-end">
          <button
            onClick={onClose}
            className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors"
          >
            Got it
          </button>
        </div>
      </div>
    </div>
  );
};
