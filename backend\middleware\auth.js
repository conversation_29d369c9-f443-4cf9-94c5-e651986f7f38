import jwt from 'jsonwebtoken';
import Admin from '../models/Admin.js';
import { isMongoConnected } from '../config/db.js';

// Protect routes - verify token and grant access
export const protect = async (req, res, next) => {
  let token;

  // Check if auth header exists and starts with Bear<PERSON>
  if (
    req.headers.authorization &&
    req.headers.authorization.startsWith('Bearer')
  ) {
    // Extract token from Bearer token
    token = req.headers.authorization.split(' ')[1];
  }
  // Check if token exists in cookies
  else if (req.cookies?.token) {
    token = req.cookies.token;
  }

  // If no token found, return unauthorized error
  if (!token) {
    return res.status(401).json({
      success: false,
      message: 'Not authorized to access this route'
    });
  }

  try {
    // Verify token
    const decoded = jwt.verify(
      token,
      process.env.JWT_SECRET
    );

    // Check if MongoDB is connected
    if (!isMongoConnected()) {
      return res.status(503).json({
        success: false,
        message: 'Database connection error'
      });
    }

    // Find admin by id from decoded token
    const admin = await Admin.findById(decoded.id);

    // If admin not found, return unauthorized error
    if (!admin) {
      return res.status(401).json({
        success: false,
        message: 'Not authorized to access this route'
      });
    }

    // Add admin to request object
    req.admin = admin;
    next();
  } catch (error) {
    return res.status(401).json({
      success: false,
      message: 'Not authorized to access this route'
    });
  }
};

// Grant access to specific roles
export const authorize = (...roles) => {
  return (req, res, next) => {
    // Check if admin role is included in the allowed roles
    if (!roles.includes(req.admin.role)) {
      return res.status(403).json({
        success: false,
        message: `Admin role ${req.admin.role} is not authorized to access this route`
      });
    }
    next();
  };
};
