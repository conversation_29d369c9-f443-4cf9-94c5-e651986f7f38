import React from 'react';
import { ChevronRight } from 'lucide-react';

interface Customer {
  id: string;
  name: string;
  bike: string;
  phone: string;
  date: string;
  model: string;
  color: string;
  finance: string;
  // Other fields omitted for brevity
}

interface CustomerCardProps {
  customer: Customer;
  onClick: () => void;
}

// Helper function to get text color class based on color name
const getTextColorClass = (colorName: string): string => {
  const colorMap: Record<string, string> = {
    'red': 'text-red-500',
    'blue': 'text-blue-500',
    'black': 'text-black',
    'white': 'text-gray-400',
    'green': 'text-green-500',
    'yellow': 'text-yellow-500',
    'orange': 'text-orange-500',
    'purple': 'text-purple-500',
    'pink': 'text-pink-500',
    'gray': 'text-gray-500',
    'silver': 'text-gray-400',
    'brown': 'text-amber-800',
  };

  return colorMap[colorName.toLowerCase()] || 'text-gray-500';
};

const CustomerCard: React.FC<CustomerCardProps> = ({ customer, onClick }) => {
  return (
    <div
      onClick={onClick}
      className="cursor-pointer bg-white shadow-sm border border-gray-100 rounded-xl p-4 flex flex-col justify-between min-h-[110px] hover:shadow-md transition-shadow relative"
    >
      {/* Header with name and date */}
      <div className="flex justify-between items-start mb-1.5">
        <h2 className="font-semibold text-base">{customer.name.split('_')[0]}</h2>
        <span className="text-xs text-gray-500">{customer.date}</span>
      </div>

      {/* Model and color */}
      <div className="mb-1.5">
        <p className="text-sm text-gray-600">
          {customer.model} | <span className={getTextColorClass(customer.color)}>
            {customer.color}
          </span>
        </p>
      </div>

      {/* Phone number */}
      <p className="text-sm font-medium">{customer.phone.split('_')[0]}</p>

      {/* Chevron icon */}
      <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
        <ChevronRight className="w-5 h-5 text-gray-400" />
      </div>
    </div>
  );
};

export default CustomerCard;
