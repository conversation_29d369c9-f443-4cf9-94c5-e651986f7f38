/**
 * Load state from localStorage
 * @returns The persisted state or undefined if not found
 */
export const loadState = () => {
  try {
    const serializedState = localStorage.getItem('q5xt1FormState');
    if (serializedState === null) {
      return undefined;
    }
    return JSON.parse(serializedState);
  } catch (err) {
    return undefined;
  }
};

/**
 * Save state to localStorage
 * @param state The state to persist
 */
export const saveState = (state: any) => {
  try {
    // Create a deep copy of the state to modify
    const stateCopy = JSON.parse(JSON.stringify(state));

    // Remove file objects from documents to avoid localStorage quota issues
    if (stateCopy.form?.formData?.documents) {
      Object.keys(stateCopy.form.formData.documents).forEach(key => {
        const doc = stateCopy.form.formData.documents[key];
        if (doc) {
          // Keep metadata but remove the file object
          if (doc.file) {
            delete doc.file;
            // Add a flag to indicate file was selected
            doc.fileSelected = true;
          }

          // Also remove any base64 data for backward compatibility
          if (doc.fileData) {
            delete doc.fileData;
            doc.fileSelected = true;
          }
        }
      });
    }

    const serializedState = JSON.stringify(stateCopy);
    localStorage.setItem('q5xt1FormState', serializedState);
  } catch (err) {
    // Silently fail
  }
};

/**
 * Clear persisted state from localStorage
 */
export const clearState = () => {
  try {
    // Clear all Q5X-related localStorage items
    localStorage.removeItem('q5xt1FormState');
    localStorage.removeItem('q5xt1VehicleId');
    localStorage.removeItem('dashboardData');
    localStorage.removeItem('customerData');

    // Clear any sessionStorage items as well
    sessionStorage.removeItem('q5xt1FormState');
    sessionStorage.removeItem('q5xt1VehicleId');

  } catch (err) {
    // Silently fail
  }
};
