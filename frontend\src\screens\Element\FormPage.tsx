import React, { useState, useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Card, CardContent } from "../../components/ui/card";
import { ButtonGroup } from "../../components/ui/button-group";
import { CustomerInformation, CustomerInformationRef } from "../../components/forms/CustomerInformation";
import { VehicleDetails } from "../../components/forms/VehicleDetails";
import { ExchangeVehicleDetails } from "../../components/forms/ExchangeVehicleDetails";
import { RegistrationRequirments} from "../../components/forms/RegistrationRequirments";
import { InsuranceDetails } from "../../components/forms/InsuranceDetails";
import { FileUploads } from "../../components/forms/FileUploads";
import { ProgressIndicator } from "../../components/ui/progress-indicator";
import { SubmissionProgress } from "../../components/ui/submission-progress";
import { SuccessModal } from "../../components/ui/success-modal";
import { validateSection, resetFormData } from "../../store/formSlice";
import {
  createVehicle,
  updateFormSection,
  submitForm,
  clearErrors,
  setOfflineMode,
  setStorageType,
  resetApiState,
  resetSubmissionProgress
} from "../../store/apiSlice";
import { RootState, AppDispatch, store } from "../../store/store";
import { clearState } from "../../utils/localStorage";
import { vehicleAPI } from "../../services/api";
import "../../assets/fonts/fonts.css"; // Using Inter font
import ReCaptchaGate from "../../components/ReCaptchaGate";

export const FormPage = (): JSX.Element => {
  const dispatch = useDispatch<AppDispatch>();
  const { formData: { name, phone, email, altPhone, houseNo, street, city, state: userState, pincode, vehicleDetails, hasSpecialNumber, expectedSpecialNumber, registrationType, hasExchange, exchangeModel, exchangeYear, exchangeKmsDriven, insuranceType, nomineeName, nomineeAge, nomineeRelationship, documents }, errors, currentSection } = useSelector((state: RootState) => state.form);
  const { vehicleId, loading, error, formSubmitted, offlineMode, storageType, submissionProgress, submissionStatus, duplicateCheck } = useSelector((state: RootState) => state.api);

  // Ref for CustomerInformation component to trigger validation alerts
  const customerInformationRef = useRef<CustomerInformationRef>(null);

  const forms = [
    { component: CustomerInformation, title: "Customer Information", section: "customer" },
    { component: VehicleDetails, title: "Vehicle Details", section: "vehicleDetails" },
    { component: ExchangeVehicleDetails, title: "Exchange Vehicle Details", section: "exchangeVehicle" },
    { component: RegistrationRequirments, title: "Registration Requirements", section: "registration" },
    { component: InsuranceDetails, title: "Insurance Details", section: "insurance" },
    { component: FileUploads, title: "File Uploads", section: "documents" }
  ];

  // Find the page index based on the current section name in Redux
  const findPageIndex = (sectionName: string) => {
    const index = forms.findIndex(form => form.section === sectionName);
    return index !== -1 ? index + 1 : 1; // Default to page 1 if not found
  };

  // Initialize current page from the current section in Redux
  const [currentPage, setCurrentPage] = useState(() => {
    // If form is submitted, always start from page 1
    if (store.getState().api.formSubmitted) {
      return 1;
    }
    // Get the current section from Redux state
    const currentSectionName = store.getState().form.currentSection.name;
    return findPageIndex(currentSectionName);
  });
  const [direction, setDirection] = useState<'next' | 'back'>('next');
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [showResetConfirmation, setShowResetConfirmation] = useState(false);
  const [isRecaptchaVerified, setIsRecaptchaVerified] = useState(false);

  // Handle reCAPTCHA verification
  const handleRecaptchaVerified = () => {
    setIsRecaptchaVerified(true);
  };

  // Handle page refresh - ensure form starts fresh
  useEffect(() => {
    // Check if the page was refreshed (no navigation state)
    const isPageRefresh = !window.performance.getEntriesByType('navigation')[0] ||
                         (window.performance.getEntriesByType('navigation')[0] as any).type === 'reload';

    if (isPageRefresh) {
      // Clear all state on page refresh
      clearState();
      dispatch(resetApiState());
      dispatch(resetFormData());
      setCurrentPage(1);
      setShowSuccessMessage(false);
      // Page refreshed - form reset to initial state
    }
  }, []); // Run only on component mount

  // Check vehicle ID on component mount
  useEffect(() => {
    const checkVehicleId = async () => {
      // If we have a vehicleId from localStorage, verify it exists in the database
      if (vehicleId) {
        try {
          // Try to get the vehicle by ID
          await vehicleAPI.getVehicleById(vehicleId);
          // Vehicle ID verified successfully
        } catch (error) {
          // Vehicle ID not found in database, reset form
          resetFormAndCreateNewVehicle();
        }
      } else if (currentPage > 1) {
        // If we're on a page other than the first but don't have a vehicleId,
        // something is wrong - reset to the first page
        setCurrentPage(1);
      }
    };

    checkVehicleId();
  }, []); // Run only on component mount

  // Show success message when form is submitted and clear all state
  useEffect(() => {
    if (formSubmitted) {
      setShowSuccessMessage(true);

      // Immediately clear all state data for a fresh start
      clearState(); // Clear localStorage

      // Clear Redux state completely
      dispatch(resetApiState()); // Reset API state including vehicleId
      dispatch(resetFormData()); // Reset all form data

      // Reset submission progress after a delay to allow the user to see the 100% completion
      setTimeout(() => {
        dispatch(resetSubmissionProgress());
      }, 2000);

      // Clear any additional browser storage that might persist
      try {
        sessionStorage.removeItem('q5xt1FormState');
        sessionStorage.removeItem('q5xt1VehicleId');
        localStorage.removeItem('q5xt1VehicleId');
        localStorage.removeItem('dashboardData');
        localStorage.removeItem('customerData');
      } catch (error) {
        // Storage clearing failed - continue silently
      }
    }
  }, [formSubmitted, dispatch]);

  // Function to reset the form and create a new vehicle record
  const resetFormAndCreateNewVehicle = async () => {
    try {
      // Clear localStorage
      clearState();

      // Reset Redux API state - this will clear the vehicleId
      dispatch(resetApiState());

      // Reset Redux form state - this will clear all form data
      dispatch(resetFormData());

      // Don't create a vehicle record immediately with empty data
      // Vehicle will be created when customer fills the first form and clicks "Next"

      // Go back to the first page
      setCurrentPage(1);
    } catch (error) {
      // Fall back to offline mode if we can't create a new vehicle
      dispatch(setOfflineMode(true));
    }
  };

  const handleNext = async () => {
    // Prevent navigation if form is submitted and success message is shown
    if (formSubmitted || showSuccessMessage) {
      return;
    }

    if (currentPage < forms.length) {
      const currentSectionName = forms[currentPage - 1].section;

      // Validate the current section - force validation regardless of previous state
      dispatch(validateSection(currentSectionName));

      // Get the updated validation state - need to wait for the validation to complete
      await new Promise(resolve => setTimeout(resolve, 0)); // Allow Redux to update state
      const { currentSection, formData } = store.getState().form;

      // Special handling for exchange vehicle section
      let isValid = currentSection.isValid;

      // If we're on the exchange vehicle page and "yes" is selected, ensure all fields are filled
      if (currentSectionName === "exchangeVehicle" && formData.hasExchange === "yes") {
        if (!formData.exchangeModel || !formData.exchangeYear || !formData.exchangeKmsDriven) {
          isValid = false;

          // Force validation again to show errors
          dispatch(validateSection(currentSectionName));
          await new Promise(resolve => setTimeout(resolve, 0));
        }
      }

      // Don't proceed if validation fails
      if (!isValid) {
        // Show validation alert for customer information page
        if (currentPage === 1 && customerInformationRef.current) {
          customerInformationRef.current.showValidationErrors();
        }
        return; // Stop here - don't go to next page if validation fails
      }

      // Additional check for customer information page - prevent duplicate customers
      if (currentPage === 1 && duplicateCheck?.isDuplicate) {
        // Don't proceed if duplicate customer is found
        return;
      }

      try {
        // API Call Optimization: Only make API calls at critical points
        // 1. When first form (Customer Information) is completed
        // 2. When moving from form 5 to form 6
        // 3. Final submission (handled in handleSubmit)

        // Critical Point 1: First form completion - Create or update customer data
        if (currentPage === 1) {
          // Extract customer data
          const customerData = {
            name,
            phone,
            email,
            altPhone,
            houseNo,
            street,
            city,
            state: userState,
            pincode
          };

          if (!vehicleId) {
            // If no vehicle ID exists, create a new vehicle with customer data
            const result = await dispatch(createVehicle({ customer: customerData }));

            // Check if the vehicle creation was successful
            if (result.type === 'api/createVehicle/rejected') {
              return; // Don't proceed if vehicle creation failed
            }
          } else {
            // If vehicle ID exists, update the customer info
            const updateResult = await dispatch(updateFormSection({
              section: "customer",
              data: customerData
            }));

            // Check if the update was successful
            if (updateResult.type === 'api/updateFormSection/rejected') {
              // Continue anyway to allow offline mode to work
            }
          }
        }
        // Critical Point 2: Moving from form 5 (Insurance) to form 6 (Documents)
        else if (currentPage === 5 && vehicleId) {
          // Prepare all data from forms 2-5 to be saved at once
          const combinedData = {
            // Vehicle Details (form 2)
            vehicleDetails: {
              model: vehicleDetails?.model || "",
              colour: vehicleDetails?.colour || "",
              financeMode: vehicleDetails?.financeMode || "",
            },

            // Exchange Vehicle (form 3)
            hasExchange: store.getState().form.formData.hasExchange || "no",
            exchangeModel,
            exchangeYear,
            exchangeKmsDriven,

            // Registration (form 4)
            hasSpecialNumber,
            expectedSpecialNumber,
            registrationType,

            // Insurance (form 5)
            insuranceType,
            nomineeName,
            nomineeAge,
            nomineeRelationship,
          };

          // Update all sections at once
          const updateResult = await dispatch(updateFormSection({
            section: "combinedForms",
            data: combinedData
          }));

          // Check if the update was successful
          if (updateResult.type === 'api/updateFormSection/rejected') {
            // Continue anyway to allow offline mode to work
          }
        }
        // For all other transitions, just update the Redux store without API calls

        // Proceed to next page
        setDirection('next');
        setCurrentPage(currentPage + 1);
      } catch (error) {
        // Show error message but allow proceeding to next page
        setDirection('next');
        setCurrentPage(currentPage + 1);
      }
    }
  };

  const handlePrevious = async () => {
    // Prevent navigation if form is submitted and success message is shown
    if (formSubmitted || showSuccessMessage) {
      return;
    }

    if (currentPage > 1) {
      // If we're moving back to the customer information page (page 1) and we have a vehicleId,
      // update the customer information in the database to ensure consistency
      if (currentPage === 2 && vehicleId) {
        try {
          // Extract customer data
          const customerData = {
            name,
            phone,
            email,
            altPhone,
            houseNo,
            street,
            city,
            state: userState,
            pincode
          };

          // Update customer info in the database
          await dispatch(updateFormSection({
            section: "customer",
            data: customerData
          }));
        } catch (error) {
          // Continue anyway to allow the user to navigate back
        }
      }

      setDirection('back');
      setCurrentPage(currentPage - 1);
    }
  };

  const handleSubmit = async () => {
    // Prevent submission if form is already submitted and success message is shown
    if (formSubmitted || showSuccessMessage) {
      return;
    }

    const currentSectionName = forms[currentPage - 1].section;

    // Validate the final section - force validation regardless of previous state
    dispatch(validateSection(currentSectionName));

    // Get the updated validation state - need to wait for the validation to complete
    await new Promise(resolve => setTimeout(resolve, 0)); // Allow Redux to update state
    const { currentSection } = store.getState().form;
    const isValid = currentSection.isValid;

    // Don't proceed if validation fails
    if (!isValid) {
      // Show validation alert for customer information page
      if (currentPage === 1 && customerInformationRef.current) {
        customerInformationRef.current.showValidationErrors();
      }
      return; // Stop here - don't submit if validation fails
    }

    try {
      // First update the final section
      let finalSectionData = {};
      if (currentSectionName === "documents") {
        finalSectionData = documents;
      }

      if (vehicleId) {
        await dispatch(updateFormSection({
          section: currentSectionName,
          data: finalSectionData
        }));

        // Then submit the entire form
        // Create a complete form data object with all fields
        const completeFormData = {
          name,
          phone,
          email,
          altPhone,
          houseNo,
          street,
          city,
          state: userState,
          pincode,
          vehicleDetails,
          hasSpecialNumber,
          expectedSpecialNumber,
          registrationType,
          hasExchange: store.getState().form.formData.hasExchange || "no",
          exchangeModel,
          exchangeYear,
          exchangeKmsDriven,
          insuranceType,
          nomineeName,
          nomineeAge,
          nomineeRelationship,
          documents
        };

        await dispatch(submitForm(completeFormData));

        // Success message is shown via useEffect when formSubmitted changes
      }
    } catch (error) {
      // Form submission error - handled by Redux state
    }
  };

  const currentForm = forms[currentPage - 1];
  const FormComponent = currentForm.component;

  // Show reCAPTCHA gate first, then form after verification
  if (!isRecaptchaVerified) {
    return (
      <ReCaptchaGate
        onVerified={handleRecaptchaVerified}
        title="Security Verification Required"
        description="Please complete the security verification to access the vehicle registration form."
      />
    );
  }

  return (
    <div className="min-h-screen bg-[#1e1f20] flex flex-col">
      {/* Fixed Header with Blur Effect */}
      <header className="w-full h-14 sm:h-16 md:h-20 bg-[#0e0e0e]/90 backdrop-blur-md fixed top-0 left-0 z-50 flex items-center justify-between px-3 sm:px-6 md:px-12 lg:px-[100px] flex-shrink-0 border-b border-[#333333]/30 shadow-md transition-all duration-300 ease-in-out">
        <img
          className="w-[120px] h-auto sm:w-[150px] md:w-[227px] transition-all duration-300 ease-in-out"
          alt="Quant 5X Logo"
          src="/logo.png"
        />
      </header>

      {/* Spacer to account for fixed header */}
      <div className="h-14 sm:h-16 md:h-20 transition-all duration-300 ease-in-out"></div>

      {/* Progress Indicator - Hide when success message is shown */}
      {!showSuccessMessage && (
        <div className="w-full max-w-[924px] mx-auto py-2 sm:py-4 md:py-8 px-3 sm:px-6 md:px-8 lg:px-0 transition-all duration-300 ease-in-out">
          <ProgressIndicator
            currentStep={currentPage}
            totalSteps={forms.length}
          />
        </div>
      )}

      {/* Main Content */}
      <div className="flex-1 flex items-start sm:items-center justify-center pb-4 sm:pb-6 md:pb-10 px-3 sm:px-6 md:px-8 lg:px-0 overflow-y-auto transition-all duration-300 ease-in-out">
        {/* Submission Progress Indicator */}
        {submissionProgress > 0 && submissionProgress < 100 && (
          <SubmissionProgress
            progress={submissionProgress}
            status={submissionStatus}
          />
        )}

        {/* Success Message */}
        <SuccessModal
          isOpen={showSuccessMessage}
          onClose={() => setShowSuccessMessage(false)}
          title="Form Submitted Successfully"
          message="Your vehicle has been successfully registered."
          showIndefinitely={true}
        />

        {/* Reset Confirmation Dialog */}
        {showResetConfirmation && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white p-8 rounded-lg max-w-md text-center">
              <div className="text-yellow-500 text-5xl mb-4">⚠️</div>
              <h2 className="text-2xl font-bold mb-4">Reset Form?</h2>
              <p className="mb-6">This will clear all your form data and create a new vehicle record. This action cannot be undone.</p>
              <div className="flex justify-center gap-4">
                <button
                  onClick={() => setShowResetConfirmation(false)}
                  className="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600"
                >
                  Cancel
                </button>
                <button
                  onClick={() => {
                    setShowResetConfirmation(false);
                    resetFormAndCreateNewVehicle();
                  }}
                  className="bg-red-500 text-white px-6 py-2 rounded-lg hover:bg-red-600"
                >
                  Reset Form
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Form Card - Hide when success message is shown */}
        {!showSuccessMessage && (
          <Card className="w-full max-w-[924px] mx-auto bg-[#0e0e0e] rounded-[12px] sm:rounded-[16px] md:rounded-[20px] border-none my-2 sm:my-0 transition-all duration-300 ease-in-out">
            <CardContent className="p-4 sm:p-6 md:p-8 lg:p-10 transition-all duration-300 ease-in-out">
            {/* Card Header */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3 sm:mb-5 md:mb-8 transition-all duration-300 ease-in-out">
              <h1 className="text-[#e3e3e3] text-[24px] sm:text-[32px] md:text-[40px] lg:text-[48px] font-light transition-all duration-300 ease-in-out mb-2 sm:mb-0">
                {currentForm.title}
              </h1>
              <div className="flex items-center justify-between sm:justify-end gap-4">
                <button
                  onClick={() => setShowResetConfirmation(true)}
                  className="text-xs sm:text-sm text-[#e3e3e3] bg-[#333333] hover:bg-[#444444] px-3 py-1.5 rounded transition-all duration-300 ease-in-out"
                  title="Reset form and create a new vehicle record"
                >
                  Reset Form
                </button>
                <span className="text-[#e3e3e3] text-sm sm:text-base md:text-xl transition-all duration-300 ease-in-out">
                  Page {currentPage} of {forms.length}
                </span>
              </div>
            </div>

            {/* Status messages */}
            {offlineMode && (
              <div className="bg-yellow-500 bg-opacity-20 border border-yellow-500 text-yellow-700 p-3 rounded-lg mb-4">
                <p className="font-medium">
                  <span className="inline-block w-4 h-4 bg-yellow-500 rounded-full mr-2"></span>
                  Offline Mode
                </p>
                <p className="text-sm mt-1">
                  The application is running in offline mode. Your data will be saved locally and can be submitted when you're back online.
                </p>
                {storageType === 'memory' && (
                  <p className="text-sm mt-1">
                    <strong>Note:</strong> Your data is being stored in the server's memory. It will be lost if the server restarts.
                  </p>
                )}
              </div>
            )}

            {/* Error message if API error exists */}
            {error && !offlineMode && (
              <div className="bg-red-500 bg-opacity-20 border border-red-500 text-red-500 p-3 rounded-lg mb-4">
                <p className="font-medium">Error: {error}</p>
                <p className="text-sm mt-1">
                  {error.includes("not found")
                    ? "The vehicle record could not be found. This may happen if the server was restarted or the database was cleared."
                    : "There was a problem communicating with the server. You can continue in offline mode."}
                </p>
                <div className="flex flex-wrap gap-2 mt-2">
                  <button
                    className="text-sm bg-red-500 text-white px-3 py-1 rounded hover:bg-red-600"
                    onClick={() => dispatch(clearErrors())}
                  >
                    Dismiss
                  </button>

                  <button
                    className="text-sm bg-yellow-500 text-white px-3 py-1 rounded hover:bg-yellow-600"
                    onClick={() => {
                      dispatch(setOfflineMode(true));
                      dispatch(clearErrors());
                    }}
                  >
                    Continue Offline
                  </button>
                </div>
              </div>
            )}

            {/* Form Content with Transition */}
            <div className="relative overflow-hidden transition-all duration-300 ease-in-out">
              {loading ? (
                <div className="flex justify-center items-center py-10">
                  <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#640064]"></div>
                </div>
              ) : (
                <div
                  key={currentPage}
                  className={`transition-all duration-300 ease-in-out
                    ${direction === 'next' ? 'animate-slideLeft' : 'animate-slideRight'}`}
                >
                  {currentPage === 1 ? (
                    <CustomerInformation ref={customerInformationRef} key={currentPage} />
                  ) : (
                    <FormComponent key={currentPage} />
                  )}

                  {/* No error messages displayed - just highlight fields with errors */}
                </div>
              )}
              <ButtonGroup
                currentStep={currentPage}
                totalSteps={forms.length}
                onNext={handleNext}
                onBack={handlePrevious}
                onSubmit={handleSubmit}
                disabled={loading}
                nextDisabled={currentPage === 1 && duplicateCheck?.isDuplicate}
              />
            </div>
          </CardContent>
        </Card>
        )}
      </div>
    </div>
  );
};