import React, { useEffect, useState } from "react";
import { Input } from "../ui/input";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../store/store";
import { updateFormField, validateSection, setFieldTouched } from "../../store/formSlice";
import { validationRules } from "../../utils/validation";

export const ExchangeVehicleDetails = () => {
  const dispatch = useDispatch();
  const { formData, errors, touchedFields } = useSelector((state: RootState) => state.form);
  const { hasExchange, exchangeModel, exchangeYear, exchangeKmsDriven } = formData;

  // State for animation
  const [showExchangeFields, setShowExchangeFields] = useState(hasExchange === "yes");

  // Initialize form data when component loads
  useEffect(() => {
    // Always clear errors on component mount
    dispatch({ type: 'form/clearSectionErrors', payload: 'exchangeVehicle' });

    // Initialize hasExchange if it doesn't exist
    if (!formData.hasExchange) {
      dispatch(updateFormField({
        field: "hasExchange",
        value: "no"
      }));

      // When "no" is selected, set dependent fields to null (clean data)
      dispatch(updateFormField({ field: "exchangeModel", value: null }));
      dispatch(updateFormField({ field: "exchangeYear", value: null }));
      dispatch(updateFormField({ field: "exchangeKmsDriven", value: null }));

      // Don't validate on initialization to prevent showing errors before user interaction
    } else if (formData.hasExchange === "yes") {
      // If hasExchange is already "yes" and all fields are filled, clear errors
      if (formData.exchangeModel && formData.exchangeYear && formData.exchangeKmsDriven) {
        // Validate exchangeKmsDriven
        if (/^\d+$/.test(formData.exchangeKmsDriven) &&
            Number(formData.exchangeKmsDriven) <= 9999999) {
          // All fields are valid, clear any errors
          dispatch({ type: 'form/clearSectionErrors', payload: 'exchangeVehicle' });
        }
      }
    }
  }, [dispatch, formData.hasExchange, formData.exchangeModel, formData.exchangeYear, formData.exchangeKmsDriven]);

  // Update animation state when hasExchange changes
  useEffect(() => {
    if (hasExchange === "yes") {
      setShowExchangeFields(true);

      // Check if all fields are filled and valid, then clear errors
      if (exchangeModel && exchangeYear && exchangeKmsDriven) {
        // Validate exchangeKmsDriven
        if (/^\d+$/.test(exchangeKmsDriven) && Number(exchangeKmsDriven) <= 9999999) {
          // All fields are valid, clear any errors
          dispatch({ type: 'form/clearSectionErrors', payload: 'exchangeVehicle' });
        }
      }
      // Don't validate automatically when fields are missing to prevent showing errors before user interaction
    } else {
      // Small delay before hiding to allow animation to complete
      const timer = setTimeout(() => {
        setShowExchangeFields(false);
      }, 300);
      return () => clearTimeout(timer);
    }
  }, [hasExchange, exchangeModel, exchangeYear, exchangeKmsDriven, dispatch]);

  // Function to check if a field is required
  const isFieldRequired = (fieldName: string) => {
    // If hasExchange is "no", then no fields are required
    if (hasExchange === "no" && fieldName !== "hasExchange") {
      return false;
    }

    const rules = validationRules.exchangeVehicle?.[fieldName as keyof typeof validationRules.exchangeVehicle];
    return rules && rules.required;
  };

  // Required field marker component
  const RequiredMarker = () => (
    <span className="text-red-500 ml-1">*</span>
  );

  // Helper function to mark a field as touched
  const markFieldAsTouched = (name: string) => {
    dispatch(setFieldTouched(name));
  };

  // Check if a field should show validation error
  const shouldShowError = (fieldName: string) => {
    return touchedFields && touchedFields[fieldName] && errors && errors[fieldName];
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;

    // Special handling for exchangeKmsDriven to ensure it's a valid number
    if (name === "exchangeKmsDriven") {
      // Only allow numeric input
      if (value !== "" && !/^\d+$/.test(value)) {
        return; // Don't update if not a valid number
      }

      // Limit to reasonable values
      if (Number(value) > 9999999) {
        return; // Don't update if too large
      }
    }

    dispatch(updateFormField({
      field: name,
      value
    }));

    // If changing hasExchange, handle appropriately
    if (name === "hasExchange") {
      if (value === "no") {
        // If "no" is selected, set dependent fields to null (clean data)
        dispatch(updateFormField({ field: "exchangeModel", value: null }));
        dispatch(updateFormField({ field: "exchangeYear", value: null }));
        dispatch(updateFormField({ field: "exchangeKmsDriven", value: null }));

        // Clear any existing errors for this section when "no" is selected
        dispatch({ type: 'form/clearSectionErrors', payload: 'exchangeVehicle' });

        // Skip validation when "no" is selected - we don't need to validate
        return;
      } else if (value === "yes") {
        // If "yes" is selected, initialize fields with empty strings for user input
        dispatch(updateFormField({ field: "exchangeModel", value: "" }));
        dispatch(updateFormField({ field: "exchangeYear", value: "" }));
        dispatch(updateFormField({ field: "exchangeKmsDriven", value: "" }));

        // Clear any existing errors for exchange fields
        dispatch({ type: 'form/clearFieldError', payload: "exchangeModel" });
        dispatch({ type: 'form/clearFieldError', payload: "exchangeYear" });
        dispatch({ type: 'form/clearFieldError', payload: "exchangeKmsDriven" });
      }
    }

    // Mark field as touched
    markFieldAsTouched(name);

    // Clear any existing errors for this field
    if (errors && errors[name]) {
      dispatch({
        type: 'form/clearFieldError',
        payload: name
      });
    }

    // Force clear errors for exchangeKmsDriven if it's a valid value
    if (name === "exchangeKmsDriven" && value !== "" && /^\d+$/.test(value) && Number(value) <= 9999999) {
      dispatch({
        type: 'form/clearFieldError',
        payload: "exchangeKmsDriven"
      });
    }

    // Only validate if the field has been touched
    if (touchedFields && touchedFields[name]) {
      setTimeout(() => {
        dispatch(validateSection("exchangeVehicle"));
      }, 0);
    }
  };

  // Handle blur event to mark field as touched and validate
  const handleBlur = (e: React.FocusEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name } = e.target;
    markFieldAsTouched(name);

    // Validate the field after it's been touched
    setTimeout(() => {
      dispatch(validateSection("exchangeVehicle"));
    }, 0);
  };

  return (
    <div className="space-y-4 sm:space-y-5 md:space-y-6">
      {/* Exchange Option */}
      <div className="space-y-1 sm:space-y-1.5">
        <label className="text-[#e3e3e3] text-lg sm:text-xl md:text-2xl font-light">
          Exchange
          <RequiredMarker />
        </label>
        <div className="flex items-center space-x-8 mt-1 mb-6">
          <label className="flex items-center cursor-pointer">
            <input
              type="radio"
              name="hasExchange"
              value="yes"
              checked={hasExchange === "yes"}
              onChange={handleChange}
              className="h-5 w-5 accent-[#640064]"
            />
            <span className="ml-3 text-[#e3e3e3] text-base sm:text-lg">Yes</span>
          </label>
          <label className="flex items-center cursor-pointer">
            <input
              type="radio"
              name="hasExchange"
              value="no"
              checked={hasExchange === "no"}
              onChange={handleChange}
              className="h-5 w-5 accent-[#640064]"
            />
            <span className="ml-3 text-[#e3e3e3] text-base sm:text-lg">No</span>
          </label>
        </div>
        {shouldShowError("hasExchange") && (
          <p className="text-red-500 text-sm mt-1 absolute">{errors["hasExchange"]}</p>
        )}
      </div>

      {/* Conditional Exchange Fields */}
      <div
        className={`space-y-4 sm:space-y-5 md:space-y-6 overflow-hidden transition-all duration-300 ease-in-out ${
          hasExchange === "yes"
            ? "max-h-[500px] opacity-100 transform translate-y-0"
            : "max-h-0 opacity-0 transform -translate-y-4"
        }`}
      >
        {showExchangeFields && (
          <>
            {/* Model */}
            <div className="space-y-1 sm:space-y-1.5">
              <label className="text-[#e3e3e3] text-lg sm:text-xl md:text-2xl font-light">
                Model
                {isFieldRequired("exchangeModel") && <RequiredMarker />}
              </label>
              <div className="relative mb-6">
                <select
                  className={`h-[45px] sm:h-[50px] md:h-[60px] w-full px-3 sm:px-4 rounded-lg border bg-transparent text-[#e3e3e3] text-sm sm:text-base placeholder:text-[#818181] focus:outline-none focus:border-[#640064] appearance-none custom-select ${
                    shouldShowError("exchangeModel") ? "border-red-500" : "border-[#333333]"
                  }`}
                  name="exchangeModel"
                  value={exchangeModel || ""}
                  onChange={handleChange}
                  onBlur={handleBlur}
                >
                  <option value="" disabled>Select your Model</option>
                  <option value="model1">Model 1</option>
                  <option value="model2">Model 2</option>
                  <option value="model3">Model 3</option>
                </select>
              </div>
              {shouldShowError("exchangeModel") && (
                <p className="text-red-500 text-sm mt-1 absolute">{errors["exchangeModel"]}</p>
              )}
            </div>

            {/* Year and Kms Driven */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
              <div className="space-y-1 sm:space-y-1.5">
                <label className="text-[#e3e3e3] text-lg sm:text-xl md:text-2xl font-light">
                  Year
                  {isFieldRequired("exchangeYear") && <RequiredMarker />}
                </label>
                <div className="relative mb-6">
                  <select
                    className={`h-[45px] sm:h-[50px] md:h-[60px] w-full px-3 sm:px-4 rounded-lg border bg-transparent text-[#e3e3e3] text-sm sm:text-base placeholder:text-[#818181] focus:outline-none focus:border-[#640064] appearance-none custom-select ${
                      shouldShowError("exchangeYear") ? "border-red-500" : "border-[#333333]"
                    }`}
                    name="exchangeYear"
                    value={exchangeYear || ""}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  >
                    <option value="" disabled>Select Year</option>
                    <option value="2024">2024</option>
                    <option value="2023">2023</option>
                    <option value="2022">2022</option>
                    <option value="2021">2021</option>
                    <option value="2020">2020</option>
                  </select>
                </div>
                {shouldShowError("exchangeYear") && (
                  <p className="text-red-500 text-sm mt-1 absolute">{errors["exchangeYear"]}</p>
                )}
              </div>

              <div className="space-y-1 sm:space-y-1.5">
                <label className="text-[#e3e3e3] text-lg sm:text-xl md:text-2xl font-light">
                  Kms Driven
                  {isFieldRequired("exchangeKmsDriven") && <RequiredMarker />}
                </label>
                <Input
                  className={`h-[45px] sm:h-[50px] md:h-[60px] w-full px-3 sm:px-4 rounded-lg border bg-transparent text-[#e3e3e3] text-sm sm:text-base placeholder:text-[#818181] focus:outline-none focus:border-[#640064] mb-6 ${
                    shouldShowError("exchangeKmsDriven") ? "border-red-500" : "border-[#333333]"
                  }`}
                  placeholder="Enter Kms Driven"
                  type="number"
                  name="exchangeKmsDriven"
                  value={exchangeKmsDriven || ""}
                  onChange={handleChange}
                  onBlur={handleBlur}
                />
                {shouldShowError("exchangeKmsDriven") && (
                  <p className="text-red-500 text-sm mt-1 absolute">{errors["exchangeKmsDriven"]}</p>
                )}
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};
