{"version": "1.0.0", "type": "module", "name": "quant5x-forms", "description": "QUANT5X FORMS", "scripts": {"dev": "vite", "build": "vite build && node copy-redirects.js", "vercel-build": "vite build", "serve": "node server.js", "start": "npm run serve"}, "dependencies": {"@radix-ui/react-slot": "^1.1.0", "@reduxjs/toolkit": "^2.7.0", "@tailwindcss/postcss": "^4.1.4", "@types/react-google-recaptcha": "^2.1.9", "autoprefixer": "^10.4.21", "axios": "^1.6.2", "class-variance-authority": "^0.7.0", "clsx": "2.1.1", "dotenv": "^16.3.1", "express": "^4.21.2", "lucide-react": "^0.453.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-google-recaptcha": "^3.1.0", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-router-dom": "^6.8.1", "tailwind-merge": "2.5.4"}, "devDependencies": {"@types/react": "^18.2.25", "@types/react-dom": "^18.2.10", "@types/react-redux": "^7.1.34", "@vitejs/plugin-react": "4.3.4", "esbuild": "0.24.0", "globals": "15.12.0", "tailwindcss": "3.4.16", "vite": "6.0.4"}, "alias": {"@/*": "./src/components/ui/$1"}}