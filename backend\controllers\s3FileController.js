/**
 * S3 File Controller - Cloud Storage Implementation
 * Replaces local file storage with Amazon S3
 */

import Vehicle from '../models/Vehicle.js';
import multer from 'multer';
import s3Service from '../services/s3Service.js';
import logger from '../utils/logger.js';

/**
 * Helper function to get customer phone from vehicle ID
 */
const getCustomerPhoneFromVehicle = async (vehicleId) => {
  try {
    const vehicle = await Vehicle.findById(vehicleId);
    if (!vehicle || !vehicle.customer?.phone) {
      throw new Error('Customer phone number not found');
    }
    return vehicle.customer.phone;
  } catch (error) {
    logger.error(`Error getting customer phone for vehicle ${vehicleId}:`, error);
    throw error;
  }
};

// Configure multer for memory storage (files will be uploaded to S3)
const storage = multer.memoryStorage();

// Multer configuration for S3 uploads
export const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
    files: 5 // Maximum 5 files per request
  },
  fileFilter: (req, file, cb) => {
    // Explicitly reject video files first
    const videoMimeTypes = [
      'video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/flv',
      'video/webm', 'video/mkv', 'video/3gp', 'video/m4v'
    ];

    const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv', '.3gp', '.m4v'];

    // Check if it's a video file
    const fileExtension = `.${file.originalname.split('.').pop().toLowerCase()}`;
    const isVideoFile = videoMimeTypes.includes(file.mimetype) || videoExtensions.includes(fileExtension);

    if (isVideoFile) {
      return cb(new Error('Video files are not allowed. Please upload documents only (PDF, JPG, PNG, DOC, DOCX).'));
    }

    // Accept only document and image file types
    const allowedTypes = {
      'application/pdf': ['.pdf'],
      'image/jpeg': ['.jpg', '.jpeg'],
      'image/png': ['.png'],
      'image/gif': ['.gif'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx']
    };

    const isValidType = allowedTypes[file.mimetype]?.includes(fileExtension);

    if (isValidType) {
      cb(null, true);
    } else {
      cb(new Error(`Invalid file type "${file.mimetype}". Only documents are allowed: PDF, JPG, PNG, DOC, DOCX.`));
    }
  }
});

/**
 * S3 File upload handler - Single API call for all files
 */
export const uploadFiles = async (req, res) => {
  try {
    const vehicleId = req.params.id;
    logger.info(`Processing S3 file upload for vehicle: ${vehicleId}`);

    // Get vehicle record
    const vehicle = await Vehicle.findById(vehicleId);
    if (!vehicle) {
      return res.status(404).json({
        success: false,
        message: 'Vehicle not found'
      });
    }

    // Get customer phone for S3 folder structure
    const customerPhone = vehicle.customer?.phone;
    if (!customerPhone) {
      return res.status(400).json({
        success: false,
        message: 'Customer phone number not found'
      });
    }

    // Initialize S3 service
    const s3Initialized = await s3Service.initialize();
    if (!s3Initialized) {
      return res.status(500).json({
        success: false,
        message: 'Failed to initialize S3 service'
      });
    }

    // Define allowed document fields
    const allowedFields = ['aadhar', 'pan', 'photo', 'license', 'other'];
    const uploadResults = [];

    // Process each uploaded file
    for (const fieldName of allowedFields) {
      const files = req.files[fieldName];
      if (files && files.length > 0) {
        const file = files[0]; // Take first file only

        try {
          // Validate file
          const validation = s3Service.validateFile(file.buffer, file.mimetype);
          if (!validation.isValid) {
            logger.warn(`File validation failed for ${fieldName}:`, {
              fileName: file.originalname,
              mimeType: file.mimetype,
              errors: validation.errors
            });

            uploadResults.push({
              fieldName,
              fileName: file.originalname,
              status: 'error',
              errors: validation.errors
            });
            continue;
          }

          // Upload to S3
          const uploadResult = await s3Service.uploadFile(
            file.buffer,
            customerPhone,
            fieldName,
            file.originalname,
            file.mimetype
          );

          // Create file record for database
          const fileRecord = {
            // Basic file info
            fileName: file.originalname,
            fileType: file.mimetype,
            fileSize: file.size,

            // S3 storage info
            s3Key: uploadResult.s3Key,
            s3Location: uploadResult.location,
            s3ETag: uploadResult.etag,

            // Status flags
            uploadDate: new Date(),
            storageType: 's3',
            fileSelected: true
          };

          // Store in vehicle document
          vehicle.documents[fieldName] = fileRecord;

          uploadResults.push({
            fieldName,
            fileName: file.originalname,
            fileSize: file.size,
            s3Key: uploadResult.s3Key,
            status: 'success'
          });

          logger.info(`Successfully uploaded ${fieldName} to S3: ${uploadResult.s3Key}`);

        } catch (error) {
          logger.error(`Error uploading ${fieldName}:`, error);
          uploadResults.push({
            fieldName,
            fileName: file.originalname,
            status: 'error',
            error: error.message
          });
        }
      }
    }

    // Save vehicle with updated document info
    await vehicle.save();

    const successCount = uploadResults.filter(r => r.status === 'success').length;
    const errorCount = uploadResults.filter(r => r.status === 'error').length;

    logger.info(`S3 upload completed for vehicle ${vehicleId}. Success: ${successCount}, Errors: ${errorCount}`);

    res.json({
      success: successCount > 0,
      message: `Upload completed. ${successCount} files uploaded successfully${errorCount > 0 ? `, ${errorCount} failed` : ''}`,
      data: {
        vehicleId: vehicleId,
        customerPhone: customerPhone,
        uploadResults: uploadResults,
        summary: {
          total: uploadResults.length,
          successful: successCount,
          failed: errorCount
        }
      }
    });

  } catch (error) {
    logger.error('S3 file upload error:', error);
    res.status(500).json({
      success: false,
      message: 'File upload failed',
      error: error.message
    });
  }
};
