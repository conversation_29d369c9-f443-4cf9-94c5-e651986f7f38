import express from 'express';
import {
  csvExportController,
  csvBulkExportController,
  csvStatsController
} from '../middleware/csvMiddleware.js';
import { protect } from '../middleware/auth.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const router = express.Router();

/**
 * CSV Export Routes
 * All routes require authentication
 */

// Apply authentication middleware to all routes
router.use(protect);

/**
 * @route   POST /api/csv/export/:vehicleId
 * @desc    Export single vehicle to CSV
 * @access  Private (Admin only)
 */
router.post('/export/:vehicleId', csvExportController);

/**
 * @route   POST /api/csv/bulk-export
 * @desc    Bulk export vehicles to CSV
 * @access  Private (Admin only)
 * @query   startDate, endDate, status, limit
 */
router.post('/bulk-export', csvBulkExportController);

/**
 * @route   GET /api/csv/stats
 * @desc    Get CSV processing statistics
 * @access  Private (Admin only)
 */
router.get('/stats', csvStatsController);

/**
 * @route   GET /api/csv/files
 * @desc    List available CSV files
 * @access  Private (Admin only)
 */
router.get('/files', async (req, res) => {
  try {
    const csvDir = path.join(path.dirname(__dirname), 'csv-exports');

    if (!fs.existsSync(csvDir)) {
      return res.json({
        success: true,
        data: {
          files: [],
          directory: csvDir,
          message: 'CSV directory does not exist'
        }
      });
    }

    const files = fs.readdirSync(csvDir, { withFileTypes: true });
    const csvFiles = files
      .filter(file => file.isFile() && file.name.endsWith('.csv'))
      .map(file => {
        const filePath = path.join(csvDir, file.name);
        const stats = fs.statSync(filePath);
        return {
          name: file.name,
          size: stats.size,
          created: stats.birthtime,
          modified: stats.mtime,
          type: 'monthly'
        };
      })
      .sort((a, b) => b.modified - a.modified);

    res.json({
      success: true,
      data: {
        files: csvFiles,
        directory: csvDir,
        totalFiles: csvFiles.length
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to list CSV files',
      error: error.message
    });
  }
});

/**
 * @route   GET /api/csv/download/:filename
 * @desc    Download CSV file
 * @access  Private (Admin only)
 */
router.get('/download/:filename', async (req, res) => {
  try {
    const { filename } = req.params;

    // Validate filename to prevent directory traversal
    if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
      return res.status(400).json({
        success: false,
        message: 'Invalid filename'
      });
    }

    if (!filename.endsWith('.csv')) {
      return res.status(400).json({
        success: false,
        message: 'File must be a CSV file'
      });
    }

    const csvDir = path.join(path.dirname(__dirname), 'csv-exports');
    const filePath = path.join(csvDir, filename);

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        message: 'File not found'
      });
    }

    const stats = fs.statSync(filePath);

    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Length', stats.size);

    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to download CSV file',
      error: error.message
    });
  }
});

/**
 * @route   DELETE /api/csv/files/:filename
 * @desc    Delete CSV file
 * @access  Private (Admin only)
 */
router.delete('/files/:filename', async (req, res) => {
  try {
    const { filename } = req.params;

    // Validate filename
    if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
      return res.status(400).json({
        success: false,
        message: 'Invalid filename'
      });
    }

    const csvDir = path.join(path.dirname(__dirname), 'csv-exports');
    const filePath = path.join(csvDir, filename);

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        message: 'File not found'
      });
    }

    fs.unlinkSync(filePath);

    res.json({
      success: true,
      message: `File ${filename} deleted successfully`
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to delete CSV file',
      error: error.message
    });
  }
});

/**
 * @route   GET /api/csv/preview/:filename
 * @desc    Preview CSV file content (first 100 rows)
 * @access  Private (Admin only)
 */
router.get('/preview/:filename', async (req, res) => {
  try {
    const { filename } = req.params;
    const { rows = 100 } = req.query;

    // Validate filename
    if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
      return res.status(400).json({
        success: false,
        message: 'Invalid filename'
      });
    }

    const csvDir = path.join(path.dirname(__dirname), 'csv-exports');
    const filePath = path.join(csvDir, filename);

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        message: 'File not found'
      });
    }

    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    const maxRows = Math.min(parseInt(rows), lines.length);
    const preview = lines.slice(0, maxRows).join('\n');

    res.json({
      success: true,
      data: {
        filename,
        totalRows: lines.length - 1, // Subtract header row
        previewRows: maxRows - 1,
        content: preview,
        truncated: lines.length > maxRows
      }
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to preview CSV file',
      error: error.message
    });
  }
});

export default router;
