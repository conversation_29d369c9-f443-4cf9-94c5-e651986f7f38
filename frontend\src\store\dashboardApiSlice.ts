import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import config from '../config/config';

// Define types for the dashboard data
interface DashboardStats {
  totalCustomers: number;
  totalVehicles: number;
  pendingVehicles: number;
  completedVehicles: number;
  vehiclesByModel: {
    model: string;
    count: number;
  }[];
  recentCustomers: {
    _id: string;
    name: string;
    phone: string;
    email: string;
    altPhone?: string;
    address?: {
      houseNo?: string;
      street?: string;
      city?: string;
      state?: string;
      pincode?: string;
      fullAddress?: string;
    } | string;
    createdAt: string;
  }[];
  recentVehicles: {
    _id: string;
    customer: {
      _id?: string;
      name: string;
    };
    vehicleDetails: {
      model: string;
      colour: string;
      financeMode?: string;
    };
    exchangeVehicle?: {
      exchangeModel?: string;
      exchangeYear?: string;
      exchangeKmsDriven?: string;
    };
    registration?: {
      hasSpecialNumber?: string;
      expectedSpecialNumber?: string;
      registrationType?: string;
    };
    insurance?: {
      insuranceType?: string;
      nomineeName?: string;
      nomineeAge?: string;
      nomineeRelationship?: string;
    };
    documents?: {
      aadhar?: {
        fileName?: string;
        fileType?: string;
        fileSize?: number;
      };
      pan?: {
        fileName?: string;
        fileType?: string;
        fileSize?: number;
      };
    };
    status: string;
    createdAt: string;
  }[];
}

interface VehicleStatusDistribution {
  status: string;
  count: number;
}

interface MonthlyRegistration {
  month: number;
  count: number;
}

interface CleanupStats {
  pendingVehiclesOlderThan7Days: number;
  totalPendingVehicles: number;
  totalCompletedVehicles: number;
  totalCustomers: number;
  cutoffDate: string;
  retentionDays: number;
  isServiceRunning: boolean;
}

// Create the API slice
export const dashboardApiSlice = createApi({
  reducerPath: 'dashboardApi',
  baseQuery: fetchBaseQuery({
    baseUrl: `${config.api.baseUrl}/dashboard`,
    // Add timeout to prevent hanging requests
    timeout: config.api.timeout,
    // Include credentials for cookies
    credentials: 'include',
    prepareHeaders: (headers) => {
      // Get token from localStorage
      const token = localStorage.getItem(config.auth.tokenKey);
      // If token exists, add authorization header
      if (token) {
        headers.set('Authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  endpoints: (builder) => ({
    getDashboardStats: builder.query<DashboardStats, void>({
      query: () => ({
        url: '/stats',
        // Add cache-busting parameter to prevent caching
        params: { _t: Date.now() },
      }),
      transformResponse: (response: { success: boolean; data: DashboardStats }) => {
        // Check if the response has the expected structure
        if (response && response.success && response.data) {
          return response.data;
        }
        // If not, throw an error that will be caught by RTK Query
        throw new Error('Invalid response format from server');
      },
      // Add retry logic
      extraOptions: {
        maxRetries: 3,
      },
      // Disable caching completely
      keepUnusedDataFor: 0,
      // Force refetch on every call
      forceRefetch({ currentArg, previousArg }) {
        return true;
      },
    }),
    getVehicleStatusDistribution: builder.query<VehicleStatusDistribution[], void>({
      query: () => ({
        url: '/vehicle-status',
        params: { _t: Date.now() },
      }),
      transformResponse: (response: { success: boolean; data: VehicleStatusDistribution[] }) => response.data,
      keepUnusedDataFor: 0,
    }),
    getMonthlyRegistrations: builder.query<MonthlyRegistration[], void>({
      query: () => ({
        url: '/monthly-registrations',
        params: { _t: Date.now() },
      }),
      transformResponse: (response: { success: boolean; data: MonthlyRegistration[] }) => response.data,
      keepUnusedDataFor: 0,
    }),
    getCleanupStats: builder.query<CleanupStats, void>({
      query: () => ({
        url: `${config.api.baseUrl}/admin/cleanup/stats`,
        params: { _t: Date.now() },
      }),
      transformResponse: (response: { success: boolean; stats: CleanupStats }) => response.stats,
      keepUnusedDataFor: 0,
    }),
  }),
});

// Export the auto-generated hooks
export const {
  useGetDashboardStatsQuery,
  useGetVehicleStatusDistributionQuery,
  useGetMonthlyRegistrationsQuery,
  useGetCleanupStatsQuery,
} = dashboardApiSlice;
