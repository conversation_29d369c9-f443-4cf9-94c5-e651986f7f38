import express from 'express';
import logger from '../utils/logger.js';
import {
  createVehicle,
  getAllVehicles,
  getVehicleById,
  getVehicleByCustomerId,
  updateVehicle,
  deleteVehicle,
  updateCustomerInfo,
  updateVehicleDetails,
  updateExchangeVehicle,
  updateRegistration,
  updateInsurance,
  uploadDocuments,
  checkDuplicate
} from '../controllers/vehicleController.js';

// Import S3 file controllers (production-ready cloud storage)
import { upload, uploadFiles } from '../controllers/s3FileController.js';
import { serveFile, getFileInfo, deleteFile } from '../controllers/s3FileServeController.js';
import {
  csvCreateMiddleware,
  csvUpdateMiddleware,
  csvCompleteMiddleware
} from '../middleware/csvMiddleware.js';

const router = express.Router();

// Production uses S3 cloud storage exclusively

router.post('/check-duplicate', checkDuplicate);

router.post('/', csvCreateMiddleware, createVehicle);
router.get('/', getAllVehicles);
router.get('/customer/:customerId', getVehicleByCustomerId);
router.get('/:id', getVehicleById);
router.put('/:id', csvCompleteMiddleware, updateVehicle);
router.delete('/:id', deleteVehicle);

router.put('/:id/customer', csvUpdateMiddleware, updateCustomerInfo);
router.put('/:id/vehicle-details', csvUpdateMiddleware, updateVehicleDetails);
router.put('/:id/exchange-vehicle', csvUpdateMiddleware, updateExchangeVehicle);
router.put('/:id/registration', csvUpdateMiddleware, updateRegistration);
router.put('/:id/insurance', csvUpdateMiddleware, updateInsurance);
router.put('/:id/documents', csvUpdateMiddleware, uploadDocuments);

router.put('/:id/documents/files', upload.fields([
  { name: 'aadhar', maxCount: 1 },
  { name: 'pan', maxCount: 1 },
  { name: 'photo', maxCount: 1 },
  { name: 'license', maxCount: 1 },
  { name: 'other', maxCount: 1 }
]), csvUpdateMiddleware, uploadFiles);

router.get('/:id/documents/:documentType/file', serveFile);
router.get('/:id/documents/:documentType/info', getFileInfo);

// S3 file deletion route
router.delete('/:id/documents/:documentType/file', deleteFile);

export default router;
