import React, { useState, useCallback, useEffect } from 'react';
import <PERSON><PERSON><PERSON><PERSON><PERSON> from 'react-google-recaptcha';
import { Shield, CheckCircle, AlertCircle, RefreshCw } from 'lucide-react';

// TypeScript declaration for reCAPTCHA v3
declare global {
  interface Window {
    grecaptcha: {
      ready: (callback: () => void) => void;
      execute: (siteKey: string, options: { action: string }) => Promise<string>;
    };
  }
}

interface ReCaptchaGateProps {
  onVerified: () => void;
  title?: string;
  description?: string;
}

const ReCaptchaGate: React.FC<ReCaptchaGateProps> = ({
  onVerified,
  title = "Security Verification Required",
  description = "Please complete the security verification to access the vehicle registration form."
}) => {
  const [isVerified, setIsVerified] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [recaptchaRef, setRecaptchaRef] = useState<ReCAPTCHA | null>(null);
  const [useV2Fallback, setUseV2Fallback] = useState(false); // Disabled for v3-only keys
  const [v3ScriptLoaded, setV3ScriptLoaded] = useState(false);
  const [retryAttempt, setRetryAttempt] = useState(0);

  // Get site key from environment
  const siteKey = import.meta.env.VITE_RECAPTCHA_SITE_KEY;

  // Debug logging (development only)
  useEffect(() => {
    if (import.meta.env.DEV) {
      console.log('reCAPTCHA Debug Info:', {
        siteKey: siteKey ? `${siteKey.substring(0, 10)}...` : 'NOT SET',
        environment: import.meta.env.NODE_ENV,
        domain: window.location.hostname,
        useV2Fallback
      });
    }
  }, [siteKey, useV2Fallback]);

  // Load reCAPTCHA v3 script
  useEffect(() => {
    if (!siteKey || useV2Fallback) return;

    const loadV3Script = () => {
      // Check if script already exists
      if (document.querySelector(`script[src*="recaptcha/api.js?render=${siteKey}"]`)) {
        setV3ScriptLoaded(true);
        return;
      }

      const script = document.createElement('script');
      script.src = `https://www.google.com/recaptcha/api.js?render=${siteKey}`;
      script.async = true;
      script.defer = true;

      script.onload = () => {
        console.log('reCAPTCHA v3 script loaded successfully');
        // Add a small delay to ensure grecaptcha is fully initialized
        setTimeout(() => {
          setV3ScriptLoaded(true);
        }, 200);
      };

      script.onerror = () => {
        console.error('Failed to load reCAPTCHA v3 script');
        setError('reCAPTCHA v3 failed to load. Please check your internet connection and refresh the page.');
      };

      document.head.appendChild(script);
    };

    loadV3Script();
  }, [siteKey, useV2Fallback]);

  // Execute reCAPTCHA v3 verification with retry logic
  const executeV3Verification = useCallback(async (retryCount = 0) => {
    const maxRetries = 3;
    const retryDelay = 1000; // 1 second

    // Check if script is loaded
    if (!v3ScriptLoaded) {
      console.error('reCAPTCHA v3 script not loaded');
      setError('reCAPTCHA v3 is not ready. Please refresh the page and try again.');
      setIsLoading(false);
      return;
    }

    // Check if grecaptcha is available with retry logic
    if (!window.grecaptcha) {
      if (retryCount < maxRetries) {
        console.log(`reCAPTCHA v3 not ready, retrying in ${retryDelay}ms (attempt ${retryCount + 1}/${maxRetries})`);
        setRetryAttempt(retryCount + 1);
        setTimeout(() => {
          executeV3Verification(retryCount + 1);
        }, retryDelay);
        return;
      } else {
        console.error('reCAPTCHA v3 not available after retries');
        setError('reCAPTCHA v3 is not ready. Please refresh the page and try again.');
        setIsLoading(false);
        setRetryAttempt(0);
        return;
      }
    }

    try {
      setIsLoading(true);
      setError(null);

      await window.grecaptcha.ready(async () => {
        try {
          const token = await window.grecaptcha.execute(siteKey, { action: 'form_access' });
          console.log('reCAPTCHA v3 token generated');
          setRetryAttempt(0); // Reset retry count on success
          await verifyToken(token);
        } catch (error) {
          console.error('reCAPTCHA v3 execution failed:', error);
          setError('reCAPTCHA v3 verification failed. Please refresh the page and try again.');
          setIsLoading(false);
        }
      });
    } catch (error) {
      console.error('reCAPTCHA v3 error:', error);
      setError('reCAPTCHA v3 failed. Please refresh the page and try again.');
      setIsLoading(false);
    }
  }, [siteKey, v3ScriptLoaded]);

  // Verify token with backend
  const verifyToken = async (token: string) => {
    try {
      // Get backend URL from environment or use default
      const backendUrl = import.meta.env.VITE_BACKEND_URL ||
                        (import.meta.env.NODE_ENV === 'production'
                          ? 'https://backend-3xqm.onrender.com'
                          : 'http://localhost:5000');

      // Verify token with backend
      const response = await fetch(`${backendUrl}/api/verify-recaptcha`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ recaptchaToken: token }),
      });

      const result = await response.json();

      if (result.success) {
        setIsVerified(true);
        setError(null);
        // Wait a moment to show success state, then proceed
        setTimeout(() => {
          onVerified();
        }, 1000);
      } else {
        setError(result.message || 'Verification failed. Please try again.');
        setIsVerified(false);
        // Reset reCAPTCHA
        if (recaptchaRef) {
          recaptchaRef.reset();
        }
      }
    } catch (err) {
      console.error('reCAPTCHA verification error:', err);
      setError('Network error. Please check your connection and try again.');
      setIsVerified(false);
      // Reset reCAPTCHA
      if (recaptchaRef) {
        recaptchaRef.reset();
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Handle reCAPTCHA v2 verification (fallback)
  const handleRecaptchaChange = useCallback(async (token: string | null) => {
    if (!token) {
      setError('reCAPTCHA verification failed. Please try again.');
      setIsVerified(false);
      return;
    }

    setIsLoading(true);
    setError(null);
    await verifyToken(token);
  }, []);

  // Handle reCAPTCHA error
  const handleRecaptchaError = useCallback(() => {
    console.error('reCAPTCHA v2 error occurred - likely due to v3-only key');
    setError('reCAPTCHA v2 verification failed. The current keys are configured for v3 only. Please configure v2-compatible keys or use v3-only mode.');
    setIsVerified(false);
  }, []);

  // Handle reCAPTCHA expiration
  const handleRecaptchaExpired = useCallback(() => {
    setError('reCAPTCHA verification expired. Please verify again.');
    setIsVerified(false);
  }, []);

  // Reset function
  const handleReset = useCallback(() => {
    setError(null);
    setIsVerified(false);
    setIsLoading(false);
    setRetryAttempt(0);
    if (v3ScriptLoaded) {
      // Retry v3 verification with a small delay
      setTimeout(() => {
        executeV3Verification(0); // Reset retry count
      }, 300);
    } else {
      // Reload the page to restart v3 script loading
      window.location.reload();
    }
  }, [v3ScriptLoaded, executeV3Verification]);

  // Auto-execute v3 verification when script is loaded with delay
  useEffect(() => {
    if (v3ScriptLoaded && !useV2Fallback && !isVerified && !isLoading) {
      console.log('Auto-executing reCAPTCHA v3 verification with delay');
      // Add a small delay to ensure grecaptcha is fully initialized
      const timer = setTimeout(() => {
        executeV3Verification();
      }, 500); // 500ms delay

      return () => clearTimeout(timer);
    }
  }, [v3ScriptLoaded, useV2Fallback, isVerified, isLoading, executeV3Verification]);

  // Don't render if no site key
  if (!siteKey) {
    return (
      <div className="min-h-screen bg-[#0a0a0a] flex items-center justify-center p-4">
        <div className="bg-[#1a1a1a] border border-red-500 rounded-lg p-8 max-w-md w-full text-center">
          <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-bold text-red-500 mb-2">Configuration Error</h2>
          <p className="text-gray-400">reCAPTCHA is not properly configured. Please contact support.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#0a0a0a] flex items-center justify-center p-4">
      <div className="bg-[#1a1a1a] border border-[#333333] rounded-lg p-8 max-w-md w-full">
        {/* Header */}
        <div className="text-center mb-6">
          <div className="flex justify-center mb-4">
            {isVerified ? (
              <CheckCircle className="w-16 h-16 text-green-500" />
            ) : (
              <Shield className="w-16 h-16 text-blue-500" />
            )}
          </div>
          <h1 className="text-2xl font-bold text-[#e3e3e3] mb-2">{title}</h1>
          <p className="text-gray-400 text-sm">{description}</p>
        </div>

        {/* Success State */}
        {isVerified && (
          <div className="text-center mb-6">
            <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4 mb-4">
              <p className="text-green-400 font-medium">✅ Verification Successful!</p>
              <p className="text-green-300 text-sm mt-1">Redirecting to form...</p>
            </div>
            <div className="flex justify-center">
              <RefreshCw className="w-6 h-6 text-green-500 animate-spin" />
            </div>
          </div>
        )}

        {/* reCAPTCHA v3 Widget */}
        {!isVerified && (
          <div className="mb-6">
            <div className="text-center">
              <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-6 mb-4">
                <div className="flex justify-center items-center text-blue-400 mb-3">
                  <RefreshCw className="w-6 h-6 animate-spin mr-3" />
                  <span className="text-lg">
                    {!v3ScriptLoaded ? 'Loading reCAPTCHA v3...' :
                     isLoading ? (retryAttempt > 0 ? `Retrying verification... (${retryAttempt}/3)` : 'Verifying automatically...') : 'Preparing verification...'}
                  </span>
                </div>
                <p className="text-blue-300 text-sm">
                  Using advanced reCAPTCHA v3 for seamless verification
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div className="mb-6">
            <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
              <div className="flex items-start">
                <AlertCircle className="w-5 h-5 text-red-500 mr-2 mt-0.5 flex-shrink-0" />
                <div className="flex-1">
                  <p className="text-red-400 text-sm">{error}</p>
                  <button
                    onClick={handleReset}
                    className="mt-2 text-red-300 hover:text-red-100 text-sm underline focus:outline-none"
                  >
                    Try Again
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="text-center">
          <p className="text-gray-500 text-xs">
            Using advanced reCAPTCHA v3 for seamless protection against automated submissions
          </p>
        </div>
      </div>
    </div>
  );
};

export default ReCaptchaGate;
