/**
 * Security configuration for the frontend application
 * Defines security policies and validation rules
 */

// Environment detection
const isProduction = import.meta.env.PROD;
const isDevelopment = import.meta.env.DEV;

/**
 * Security configuration object
 */
export const securityConfig = {
  // Environment settings
  environment: {
    isProduction,
    isDevelopment,
    allowDebugLogs: isDevelopment,
    allowConsoleAccess: isDevelopment
  },

  // Authentication settings
  auth: {
    tokenExpiry: 24 * 60 * 60 * 1000, // 24 hours in milliseconds
    refreshThreshold: 5 * 60 * 1000, // Refresh token 5 minutes before expiry
    maxLoginAttempts: 5,
    lockoutDuration: 15 * 60 * 1000, // 15 minutes
    sessionTimeout: 30 * 60 * 1000, // 30 minutes of inactivity
  },

  // Data validation rules
  validation: {
    // Phone number validation
    phone: {
      minLength: 10,
      maxLength: 15,
      pattern: /^[0-9+\-\s()]+$/
    },
    
    // Email validation
    email: {
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      maxLength: 254
    },
    
    // Name validation
    name: {
      minLength: 2,
      maxLength: 50,
      pattern: /^[a-zA-Z\s.'-]+$/
    },
    
    // Address validation
    address: {
      houseNo: { maxLength: 20 },
      street: { maxLength: 100 },
      city: { maxLength: 50 },
      pincode: { 
        length: 6,
        pattern: /^[0-9]{6}$/
      }
    }
  },

  // Data sanitization settings
  sanitization: {
    // Fields that should never be logged
    sensitiveFields: [
      'password',
      'token',
      'authorization',
      'auth',
      'phone',
      'email',
      'altPhone',
      'pincode',
      'name',
      'houseNo',
      'street',
      'city',
      'aadhaar',
      'pan',
      'license'
    ],
    
    // Fields that can be partially logged (masked)
    partiallyMaskedFields: [
      'phone',
      'email',
      'pincode'
    ],
    
    // Fields that should be completely removed from logs
    completelyHiddenFields: [
      'password',
      'token',
      'authorization',
      'aadhaar',
      'pan'
    ]
  },

  // API security settings
  api: {
    timeout: 15000, // 15 seconds
    retryAttempts: 3,
    retryDelay: 1000, // 1 second
    
    // Headers that should always be included
    securityHeaders: {
      'X-Requested-With': 'XMLHttpRequest',
      'Cache-Control': 'no-cache',
      'Pragma': 'no-cache'
    }
  },

  // Content Security Policy settings
  csp: {
    // Allowed sources for different content types
    defaultSrc: ["'self'"],
    scriptSrc: ["'self'", "'unsafe-inline'"],
    styleSrc: ["'self'", "'unsafe-inline'"],
    imgSrc: ["'self'", "data:", "https:"],
    connectSrc: ["'self'"],
    fontSrc: ["'self'"],
    objectSrc: ["'none'"],
    mediaSrc: ["'self'"],
    frameSrc: ["'none'"]
  },

  // Error handling settings
  errorHandling: {
    // Whether to show detailed error messages
    showDetailedErrors: isDevelopment,
    
    // Whether to log errors to external service
    logToExternalService: isProduction,
    
    // Maximum error message length to display
    maxErrorMessageLength: 200,
    
    // Generic error messages for production
    genericMessages: {
      network: 'Network error occurred. Please try again.',
      authentication: 'Authentication failed. Please login again.',
      authorization: 'You do not have permission to perform this action.',
      validation: 'Please check your input and try again.',
      server: 'Server error occurred. Please try again later.',
      unknown: 'An unexpected error occurred. Please try again.'
    }
  },

  // Rate limiting (client-side)
  rateLimiting: {
    // Maximum requests per minute for different operations
    api: {
      default: 60,
      auth: 10,
      upload: 5,
      search: 30
    },
    
    // Time window for rate limiting (in milliseconds)
    window: 60 * 1000 // 1 minute
  },

  // Local storage security
  storage: {
    // Encryption key for sensitive data (in production, this should come from env)
    encryptionKey: isProduction ? import.meta.env.VITE_STORAGE_KEY : 'dev-key-not-secure',
    
    // Items that should be encrypted in localStorage
    encryptedItems: [
      'authToken',
      'userInfo',
      'formData'
    ],
    
    // Maximum age for stored items (in milliseconds)
    maxAge: {
      authToken: 24 * 60 * 60 * 1000, // 24 hours
      formData: 7 * 24 * 60 * 60 * 1000, // 7 days
      userPreferences: 30 * 24 * 60 * 60 * 1000 // 30 days
    }
  }
};

/**
 * Validate if current environment is secure
 */
export const isSecureEnvironment = (): boolean => {
  return isProduction || (isDevelopment && window.location.protocol === 'https:');
};

/**
 * Get security headers for API requests
 */
export const getSecurityHeaders = (): Record<string, string> => {
  return {
    ...securityConfig.api.securityHeaders,
    ...(isProduction && {
      'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block'
    })
  };
};

/**
 * Validate input against security rules
 */
export const validateInput = (field: string, value: string): { isValid: boolean; error?: string } => {
  const rules = securityConfig.validation;
  
  switch (field) {
    case 'phone':
      if (value.length < rules.phone.minLength || value.length > rules.phone.maxLength) {
        return { isValid: false, error: `Phone number must be between ${rules.phone.minLength} and ${rules.phone.maxLength} characters` };
      }
      if (!rules.phone.pattern.test(value)) {
        return { isValid: false, error: 'Invalid phone number format' };
      }
      break;
      
    case 'email':
      if (!rules.email.pattern.test(value)) {
        return { isValid: false, error: 'Invalid email format' };
      }
      if (value.length > rules.email.maxLength) {
        return { isValid: false, error: `Email must be less than ${rules.email.maxLength} characters` };
      }
      break;
      
    case 'name':
      if (value.length < rules.name.minLength || value.length > rules.name.maxLength) {
        return { isValid: false, error: `Name must be between ${rules.name.minLength} and ${rules.name.maxLength} characters` };
      }
      if (!rules.name.pattern.test(value)) {
        return { isValid: false, error: 'Name contains invalid characters' };
      }
      break;
      
    case 'pincode':
      if (!rules.address.pincode.pattern.test(value)) {
        return { isValid: false, error: 'Pincode must be 6 digits' };
      }
      break;
  }
  
  return { isValid: true };
};

export default securityConfig;
