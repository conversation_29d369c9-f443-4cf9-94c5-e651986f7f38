// screens/Dashboard/Modal.tsx
import { ReactNode, useEffect, useState } from "react";
import { createPortal } from "react-dom";
import "../../styles/modal.css";

interface ModalProps {
    children: ReactNode;
    onClose: () => void;
    title?: string;
}

const Modal = ({ children, onClose, title = "Details" }: ModalProps) => {
    const [modalRoot, setModalRoot] = useState<HTMLElement | null>(null);
    const [isClosing, setIsClosing] = useState(false);
    const [isOpening, setIsOpening] = useState(true);

    useEffect(() => {
        // Find or create the modal root element
        let root = document.getElementById("modal-root");
        if (!root) {
            root = document.createElement("div");
            root.id = "modal-root";
            document.body.appendChild(root);
        }
        setModalRoot(root);

        // Prevent body scrolling when modal is open
        document.body.style.overflow = "hidden";

        // Set opening animation
        const timer = setTimeout(() => {
            setIsOpening(false);
        }, 50); // Small delay to ensure the initial state is rendered first

        // Cleanup function
        return () => {
            // Don't remove the modal root as it might be used by other components
            document.body.style.overflow = "auto";
            clearTimeout(timer);
        };
    }, []);

    // Handle smooth closing animation
    const handleClose = () => {
        setIsClosing(true);
        setTimeout(() => {
            onClose();
        }, 300); // Match this with the animation duration
    };

    // Don't render anything until we have a modal root
    if (!modalRoot) return null;

    return createPortal(
        <div
            className={`fixed inset-0 z-50 flex items-center justify-center transition-opacity duration-300 ease-in-out ${
                isClosing ? 'opacity-0' : isOpening ? 'opacity-0' : 'opacity-100'
            } bg-black bg-opacity-60 backdrop-blur-sm`}
        >
            <div
                className={`bg-white rounded-2xl w-[95%] max-w-3xl relative shadow-2xl max-h-[90vh] flex flex-col transition-all duration-300 ease-in-out overflow-hidden ${
                    isClosing ? 'opacity-0 scale-95' : isOpening ? 'opacity-0 scale-95' : 'opacity-100 scale-100'
                }`}
            >
                {/* Fixed header with title and close button */}
                <div className="sticky top-0 bg-white z-10 px-6 py-4 border-b flex justify-between items-center">
                    <h2 className="text-lg font-bold">{title}</h2>
                    <button
                        onClick={handleClose}
                        className="text-gray-600 hover:text-black transition-all duration-200 ease-in-out transform hover:scale-110 w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100"
                        aria-label="Close"
                    >
                        <span className="text-xl font-bold">&times;</span>
                    </button>
                </div>

                {/* Scrollable content area with custom scrollbar */}
                <div className="overflow-y-auto flex-1 modal-scrollbar">
                    <div className="px-6 py-4">
                        {children}
                    </div>
                </div>
            </div>
        </div>,
        modalRoot
    );
};

// Helper components for structured content
export const ModalSection = ({
    title,
    children
}: {
    title: string;
    children: ReactNode
}) => {
    return (
        <div className="mb-5">
            <h3 className="text-base font-medium mb-3">{title}</h3>
            <div>
                {children}
            </div>
        </div>
    );
};

export const ModalField = ({
    label,
    value
}: {
    label: string;
    value: string | number | null | undefined
}) => {
    const displayValue = value === null || value === undefined ? "N/A" :
                         value === "" ? "N/A" :
                         value;

    return (
        <div className="flex justify-between py-1.5">
            <span className="text-gray-600">{label}</span>
            <span className="font-normal text-right">{displayValue}</span>
        </div>
    );
};

export const ModalDivider = () => {
    return <hr className="my-4 border-gray-200" />;
};

export default Modal;
