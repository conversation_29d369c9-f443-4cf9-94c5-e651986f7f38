import React, { useEffect, useState } from "react";
import { Input } from "../ui/input";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../store/store";
import { updateFormField, validateSection } from "../../store/formSlice";

export const RegistrationRequirments = () => {
  const dispatch = useDispatch();
  const { formData, errors } = useSelector((state: RootState) => state.form);

  // State for animation
  const [showSpecialNumberField, setShowSpecialNumberField] = useState(formData.hasSpecialNumber === "yes");

  // Validate the form when it loads or when form data changes
  useEffect(() => {
    // Initialize required fields if they don't exist
    if (!formData.hasSpecialNumber) {
      dispatch(updateFormField({
        field: "hasSpecialNumber",
        value: "no"
      }));
    }

    if (!formData.registrationType) {
      dispatch(updateFormField({
        field: "registrationType",
        value: "permanent"
      }));
    }

    // Don't validate on initialization to prevent showing errors before user interaction
  }, [dispatch, formData]);

  // Update animation state when hasSpecialNumber changes
  useEffect(() => {
    if (formData.hasSpecialNumber === "yes") {
      setShowSpecialNumberField(true);
    } else {
      // Small delay before hiding to allow animation to complete
      const timer = setTimeout(() => {
        setShowSpecialNumberField(false);
      }, 300);
      return () => clearTimeout(timer);
    }
  }, [formData.hasSpecialNumber]);

  const handleSplChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    dispatch(updateFormField({
      field: "hasSpecialNumber",
      value
    }));

    // If changing from yes to no, clear the expected special number
    if (value === "no" && formData.expectedSpecialNumber) {
      dispatch(updateFormField({
        field: "expectedSpecialNumber",
        value: ""
      }));
    }

    // Validate the section after the update
    setTimeout(() => {
      dispatch(validateSection("registration"));
    }, 0);
  };

  const handleRegTypeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(updateFormField({
      field: "registrationType",
      value: e.target.value
    }));
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    dispatch(updateFormField({
      field: name,
      value
    }));

    // Validate the section after the update
    setTimeout(() => {
      dispatch(validateSection("registration"));
    }, 0);
  };



  // Required field marker component
  const RequiredMarker = () => (
    <span className="text-red-500 ml-1">*</span>
  );

  return (
    <div className="space-y-4 sm:space-y-5 md:space-y-6">
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 sm:gap-8">
        {/* SPL Number */}
        <div className="space-y-1 sm:space-y-1.5">
          <label className="text-[#e3e3e3] text-lg sm:text-xl md:text-2xl font-light">SPL number</label>
          <div className="flex items-center gap-4 sm:gap-8 h-[45px] sm:h-[50px] md:h-[60px] mt-1 mb-6">
            <label className="inline-flex items-center">
              <input
                type="radio"
                name="spl"
                value="yes"
                checked={formData.hasSpecialNumber === "yes"}
                onChange={handleSplChange}
                className="h-5 w-5 accent-[#640064]"
              />
              <span className="ml-2 sm:ml-3 text-[#e3e3e3] text-lg sm:text-xl">Yes</span>
            </label>
            <label className="inline-flex items-center">
              <input
                type="radio"
                name="spl"
                value="no"
                checked={formData.hasSpecialNumber === "no"}
                onChange={handleSplChange}
                className="h-5 w-5 accent-[#640064]"
              />
              <span className="ml-2 sm:ml-3 text-[#e3e3e3] text-lg sm:text-xl">No</span>
            </label>
            </div>
          </div>

        {/* Registration Type */}
        <div className="space-y-1 sm:space-y-1.5">
          <label className="text-[#e3e3e3] text-lg sm:text-xl md:text-2xl font-light">Registration</label>
          <div className="flex items-center gap-4 sm:gap-8 h-[45px] sm:h-[50px] md:h-[60px] mt-1 mb-6">
            <label className="inline-flex items-center">
              <input
                type="radio"
                name="registration"
                value="permanent"
                checked={formData.registrationType === "permanent"}
                onChange={handleRegTypeChange}
                className="h-5 w-5 accent-[#640064]"
              />
              <span className="ml-2 sm:ml-3 text-[#e3e3e3] text-lg sm:text-xl">Permanent</span>
            </label>
            <label className="inline-flex items-center">
              <input
                type="radio"
                name="registration"
                value="temporary"
                checked={formData.registrationType === "temporary"}
                onChange={handleRegTypeChange}
                className="h-5 w-5 accent-[#640064]"
              />
              <span className="ml-2 sm:ml-3 text-[#e3e3e3] text-lg sm:text-xl">Temporary</span>
            </label>
            </div>
          </div>
        </div>

      {/* Conditional field for expected special number with animation */}
      <div
        className={`space-y-4 sm:space-y-5 md:space-y-6 overflow-hidden transition-all duration-300 ease-in-out ${
          formData.hasSpecialNumber === "yes"
            ? "max-h-[200px] opacity-100 transform translate-y-0"
            : "max-h-0 opacity-0 transform -translate-y-4"
        }`}
      >
        {showSpecialNumberField && (
          <div className="space-y-1 sm:space-y-1.5">
            <label className="text-[#e3e3e3] text-lg sm:text-xl md:text-2xl font-light">
              Expected Special Number
            </label>
            <Input
              type="text"
              name="expectedSpecialNumber"
              className={`w-full h-[45px] sm:h-[50px] md:h-[60px] bg-transparent border rounded-lg px-3 sm:px-4 text-[#e3e3e3] placeholder:text-[#818181] focus:outline-none focus:border-[#640064] mb-6 ${
                errors["expectedSpecialNumber"] ? "border-red-500" : "border-[#333333]"
              }`}
              placeholder="Enter your expected special number"
              value={formData.expectedSpecialNumber || ""}
              onChange={handleInputChange}
            />
            {errors["expectedSpecialNumber"] && (
              <p className="text-red-500 text-sm mt-1 absolute">{errors["expectedSpecialNumber"]}</p>
            )}
          </div>
        )}
      </div>
    </div>
  );
};
