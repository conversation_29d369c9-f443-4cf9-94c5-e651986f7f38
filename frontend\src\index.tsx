import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { BrowserRouter } from "react-router-dom";
import AppRouter from "./screens/AppRouter";
import "./styles/forms.css"; // Ensure this file exists in the 'styles' folder
import { Provider } from "react-redux";
import { store } from "./store/store";
import { AuthProvider } from "./context/AuthContext";

// Get the root element
const rootElement = document.getElementById("app");

// Make sure we have a root element
if (!rootElement) {
  throw new Error("Root element with id 'app' not found");
}

// Create the root
const root = createRoot(rootElement);

// Render the app
root.render(
  <StrictMode>
    <Provider store={store}>
      <BrowserRouter>
        <AuthProvider>
          <AppRouter />
        </AuthProvider>
      </BrowserRouter>
    </Provider>
  </StrictMode>,
);
