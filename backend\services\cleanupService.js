import mongoose from 'mongoose';
import Vehicle from '../models/Vehicle.js';
import Customer from '../models/Customer.js';
import logger from '../utils/logger.js';
import { isMongoConnected } from '../config/db.js';

class CleanupService {
  constructor() {
    this.isRunning = false;
    this.intervalId = null;
    this.CLEANUP_INTERVAL = 24 * 60 * 60 * 1000; // 24 hours
    this.PENDING_RETENTION_DAYS = 7;
  }

  start() {
    if (this.isRunning) {
      return;
    }

    this.isRunning = true;

    // Run cleanup immediately on start
    this.runCleanup();

    // Schedule periodic cleanup
    this.intervalId = setInterval(() => {
      this.runCleanup();
    }, this.CLEANUP_INTERVAL);
  }

  stop() {
    if (!this.isRunning) {
      logger.warn('Cleanup service is not running');
      return;
    }

    logger.info('Stopping cleanup service');
    this.isRunning = false;

    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }

    logger.info('Cleanup service stopped');
  }

  async runCleanup() {
    if (!isMongoConnected()) {
      logger.warn('MongoDB not connected, skipping cleanup');
      return;
    }

    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - this.PENDING_RETENTION_DAYS);

      // Find pending vehicles older than 7 days
      const pendingVehicles = await Vehicle.find({
        status: 'pending',
        createdAt: { $lt: cutoffDate }
      });

      let deletedVehicles = 0;
      let deletedCustomers = 0;

      for (const vehicle of pendingVehicles) {
        try {
          // Get customer ID if exists
          const customerId = vehicle.customerId;

          // Delete the vehicle
          await Vehicle.findByIdAndDelete(vehicle._id);
          deletedVehicles++;
          logger.debug(`Deleted pending vehicle: ${vehicle._id}`);

          // If vehicle had a linked customer, check if customer should be deleted
          if (customerId) {
            const customer = await Customer.findById(customerId);
            if (customer) {
              // Check if customer has any other vehicles
              const remainingVehicles = await Vehicle.find({ customerId: customerId });
              
              if (remainingVehicles.length === 0) {
                // Customer has no vehicles left, delete customer
                await Customer.findByIdAndDelete(customerId);
                deletedCustomers++;
                logger.debug(`Deleted customer with no remaining vehicles: ${customerId}`);
              }
            }
          }

          // Also check for customers created from this vehicle's customer data
          if (vehicle.customer && vehicle.customer.phone) {
            const orphanedCustomer = await Customer.findOne({
              phone: vehicle.customer.phone,
              createdAt: { $lt: cutoffDate }
            });

            if (orphanedCustomer) {
              // Check if this customer has any vehicles
              const customerVehicles = await Vehicle.find({ customerId: orphanedCustomer._id });
              
              if (customerVehicles.length === 0) {
                await Customer.findByIdAndDelete(orphanedCustomer._id);
                deletedCustomers++;
                logger.debug(`Deleted orphaned customer: ${orphanedCustomer._id}`);
              }
            }
          }

        } catch (error) {
          logger.error(`Error cleaning up vehicle ${vehicle._id}:`, error);
        }
      }

      // Also clean up customers who only have pending vehicles older than 7 days
      const customersWithOnlyOldPendingVehicles = await Customer.aggregate([
        {
          $lookup: {
            from: 'vehicles',
            localField: '_id',
            foreignField: 'customerId',
            as: 'vehicles'
          }
        },
        {
          $match: {
            $and: [
              { 'vehicles': { $ne: [] } }, // Has vehicles
              {
                'vehicles': {
                  $not: {
                    $elemMatch: {
                      $or: [
                        { status: 'completed' },
                        { status: 'pending', createdAt: { $gte: cutoffDate } }
                      ]
                    }
                  }
                }
              }
            ]
          }
        }
      ]);

      for (const customer of customersWithOnlyOldPendingVehicles) {
        try {
          // Delete all pending vehicles for this customer
          await Vehicle.deleteMany({ customerId: customer._id, status: 'pending' });
          
          // Delete the customer
          await Customer.findByIdAndDelete(customer._id);
          deletedCustomers++;
          logger.debug(`Deleted customer with only old pending vehicles: ${customer._id}`);
        } catch (error) {
          logger.error(`Error cleaning up customer ${customer._id}:`, error);
        }
      }

      // Only log if something was actually deleted
      if (deletedVehicles > 0 || deletedCustomers > 0) {
        logger.info(`Cleanup completed: ${deletedVehicles} vehicles and ${deletedCustomers} customers deleted`);
      }

      return {
        success: true,
        deletedVehicles,
        deletedCustomers,
        cutoffDate: cutoffDate.toISOString()
      };

    } catch (error) {
      logger.error('Error during cleanup process:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  async getCleanupStats() {
    if (!isMongoConnected()) {
      return {
        success: false,
        message: 'MongoDB not connected'
      };
    }

    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - this.PENDING_RETENTION_DAYS);

      const pendingVehiclesCount = await Vehicle.countDocuments({
        status: 'pending',
        createdAt: { $lt: cutoffDate }
      });

      const totalPendingVehicles = await Vehicle.countDocuments({ status: 'pending' });
      const totalCompletedVehicles = await Vehicle.countDocuments({ status: 'completed' });
      const totalCustomers = await Customer.countDocuments();

      return {
        success: true,
        stats: {
          pendingVehiclesOlderThan7Days: pendingVehiclesCount,
          totalPendingVehicles,
          totalCompletedVehicles,
          totalCustomers,
          cutoffDate: cutoffDate.toISOString(),
          retentionDays: this.PENDING_RETENTION_DAYS,
          isServiceRunning: this.isRunning
        }
      };
    } catch (error) {
      logger.error('Error getting cleanup stats:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

const cleanupService = new CleanupService();
export default cleanupService;
