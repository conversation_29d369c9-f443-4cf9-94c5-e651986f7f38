import { useState, useEffect } from "react";
import { RefreshCcw } from "lucide-react";
import CustomerDetailed from "./CustomerDetailed";
import { useGetDashboardStatsQuery, dashboardApiSlice } from "../../store/dashboardApiSlice";
import { useGetCustomersQuery, customerApiSlice, CustomerQueryParams } from "../../store/customerApiSlice";
import { useNavigate } from "react-router-dom";
import { store } from "../../store/store";
import { useAuth } from "../../context/AuthContext";
import { TotalCustomers, DatePicker, SortBy, Filters, FilterState } from "./ToolbarComponents";
import CustomerCard from "./CustomerCard";
import { SkeletonGrid } from "./SkeletonCard";

interface Customer {
  id: string;
  name: string;
  bike: string;
  phone: string;
  date: string;
  email: string;
  address: string;
  model: string;
  color: string;
  finance: string;
  altPhone: string;
  // Vehicle ID for document access
  vehicleId?: string | null;
  // Array of vehicle IDs associated with this customer
  vehicles?: string[];
  // Additional fields for exchange vehicle
  exchangeModel?: string;
  exchangeYear?: string;
  exchangeKmsDriven?: string;
  // Registration fields
  hasSpecialNumber?: string;
  expectedSpecialNumber?: string;
  registrationType?: string;
  // Insurance fields
  insuranceType?: string;
  nomineeName?: string;
  nomineeAge?: string;
  nomineeRelationship?: string;
  // Document fields
  documents?: {
    aadhar?: {
      fileName?: string;
      fileType?: string;
      fileSize?: number;
    };
    pan?: {
      fileName?: string;
      fileType?: string;
      fileSize?: number;
    };
    photo?: {
      fileName?: string;
      fileType?: string;
      fileSize?: number;
    };
    license?: {
      fileName?: string;
      fileType?: string;
      fileSize?: number;
    };
    other?: {
      fileName?: string;
      fileType?: string;
      fileSize?: number;
    };
  };
}

const Dashboard: React.FC = () => {
  const [page, setPage] = useState<number>(1);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [totalCustomers, setTotalCustomers] = useState<number>(0);
  const ITEMS_PER_PAGE = 16; // 4 in a row, 4 rows = 16 items per page

  // New filter state using FilterState interface - Default to "Today" filter
  const [filters, setFilters] = useState<FilterState>({
    search: '',
    sortField: 'createdAt',
    sortOrder: 'desc',
    dateRange: 'today', // Default to today's data
  });

  // Toolbar state
  const [datePickerOpen, setDatePickerOpen] = useState<boolean>(false);
  const [sortByOpen, setSortByOpen] = useState<boolean>(false);
  const [filtersOpen, setFiltersOpen] = useState<boolean>(false);

  const navigate = useNavigate();

  // Function to ensure only one toolbar dropdown is open at a time
  const toggleToolbar = (toolbar: 'date' | 'sort' | 'filter') => {
    if (toolbar === 'date') {
      setDatePickerOpen(!datePickerOpen);
      setSortByOpen(false);
      setFiltersOpen(false);
    } else if (toolbar === 'sort') {
      setSortByOpen(!sortByOpen);
      setDatePickerOpen(false);
      setFiltersOpen(false);
    } else if (toolbar === 'filter') {
      setFiltersOpen(!filtersOpen);
      setDatePickerOpen(false);
      setSortByOpen(false);
    }
  };

  // Close all dropdowns
  const closeAllDropdowns = () => {
    setDatePickerOpen(false);
    setSortByOpen(false);
    setFiltersOpen(false);
  };

  // Handle filter updates from toolbar components
  const handleFilterUpdate = (newFilters: Partial<FilterState>) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters
    }));
    setPage(1); // Reset to first page when filters change
  };

  // Handle search input change
  const handleSearchChange = (value: string) => {
    setFilters(prev => ({
      ...prev,
      search: value
    }));
    setPage(1);
  };

  // Get auth context
  const { user, logout } = useAuth();

  // Handle logout
  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      // Logout error - redirect to login anyway
      navigate('/login');
    }
  };

  // Build API query parameters from filter state
  const apiQueryParams: CustomerQueryParams = {
    page,
    limit: ITEMS_PER_PAGE,
    search: filters.search,
    sortField: filters.sortField,
    sortOrder: filters.sortOrder,
    dateFrom: filters.dateFrom,
    dateTo: filters.dateTo,
    dateRange: filters.dateRange,
    model: filters.model,
    color: filters.color,
    status: filters.status,
    city: filters.city,
    state: filters.state,
  };



  // Fetch paginated customers from the backend
  const {
    data: paginatedData,
    isLoading: isLoadingCustomers,
    isError: isCustomersError,
    error: customersError,
    refetch: refetchCustomers
  } = useGetCustomersQuery(apiQueryParams, {
    // Skip caching completely and always refetch
    refetchOnMountOrArgChange: 0.1, // Refetch immediately on any argument change
    refetchOnFocus: true,
    refetchOnReconnect: true,
    // Force fresh data on every filter change
    skip: false
  });

  // Fetch dashboard stats for total counts
  const {
    data: dashboardData,
    isLoading: isLoadingStats,
    isError: isStatsError,
    error: statsError,
    refetch: refetchStats
  } = useGetDashboardStatsQuery(undefined, {
    // Skip caching completely
    refetchOnMountOrArgChange: true,
    refetchOnFocus: true,
    refetchOnReconnect: true,
    // Add a cache busting parameter to ensure we always get fresh data
    skip: false
  });

  // Create a state for loading that we can control
  const [isLoadingState, setIsLoading] = useState<boolean>(false);
  // Determine overall loading and error states
  const isLoading = isLoadingCustomers || isLoadingStats || isLoadingState;
  const isError = isCustomersError || isStatsError;
  const error = customersError || statsError;

  // Force a refresh when component mounts and clear cache
  useEffect(() => {
    // Clear RTK Query cache
    store.dispatch(customerApiSlice.util.resetApiState());
    store.dispatch(dashboardApiSlice.util.resetApiState());

    // Clear localStorage data that might be causing issues
    localStorage.removeItem('dashboardData');
    localStorage.removeItem('customerData');

    // Then refetch
    refetchCustomers();
    refetchStats();
  }, [refetchCustomers, refetchStats]);

  // Handle refresh button click
  const handleRefresh = async () => {
    // Always show loading state when refresh is clicked
    setIsLoading(true);

    // Clear any existing customers first
    setCustomers([]);

    // Clear RTK Query cache
    store.dispatch(customerApiSlice.util.resetApiState());
    store.dispatch(dashboardApiSlice.util.resetApiState());

    // Clear any localStorage data that might be causing issues
    localStorage.removeItem('dashboardData');
    localStorage.removeItem('customerData');

    try {
      // Fetch dashboard stats first to get the latest total customer count
      const statsResult = await refetchStats();

      if (statsResult.data) {
        // Update total customers count immediately from the stats data
        setTotalCustomers(statsResult.data.totalCustomers);
      }

      // Then fetch the customer data
      const customersResult = await refetchCustomers();

      if (customersResult.data) {
        // If we have customer data but the count is 0, make sure we update the UI
        if (customersResult.data.totalCount === 0) {
          setTotalCustomers(0);
        }
      }

      // Dashboard refreshed with latest data
    } catch (error) {
      // Error refreshing dashboard data - handled by error state
    } finally {
      // Add a small delay to ensure skeleton effect is visible for at least a moment
      // This provides better UX by showing the loading state even if data loads very quickly
      setTimeout(() => {
        setIsLoading(false);
      }, 800);
    }

    return true; // Return true to indicate the refresh completed
  };

  // Process paginated customer data
  useEffect(() => {
    if (paginatedData && paginatedData.customers) {


      // Process customer data for display

      const apiCustomers = paginatedData.customers.map(customer => {
        // Format address if available
        let formattedAddress = "Address not available";
        if (customer.address) {
          if (typeof customer.address === 'string') {
            formattedAddress = customer.address;
          } else if (customer.address.fullAddress) {
            formattedAddress = customer.address.fullAddress;
          } else if (customer.address.houseNo && customer.address.street) {
            formattedAddress = `${customer.address.houseNo}, ${customer.address.street}, ${customer.address.city || ''}, ${customer.address.state || ''} ${customer.address.pincode || ''}`;
          }
        }

        // Get the first vehicle from the populated vehicles array
        const firstVehicle = customer.vehicles && customer.vehicles.length > 0
          ? customer.vehicles[0]
          : null;

        // Type assertion to handle the vehicle object properly
        const vehicleDetails = firstVehicle && typeof firstVehicle === 'object' && 'vehicleDetails' in firstVehicle
          ? (firstVehicle as any).vehicleDetails
          : null;

        // Clean up data by removing timestamps
        const cleanName = (customer.name || "Unknown").split('_')[0];
        const cleanPhone = (customer.phone || "N/A").split('_')[0];
        const cleanEmail = (customer.email || "N/A").split('_')[0];
        const cleanAltPhone = customer.altPhone ? customer.altPhone.split('_')[0] : "N/A";

        return {
          id: customer._id,
          name: cleanName,
          bike: vehicleDetails?.model || "Not specified",
          phone: cleanPhone,
          date: new Date(customer.createdAt).toLocaleDateString('en-GB', { timeZone: 'UTC' }) || "N/A",
          email: cleanEmail,
          address: formattedAddress,
          model: vehicleDetails?.model || "Not specified",
          color: vehicleDetails?.colour || "Not specified",
          finance: vehicleDetails?.financeMode || "Not specified",
          altPhone: cleanAltPhone,

          // Store vehicle ID for document access
          vehicleId: firstVehicle && typeof firstVehicle === 'object' && '_id' in firstVehicle
            ? (firstVehicle as any)._id
            : null,
          // Store all vehicle IDs
          vehicles: customer.vehicles || [],
        };
      });

      setCustomers(apiCustomers);

      // Set total customers count and update page
      if (paginatedData.totalCount) {
        setTotalCustomers(paginatedData.totalCount);
        setPage(paginatedData.page);
      }

      // Customer data loaded successfully
    }
  }, [paginatedData]);

  // Update total customers count from dashboard stats if available
  useEffect(() => {
    if (dashboardData && dashboardData.totalCustomers !== undefined) {
      setTotalCustomers(dashboardData.totalCustomers);
    }
  }, [dashboardData]);

  // Refetch data when filters or page change
  useEffect(() => {
    if (!isLoading) {
      refetchCustomers();
    }
  }, [filters, page, isLoading, refetchCustomers]);

  // Calculate pagination values from API response
  const totalPages = paginatedData ? paginatedData.totalPages : 1;
  const isLastPage = page >= totalPages;
  const isFirstPage = page <= 1;

  // Use the customers array for display
  const currentPageItems = customers;

  // Handle API errors
  useEffect(() => {
    if (isError) {
      // Dashboard API error - handled by error state display
    }
  }, [isError, error]);

  // Reset page to 1 when search term changes
  useEffect(() => {
    setPage(1);
  }, [filters.search]);

  // Return JSX
  return (
    <div className="bg-[#f9fbfd] min-h-screen text-gray-900">
      {/* Fixed Header with Logo and Home Icon - Mobile Optimized */}
      <header className="fixed top-0 left-0 right-0 bg-white px-4 sm:px-6 py-3 flex items-center justify-between border-b shadow-sm z-20">
        {/* Logo Only */}
        <div className="flex items-center">
          <img src="/logodark.png" alt="Quant 5X" className="h-7" />
        </div>

        {/* User Info and Logout - Aligned Right on Mobile */}
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-1 bg-gray-100 rounded-full px-2 py-1">
            <div className="w-7 h-7 bg-[#ff00ff] rounded-full flex items-center justify-center text-white text-xs font-bold">
              {user?.name ? user.name.charAt(0).toUpperCase() : 'A'}
            </div>
            <span className="text-sm font-medium text-gray-700 hidden sm:inline">{user?.name || 'Admin User'}</span>
          </div>

          <button
            onClick={handleLogout}
            className="px-3 py-1.5 bg-[#ff00ff] text-white rounded-full text-sm font-medium hover:bg-[#e600e6] transition-colors"
          >
            Logout
          </button>
        </div>
      </header>

      {/* Spacer to prevent content from being hidden under fixed header */}
      <div className="h-16"></div>

      {/* Stats removed */}

      {/* Toolbar - Responsive with smooth transitions */}
      <div className="px-4 sm:px-6 py-3">
        {/* Search Bar - Full width on all screens */}
        <div className="w-full mb-3">
          <div className="relative h-[38px]">
            <input
              type="text"
              placeholder="Search Customer"
              value={filters.search}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="pl-4 pr-10 py-1.5 h-[38px] border border-gray-300 rounded-md w-full focus:outline-none focus:ring-1 focus:ring-[#ff00ff] focus:border-[#ff00ff] text-sm"
            />
            <button className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-4 h-4">
                <path fillRule="evenodd" d="M10.5 3.75a6.75 6.75 0 100 13.5 6.75 6.75 0 000-13.5zM2.25 10.5a8.25 8.25 0 1114.59 5.28l4.69 4.69a.75.75 0 11-1.06 1.06l-4.69-4.69A8.25 8.25 0 012.25 10.5z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
        </div>

        {/* Filter Tools and Total Customers Row */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
          {/* Filter Tools Row - Fixed responsive layout */}
          <div className="grid grid-cols-3 gap-2 sm:flex sm:flex-nowrap sm:items-center sm:gap-3 transition-all duration-300 ease-in-out min-h-[38px]">
            {/* Date Picker Component */}
            <div className="w-full sm:w-auto sm:min-w-[120px]">
              <DatePicker
                isOpen={datePickerOpen}
                onToggle={() => toggleToolbar('date')}
                onClose={() => setDatePickerOpen(false)}
                onApply={handleFilterUpdate}
                currentFilters={filters}
              />
            </div>

            {/* Sort By Component */}
            <div className="w-full sm:w-auto sm:min-w-[100px]">
              <SortBy
                isOpen={sortByOpen}
                onToggle={() => toggleToolbar('sort')}
                onClose={() => setSortByOpen(false)}
                onApply={handleFilterUpdate}
                currentFilters={filters}
              />
            </div>

            {/* Filters Component */}
            <div className="w-full sm:w-auto sm:min-w-[80px]">
              <Filters
                isOpen={filtersOpen}
                onToggle={() => toggleToolbar('filter')}
                onClose={() => setFiltersOpen(false)}
                onApply={handleFilterUpdate}
                currentFilters={filters}
              />
            </div>
          </div>

          {/* Total Customers - Right side on desktop, below filters on mobile */}
          <div className="w-full sm:w-auto sm:flex-shrink-0 sm:min-w-[280px] transition-all duration-300 ease-in-out">
            <TotalCustomers
              totalCount={totalCustomers}
              onRefresh={handleRefresh}
            />
          </div>
        </div>
      </div>

      {/* Quick Date Filter Tabs - Improved for Mobile */}
      <div className="flex border-b border-gray-200 px-4 sm:px-6 mb-4 sm:mb-6 overflow-x-auto whitespace-nowrap justify-start gap-1 mt-1">
        <button
          onClick={() => {
            handleFilterUpdate({ dateRange: undefined, dateFrom: undefined, dateTo: undefined });
          }}
          className={`px-3 sm:px-4 py-2 text-sm font-medium transition-colors ${!filters.dateRange && !filters.dateFrom && !filters.dateTo ? 'text-gray-900 border-b-2 border-[#ff00ff]' : 'text-gray-500 hover:text-gray-700'}`}
        >
          View All
        </button>
        <button
          onClick={() => {
            handleFilterUpdate({ dateRange: 'today', dateFrom: undefined, dateTo: undefined });
          }}
          className={`px-3 sm:px-4 py-2 text-sm font-medium transition-colors ${filters.dateRange === 'today' ? 'text-gray-900 border-b-2 border-[#ff00ff]' : 'text-gray-500 hover:text-gray-700'}`}
        >
          Today
        </button>
        <button
          onClick={() => {
            handleFilterUpdate({ dateRange: 'lastWeek', dateFrom: undefined, dateTo: undefined });
          }}
          className={`px-3 sm:px-4 py-2 text-sm font-medium transition-colors ${filters.dateRange === 'lastWeek' ? 'text-gray-900 border-b-2 border-[#ff00ff]' : 'text-gray-500 hover:text-gray-700'}`}
        >
          Last Week
        </button>
        <button
          onClick={() => {
            handleFilterUpdate({ dateRange: 'lastMonth', dateFrom: undefined, dateTo: undefined });
          }}
          className={`px-3 sm:px-4 py-2 text-sm font-medium transition-colors ${filters.dateRange === 'lastMonth' ? 'text-gray-900 border-b-2 border-[#ff00ff]' : 'text-gray-500 hover:text-gray-700'}`}
        >
          Last Month
        </button>
      </div>

      {/* Error State */}
      {isError && (
        <div className="mx-6 my-6 bg-red-50 border border-red-200 rounded-lg overflow-hidden">
          <div className="bg-red-100 px-6 py-3 border-b border-red-200">
            <h3 className="font-semibold text-red-700">Error loading dashboard data</h3>
          </div>
          <div className="p-6">
            <p className="text-red-600 mb-4">
              {error && 'status' in error
                ? `Server responded with status: ${error.status}`
                : 'Could not connect to the server. Please check if the backend is running.'}
            </p>
            <button
              onClick={handleRefresh}
              className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg text-sm transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      )}

      {/* Cards */}
      {isLoading ? (
        // Show skeleton loading effect when loading
        <SkeletonGrid />
      ) : (
        <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-5 px-4 sm:px-6 pb-24 mt-4">
          {currentPageItems.length > 0 ? (
            currentPageItems.map((customer, index) => (
              <CustomerCard
                key={index}
                customer={customer}
                onClick={() => setSelectedCustomer(customer)}
              />
            ))
          ) : (
            <div className="col-span-full text-center py-10">
              <p className="text-gray-500 mb-4">No customer data available. Click update to refresh.</p>
              <button
                onClick={handleRefresh}
                className="px-4 py-2 bg-[#ff00ff] hover:bg-[#e600e6] text-white rounded-lg text-sm transition-colors flex items-center mx-auto"
              >
                <RefreshCcw className="w-4 h-4 mr-2" />
                Check for new data
              </button>
            </div>
          )}
        </div>
      )}

      {/* Fixed Pagination - Improved for consistent alignment at all zoom levels */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-md z-20 w-full">
        <div className="flex justify-between items-center px-3 sm:px-6 py-3 sm:py-4">
          <div className="text-sm font-medium whitespace-nowrap">
            Page <span className="font-bold">{page}</span>{totalPages > 0 ? ` / ${totalPages}` : ''}
          </div>
          <div className="flex items-center gap-2 sm:gap-3">
            <button
              onClick={() => {
                if (!isFirstPage) {
                  setPage((p) => Math.max(1, p - 1));
                }
              }}
              disabled={isFirstPage || isLoading}
              className={`px-3 sm:px-6 py-2 rounded-md text-sm font-medium transition-all ${
                isFirstPage || isLoading
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'border border-gray-300 hover:bg-gray-50 text-gray-700'
              }`}
              aria-label="Previous page"
            >
              {isLoading ? 'Loading...' : 'Previous'}
            </button>
            <button
              onClick={() => {
                if (!isLastPage) {
                  setPage((p) => p + 1);
                }
              }}
              disabled={isLastPage || isLoading}
              className={`px-3 sm:px-6 py-2 rounded-md text-sm font-medium transition-all ${
                isLastPage || isLoading
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-[#ff00ff] hover:bg-[#e600e6] text-white'
              }`}
              aria-label="Next page"
            >
              {isLoading ? 'Loading...' : 'Next'}
            </button>
          </div>
        </div>
      </div>

      {/* Backdrop for closing dropdowns */}
      {(datePickerOpen || sortByOpen || filtersOpen) && (
        <div
          className="fixed inset-0 bg-black bg-opacity-20 z-40"
          onClick={closeAllDropdowns}
        ></div>
      )}

      {/* Customer Detail Modal */}
      {selectedCustomer && (
        <CustomerDetailed
          isOpen={!!selectedCustomer}
          onClose={() => setSelectedCustomer(null)}
          customer={selectedCustomer}
        />
      )}
    </div>
  );
};

export default Dashboard;