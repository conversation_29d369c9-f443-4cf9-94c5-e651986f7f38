import axios from 'axios';
import config from '../config/config';

const API_URL = config.api.baseUrl;

// Create axios instances with interceptors for better error handling
const createApiInstance = (contentType) => {
  const instance = axios.create({
    baseURL: API_URL,
    headers: {
      'Content-Type': contentType
    },
    timeout: config.api.timeout,
    withCredentials: config.api.withCredentials // Enable cookies for cross-origin requests
  });

  // Add request interceptor for error handling
  instance.interceptors.request.use(
    (config) => {
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  // Add response interceptor for error handling
  instance.interceptors.response.use(
    (response) => {
      return response;
    },
    (error) => {
      // If the server is unreachable, provide a more user-friendly error
      if (!error.response) {
        return Promise.reject({
          response: {
            data: {
              success: false,
              message: 'Cannot connect to server. Please check your internet connection.'
            }
          }
        });
      }

      return Promise.reject(error);
    }
  );

  return instance;
};

// Create API instances
const api = createApiInstance('application/json');
const fileApi = createApiInstance('multipart/form-data');

// Production API implementation
const vehicleAPI = {
  // Check for duplicate customers
  checkDuplicate: async (data) => {
    return await api.post('/vehicle/check-duplicate', data);
  },

  // Create a new vehicle record
  createVehicle: async (data) => {
    return await api.post('/vehicle', data);
  },

  // Get all vehicle records
  getAllVehicles: async () => {
    return await api.get('/vehicle');
  },

  // Get a vehicle record by ID
  getVehicleById: async (id) => {
    return await api.get(`/vehicle/${id}`);
  },

  // Update an entire vehicle record
  updateVehicle: async (id, data) => {
    return await api.put(`/vehicle/${id}`, data);
  },

  // Delete a vehicle record
  deleteVehicle: async (id) => {
    return await api.delete(`/vehicle/${id}`);
  },

  // Form-specific updates
  updateCustomerInfo: async (id, data) => {
    return await api.put(`/vehicle/${id}/customer`, data);
  },

  updateVehicleDetails: async (id, data) => {
    return await api.put(`/vehicle/${id}/vehicle-details`, data);
  },

  updateExchangeVehicle: async (id, data) => {
    return await api.put(`/vehicle/${id}/exchange-vehicle`, data);
  },

  updateRegistration: async (id, data) => {
    return await api.put(`/vehicle/${id}/registration`, data);
  },

  updateInsurance: async (id, data) => {
    return await api.put(`/vehicle/${id}/insurance`, data);
  },

  uploadDocuments: async (id, data) => {
    return await api.put(`/vehicle/${id}/documents`, data);
  },

  // Special method for file uploads using FormData
  uploadDocumentsWithFiles: async (id, formData) => {
    return await fileApi.put(`/vehicle/${id}/documents/files`, formData);
  }
};

// Export the production API implementation
export { vehicleAPI };

export default api;
