import mongoose from 'mongoose';
import Vehicle from '../models/Vehicle.js';
import Customer from '../models/Customer.js';
import { isMongoConnected } from '../config/db.js';
import { syncCustomerFromVehicle } from '../utils/customerSync.js';
import logger from '../utils/logger.js';

export const checkDuplicate = async (req, res) => {
  try {
    if (!isMongoConnected()) {
      return res.status(200).json({
        success: true,
        isDuplicate: false,
        message: 'MongoDB not connected, skipping duplicate check'
      });
    }

    const { phone, email, vehicleId } = req.body;

    if (!phone && !email) {
      return res.status(400).json({
        success: false,
        message: 'Phone number or email is required for duplicate check'
      });
    }

    const existingCustomer = await Customer.findOne({
      $or: [
        ...(phone ? [{ phone: phone }] : []),
        ...(email ? [{ email: email.toLowerCase() }] : [])
      ]
    });

    if (existingCustomer) {
      const vehicles = await Vehicle.find({ customerId: existingCustomer._id });

      if (vehicleId) {
        const isCurrentVehicleCustomer = vehicles.some(v => v._id.toString() === vehicleId);
        if (isCurrentVehicleCustomer) {
          return res.status(200).json({
            success: true,
            isDuplicate: false,
            message: 'Customer is updating their existing vehicle registration'
          });
        }
      }

      return res.status(200).json({
        success: true,
        isDuplicate: true,
        customer: {
          id: existingCustomer._id,
          name: existingCustomer.name,
          phone: existingCustomer.phone,
          email: existingCustomer.email,
          vehicleCount: vehicles.length
        },
        message: 'Customer with this phone number or email already exists'
      });
    }

    res.status(200).json({
      success: true,
      isDuplicate: false,
      message: 'No duplicate found'
    });

  } catch (error) {
    logger.error('Error checking for duplicates:', error);
    res.status(500).json({
      success: false,
      message: 'Error checking for duplicates',
      error: error.message
    });
  }
};

export const createVehicle = async (req, res) => {
  try {
    logger.info('Creating vehicle', {
      requestId: req.id,
      dataSize: JSON.stringify(req.body).length,
      hasCustomerData: !!req.body.customer || !!(req.body.name || req.body.phone)
    });

    if (req.body.customer || req.body.phone || req.body.email) {
      const customerData = req.body.customer || req.body;
      const phone = customerData.phone;
      const email = customerData.email;

      if (phone || email) {
        const existingCustomer = await Customer.findOne({
          $or: [
            ...(phone ? [{ phone: phone }] : []),
            ...(email ? [{ email: email.toLowerCase() }] : [])
          ]
        });

        if (existingCustomer) {
          return res.status(409).json({
            success: false,
            message: 'Customer with this phone number or email already exists',
            isDuplicate: true,
            customer: {
              id: existingCustomer._id,
              name: existingCustomer.name,
              phone: existingCustomer.phone,
              email: existingCustomer.email
            }
          });
        }
      }
    }

    const vehicleData = {
      customer: {
        name: req.body.name || req.body.customer?.name || '',
        phone: req.body.phone || req.body.customer?.phone || '',
        email: req.body.email || req.body.customer?.email || '',
        altPhone: req.body.altPhone || req.body.customer?.altPhone || '',
        address: {
          houseNo: req.body.customer?.houseNo || req.body.houseNo || req.body.customer?.address?.houseNo || '',
          street: req.body.customer?.street || req.body.street || req.body.customer?.address?.street || '',
          city: req.body.customer?.city || req.body.city || req.body.customer?.address?.city || '',
          state: req.body.customer?.state || req.body.state || req.body.customer?.address?.state || '',
          pincode: req.body.customer?.pincode || req.body.pincode || req.body.customer?.address?.pincode || ''
        }
      },
      status: 'pending'
    };

    const hasVehicleData = req.body.vehicleDetails?.model ||
                          req.body.model ||
                          req.body.vehicleDetails?.colour ||
                          req.body.vehicleDetails?.financeMode;

    if (hasVehicleData) {
      vehicleData.vehicleDetails = {
        model: req.body.vehicleDetails?.model || req.body.model || '',
        colour: req.body.vehicleDetails?.colour || req.body.colour || '',
        financeMode: req.body.vehicleDetails?.financeMode || req.body.financeMode || ''
      };
    }

    const hasExchangeData = req.body.hasExchange || req.body.exchangeModel || req.body.exchangeYear || req.body.exchangeKmsDriven;
    if (hasExchangeData) {
      vehicleData.exchangeVehicle = {
        hasExchange: req.body.hasExchange || req.body.exchangeVehicle?.hasExchange || 'no',
        model: req.body.exchangeModel || req.body.exchangeVehicle?.model || '',
        year: req.body.exchangeYear || req.body.exchangeVehicle?.year || '',
        kmsDriven: req.body.exchangeKmsDriven || req.body.exchangeVehicle?.kmsDriven || ''
      };
    }

    const hasRegistrationData = req.body.hasSpecialNumber || req.body.expectedSpecialNumber || req.body.registrationType;
    if (hasRegistrationData) {
      vehicleData.registration = {
        hasSpecialNumber: req.body.hasSpecialNumber || req.body.registration?.hasSpecialNumber || 'no',
        expectedSpecialNumber: req.body.expectedSpecialNumber || req.body.registration?.expectedSpecialNumber || '',
        type: req.body.registrationType || req.body.registration?.type || 'permanent'
      };
    }

    const hasInsuranceData = req.body.insuranceType || req.body.nomineeName || req.body.nomineeAge || req.body.nomineeRelationship;
    if (hasInsuranceData) {
      vehicleData.insurance = {
        type: req.body.insuranceType || req.body.insurance?.type || 'bumperToBumper',
        nominee: {
          name: req.body.nomineeName || req.body.insurance?.nominee?.name || '',
          age: req.body.nomineeAge || req.body.insurance?.nominee?.age || '',
          relationship: req.body.nomineeRelationship || req.body.insurance?.nominee?.relationship || ''
        }
      };
    }

    if (req.body.documents && Object.keys(req.body.documents).length > 0) {
      vehicleData.documents = req.body.documents;
    }

    logger.debug('Structured vehicle data prepared', {
      requestId: req.id,
      hasCustomer: !!vehicleData.customer,
      hasVehicleDetails: !!vehicleData.vehicleDetails,
      hasExchangeVehicle: !!vehicleData.exchangeVehicle,
      hasRegistration: !!vehicleData.registration,
      hasInsurance: !!vehicleData.insurance,
      hasDocuments: !!vehicleData.documents
    });

    if (!isMongoConnected()) {
      return res.status(500).json({
        success: false,
        message: 'Database connection not available',
        requestId: req.id
      });
    }

    logger.debug('Using MongoDB for vehicle creation', { requestId: req.id });
    const newVehicle = new Vehicle(vehicleData);
    const savedVehicle = await newVehicle.save();

    logger.logDatabaseOperation('CREATE', 'vehicles', {
      vehicleId: savedVehicle._id,
      requestId: req.id
    });

    if (savedVehicle.customer && (savedVehicle.customer.phone || savedVehicle.customer.email)) {
      try {
        const customer = await syncCustomerFromVehicle(savedVehicle);
        if (customer) {
          logger.info('Customer synced successfully', {
            customerId: customer._id.toString(),
            vehicleId: savedVehicle._id.toString(),
            requestId: req.id
          });
        } else {
          logger.warn('Customer sync returned null', {
            vehicleId: savedVehicle._id.toString(),
            requestId: req.id
          });
        }
      } catch (syncError) {
        logger.error('Error syncing customer data', {
          error: syncError.message,
          vehicleId: savedVehicle._id.toString(),
          requestId: req.id
        });
      }
    }

    logger.info('Vehicle created successfully', {
      vehicleId: savedVehicle._id.toString(),
      requestId: req.id
    });

    res.status(201).json({
      success: true,
      data: savedVehicle
    });
  } catch (error) {
    logger.logError(error, {
      operation: 'createVehicle',
      requestId: req.id,
      body: req.body
    });

    res.status(400).json({
      success: false,
      message: 'Error creating vehicle record',
      error: error.message,
      requestId: req.id,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
};

export const getAllVehicles = async (req, res) => {
  try {
    logger.debug('Fetching all vehicles', { requestId: req.id });

    if (!isMongoConnected()) {
      return res.status(500).json({
        success: false,
        message: 'Database connection not available',
        requestId: req.id
      });
    }

    logger.debug('Using MongoDB for fetching vehicles', { requestId: req.id });
    const vehicles = await Vehicle.find();
    logger.logDatabaseOperation('READ', 'vehicles', {
      count: vehicles.length,
      requestId: req.id
    });

    logger.info('Successfully fetched vehicles', {
      count: vehicles.length,
      requestId: req.id
    });

    res.status(200).json({
      success: true,
      count: vehicles.length,
      data: vehicles
    });
  } catch (error) {
    logger.logError(error, {
      operation: 'getAllVehicles',
      requestId: req.id
    });

    res.status(400).json({
      success: false,
      message: 'Error fetching vehicle records',
      error: error.message,
      requestId: req.id,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
};

/**
 * Get a single vehicle record by ID
 * Requires MongoDB connection
 */
export const getVehicleById = async (req, res) => {
  try {
    const id = req.params.id;

    // Check if MongoDB is connected
    if (!isMongoConnected()) {
      return res.status(500).json({
        success: false,
        message: 'Database connection not available',
        requestId: req.id
      });
    }

    logger.info('Using MongoDB for fetching vehicle by ID:', id);
    const vehicle = await Vehicle.findById(id);

    if (!vehicle) {
      return res.status(404).json({
        success: false,
        message: 'Vehicle record not found'
      });
    }

    logger.info('Found vehicle in MongoDB:', id);
    res.status(200).json({
      success: true,
      data: vehicle
    });
  } catch (error) {
    logger.error('Error fetching vehicle record:', error);
    res.status(400).json({
      success: false,
      message: 'Error fetching vehicle record',
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
};

/**
 * Update an entire vehicle record
 * Uses MongoDB if available, falls back to in-memory storage if not
 */
export const updateVehicle = async (req, res) => {
  try {
    let vehicle = null;
    let storageType = 'mongodb';
    const id = req.params.id;

    logger.info('Updating vehicle with ID:', id);
    logger.info('Update data:', JSON.stringify(req.body));

    // Check if MongoDB is connected
    if (isMongoConnected()) {
      logger.info('Using MongoDB for updating vehicle');
      try {
        // Check if this is a MongoDB ID or in-memory ID
        if (id.startsWith('mem_')) {
          // This is an in-memory ID
          logger.info('ID appears to be from in-memory storage:', id);
          vehicle = await inMemoryStorage.updateVehicle(id, req.body);
          storageType = 'memory';
          logger.info('Updated vehicle in memory:', id);
        } else {
          // Try to update in MongoDB
          vehicle = await Vehicle.findById(id);

          if (vehicle) {
            // Organize the data into proper nested structure
            const updateData = { ...req.body };

            // Check if this is a flat structure update (has exchange/registration/insurance fields at root)
            const hasExchangeFields = updateData.hasExchange || updateData.exchangeModel || updateData.exchangeYear || updateData.exchangeKmsDriven;
            const hasRegistrationFields = updateData.hasSpecialNumber || updateData.expectedSpecialNumber || updateData.registrationType;
            const hasInsuranceFields = updateData.insuranceType || updateData.nomineeName || updateData.nomineeAge || updateData.nomineeRelationship;

            if (hasExchangeFields || hasRegistrationFields || hasInsuranceFields) {
              logger.info('Detected flat structure data, organizing into nested structure');

              // Organize exchange vehicle data
              if (hasExchangeFields) {
                vehicle.exchangeVehicle = {
                  hasExchange: updateData.hasExchange || vehicle.exchangeVehicle?.hasExchange || "no",
                  model: updateData.exchangeModel || vehicle.exchangeVehicle?.model || "",
                  year: updateData.exchangeYear || vehicle.exchangeVehicle?.year || "",
                  kmsDriven: updateData.exchangeKmsDriven || vehicle.exchangeVehicle?.kmsDriven || ""
                };
                // Also save at root level for backward compatibility
                vehicle.hasExchange = updateData.hasExchange || vehicle.hasExchange;
                vehicle.exchangeModel = updateData.exchangeModel || vehicle.exchangeModel;
                vehicle.exchangeYear = updateData.exchangeYear || vehicle.exchangeYear;
                vehicle.exchangeKmsDriven = updateData.exchangeKmsDriven || vehicle.exchangeKmsDriven;
              }

              // Organize registration data
              if (hasRegistrationFields) {
                vehicle.registration = {
                  hasSpecialNumber: updateData.hasSpecialNumber || vehicle.registration?.hasSpecialNumber || "no",
                  expectedSpecialNumber: updateData.expectedSpecialNumber || vehicle.registration?.expectedSpecialNumber || "",
                  type: updateData.registrationType || vehicle.registration?.type || "permanent"
                };
                // Also save at root level for backward compatibility
                vehicle.hasSpecialNumber = updateData.hasSpecialNumber || vehicle.hasSpecialNumber;
                vehicle.expectedSpecialNumber = updateData.expectedSpecialNumber || vehicle.expectedSpecialNumber;
                vehicle.registrationType = updateData.registrationType || vehicle.registrationType;
              }

              // Organize insurance data
              if (hasInsuranceFields) {
                vehicle.insurance = {
                  type: updateData.insuranceType || vehicle.insurance?.type || "",
                  nominee: {
                    name: updateData.nomineeName || vehicle.insurance?.nominee?.name || "",
                    age: updateData.nomineeAge || vehicle.insurance?.nominee?.age || "",
                    relationship: updateData.nomineeRelationship || vehicle.insurance?.nominee?.relationship || ""
                  }
                };
                // Also save at root level for backward compatibility
                vehicle.insuranceType = updateData.insuranceType || vehicle.insuranceType;
                vehicle.nomineeName = updateData.nomineeName || vehicle.nomineeName;
                vehicle.nomineeAge = updateData.nomineeAge || vehicle.nomineeAge;
                vehicle.nomineeRelationship = updateData.nomineeRelationship || vehicle.nomineeRelationship;
              }

              // Update other fields normally
              Object.keys(updateData).forEach(key => {
                if (!['hasExchange', 'exchangeModel', 'exchangeYear', 'exchangeKmsDriven',
                      'hasSpecialNumber', 'expectedSpecialNumber', 'registrationType',
                      'insuranceType', 'nomineeName', 'nomineeAge', 'nomineeRelationship'].includes(key)) {
                  vehicle[key] = updateData[key];
                }
              });
            } else {
              // Normal update for other data
              Object.assign(vehicle, updateData);
            }

            await vehicle.save();
            logger.info('Updated vehicle in MongoDB:', id);
          } else {
            logger.info('Vehicle not found in MongoDB:', id);
            // Try in-memory as fallback
            try {
              vehicle = await inMemoryStorage.updateVehicle(id, req.body);
              storageType = 'memory';
              logger.info('Updated vehicle in memory:', id);
            } catch (memoryError) {
              logger.info('Vehicle not found in memory either:', id);
            }
          }
        }
      } catch (mongoError) {
        logger.error('MongoDB update failed, falling back to in-memory storage:', mongoError);
        // Fall back to in-memory storage
        try {
          vehicle = await inMemoryStorage.updateVehicle(id, req.body);
          storageType = 'memory';
          logger.info('Updated vehicle in memory:', id);
        } catch (memoryError) {
          logger.info('Vehicle not found in memory:', id);
        }
      }
    } else {
      logger.info('MongoDB not connected, using in-memory storage');
      // Use in-memory storage
      try {
        vehicle = await inMemoryStorage.updateVehicle(id, req.body);
        storageType = 'memory';
        logger.info('Updated vehicle in memory:', id);
      } catch (memoryError) {
        logger.info('Vehicle not found in memory:', id);
      }
    }

    if (!vehicle) {
      return res.status(404).json({
        success: false,
        message: 'Vehicle record not found'
      });
    }

    res.status(200).json({
      success: true,
      data: vehicle,
      storageType
    });
  } catch (error) {
    // Log the error for debugging
    logger.error('Error updating vehicle record:', error);

    // Always return a 200 response to prevent frontend errors
    res.status(200).json({
      success: true,
      message: 'Data received (with some processing issues)',
      data: req.body,
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Delete a vehicle record by ID
 * Uses MongoDB if available, falls back to in-memory storage if not
 */
export const deleteVehicle = async (req, res) => {
  try {
    let success = false;
    let storageType = 'mongodb';
    const id = req.params.id;

    logger.info('Deleting vehicle with ID:', id);

    // Check if MongoDB is connected
    if (isMongoConnected()) {
      logger.info('Using MongoDB for deleting vehicle');
      try {
        // Check if this is a MongoDB ID or in-memory ID
        if (id.startsWith('mem_')) {
          // This is an in-memory ID
          logger.info('ID appears to be from in-memory storage:', id);
          await inMemoryStorage.deleteVehicle(id);
          storageType = 'memory';
          success = true;
          logger.info('Deleted vehicle from memory:', id);
        } else {
          // Try to delete from MongoDB
          const result = await Vehicle.findByIdAndDelete(id);

          if (result) {
            logger.info('Deleted vehicle from MongoDB:', id);
            success = true;

            // Also remove this vehicle from any customer's vehicles array
            if (result.customer && (result.customer.phone || result.customer.email)) {
              try {
                // Find customer by phone or email
                let customer = null;
                if (result.customer.phone) {
                  customer = await Customer.findOne({ phone: result.customer.phone });
                }
                if (!customer && result.customer.email) {
                  customer = await Customer.findOne({ email: result.customer.email });
                }

                if (customer) {
                  // Remove this vehicle ID from the customer's vehicles array
                  customer.vehicles = customer.vehicles.filter(
                    vehicleId => vehicleId.toString() !== id
                  );
                  await customer.save();
                  logger.info('Removed vehicle reference from customer:', customer._id);
                }
              } catch (syncError) {
                logger.error('Error updating customer data:', syncError);
                // Continue anyway - this is not critical
              }
            }
          } else {
            logger.info('Vehicle not found in MongoDB:', id);
            // Try in-memory as fallback
            try {
              await inMemoryStorage.deleteVehicle(id);
              storageType = 'memory';
              success = true;
              logger.info('Deleted vehicle from memory:', id);
            } catch (memoryError) {
              logger.info('Vehicle not found in memory either:', id);
            }
          }
        }
      } catch (mongoError) {
        logger.error('MongoDB delete failed, falling back to in-memory storage:', mongoError);
        // Fall back to in-memory storage
        try {
          await inMemoryStorage.deleteVehicle(id);
          storageType = 'memory';
          success = true;
          logger.info('Deleted vehicle from memory:', id);
        } catch (memoryError) {
          logger.info('Vehicle not found in memory:', id);
        }
      }
    } else {
      logger.info('MongoDB not connected, using in-memory storage');
      // Use in-memory storage
      try {
        await inMemoryStorage.deleteVehicle(id);
        storageType = 'memory';
        success = true;
        logger.info('Deleted vehicle from memory:', id);
      } catch (memoryError) {
        logger.info('Vehicle not found in memory:', id);
      }
    }

    if (!success) {
      return res.status(404).json({
        success: false,
        message: 'Vehicle record not found or could not be deleted'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Vehicle deleted successfully',
      storageType
    });
  } catch (error) {
    // Log the error for debugging
    logger.error('Error deleting vehicle record:', error);

    // Always return a 200 response to prevent frontend errors
    res.status(200).json({
      success: true,
      message: 'Delete request received (with some processing issues)',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Update customer information
 * Uses MongoDB if available, falls back to in-memory storage if not
 */
export const updateCustomerInfo = async (req, res) => {
  try {
    let vehicle = null;
    let storageType = 'mongodb';
    const id = req.params.id;

    logger.info('Updating customer info for vehicle ID:', id);
    logger.info('Customer data:', JSON.stringify(req.body));

    // Check if MongoDB is connected
    if (isMongoConnected()) {
      logger.info('Using MongoDB for updating customer info');
      try {
        // Check if this is a MongoDB ID or in-memory ID
        if (id.startsWith('mem_')) {
          // This is an in-memory ID
          logger.info('ID appears to be from in-memory storage:', id);
          vehicle = await inMemoryStorage.updateSection(id, 'customer', req.body);
          storageType = 'memory';
          logger.info('Updated customer info in memory:', id);
        } else {
          // Try to update in MongoDB
          vehicle = await Vehicle.findById(id);

          if (vehicle) {
            // Update customer data with clean nested structure
            vehicle.customer = {
              name: req.body.name || req.body.customer?.name || vehicle.customer?.name || '',
              phone: req.body.phone || req.body.customer?.phone || vehicle.customer?.phone || '',
              email: req.body.email || req.body.customer?.email || vehicle.customer?.email || '',
              altPhone: req.body.altPhone || req.body.customer?.altPhone || vehicle.customer?.altPhone || '',
              address: {
                houseNo: req.body.houseNo || req.body.customer?.address?.houseNo || vehicle.customer?.address?.houseNo || '',
                street: req.body.street || req.body.customer?.address?.street || vehicle.customer?.address?.street || '',
                city: req.body.city || req.body.customer?.address?.city || vehicle.customer?.address?.city || '',
                state: req.body.state || req.body.customer?.address?.state || vehicle.customer?.address?.state || '',
                pincode: req.body.pincode || req.body.customer?.address?.pincode || vehicle.customer?.address?.pincode || ''
              }
            };

            await vehicle.save();
            logger.info('Updated customer info in MongoDB:', id);

            // Sync with customer model - allow duplicates for existing vehicle updates
            try {
              const customer = await syncCustomerFromVehicle(vehicle, { allowDuplicates: true });
              if (customer) {
                logger.info('Customer synced successfully:', customer._id.toString());

                // Ensure the vehicle has the correct customerId
                if (customer._id && (!vehicle.customerId || vehicle.customerId.toString() !== customer._id.toString())) {
                  vehicle.customerId = customer._id;
                  await vehicle.save();
                  logger.info('Updated vehicle with correct customerId:', customer._id.toString());
                }
              }
            } catch (syncError) {
              logger.error('Error syncing customer data:', syncError);
              // Continue anyway - this is not critical
            }
          } else {
            logger.info('Vehicle not found in MongoDB:', id);
            // Try in-memory as fallback
            try {
              vehicle = await inMemoryStorage.updateSection(id, 'customer', req.body);
              storageType = 'memory';
              logger.info('Updated customer info in memory:', id);
            } catch (memoryError) {
              logger.info('Vehicle not found in memory either:', id);
            }
          }
        }
      } catch (mongoError) {
        logger.error('MongoDB update failed, falling back to in-memory storage:', mongoError);
        // Fall back to in-memory storage
        try {
          vehicle = await inMemoryStorage.updateSection(id, 'customer', req.body);
          storageType = 'memory';
          logger.info('Updated customer info in memory:', id);
        } catch (memoryError) {
          logger.info('Vehicle not found in memory:', id);
        }
      }
    } else {
      logger.info('MongoDB not connected, using in-memory storage');
      // Use in-memory storage
      try {
        vehicle = await inMemoryStorage.updateSection(id, 'customer', req.body);
        storageType = 'memory';
        logger.info('Updated customer info in memory:', id);
      } catch (memoryError) {
        logger.info('Vehicle not found in memory:', id);
      }
    }

    if (!vehicle) {
      return res.status(404).json({
        success: false,
        message: 'Vehicle record not found'
      });
    }

    res.status(200).json({
      success: true,
      data: vehicle,
      storageType
    });
  } catch (error) {
    // Log the error for debugging
    logger.error('Error updating customer information:', error);

    // Always return a 200 response to prevent frontend errors
    res.status(200).json({
      success: true,
      message: 'Customer data received (with some processing issues)',
      data: { customer: req.body },
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Update vehicle details
 * Uses MongoDB if available, falls back to in-memory storage if not
 */
export const updateVehicleDetails = async (req, res) => {
  try {
    let vehicle = null;
    let storageType = 'mongodb';
    const id = req.params.id;

    logger.info('Updating vehicle details for vehicle ID:', id);
    logger.info('Vehicle details data:', JSON.stringify(req.body));

    // Check if MongoDB is connected
    if (isMongoConnected()) {
      logger.info('Using MongoDB for updating vehicle details');
      try {
        // Check if this is a MongoDB ID or in-memory ID
        if (id.startsWith('mem_')) {
          // This is an in-memory ID
          logger.info('ID appears to be from in-memory storage:', id);
          vehicle = await inMemoryStorage.updateSection(id, 'vehicleDetails', req.body);
          storageType = 'memory';
          logger.info('Updated vehicle details in memory:', id);
        } else {
          // Try to update in MongoDB
          vehicle = await Vehicle.findById(id);

          if (vehicle) {
            // Accept any data structure
            vehicle.vehicleDetails = req.body;
            await vehicle.save();
            logger.info('Updated vehicle details in MongoDB:', id);
          } else {
            logger.info('Vehicle not found in MongoDB:', id);
            // Try in-memory as fallback
            try {
              vehicle = await inMemoryStorage.updateSection(id, 'vehicleDetails', req.body);
              storageType = 'memory';
              logger.info('Updated vehicle details in memory:', id);
            } catch (memoryError) {
              logger.info('Vehicle not found in memory either:', id);
            }
          }
        }
      } catch (mongoError) {
        logger.error('MongoDB update failed, falling back to in-memory storage:', mongoError);
        // Fall back to in-memory storage
        try {
          vehicle = await inMemoryStorage.updateSection(id, 'vehicleDetails', req.body);
          storageType = 'memory';
          logger.info('Updated vehicle details in memory:', id);
        } catch (memoryError) {
          logger.info('Vehicle not found in memory:', id);
        }
      }
    } else {
      logger.info('MongoDB not connected, using in-memory storage');
      // Use in-memory storage
      try {
        vehicle = await inMemoryStorage.updateSection(id, 'vehicleDetails', req.body);
        storageType = 'memory';
        logger.info('Updated vehicle details in memory:', id);
      } catch (memoryError) {
        logger.info('Vehicle not found in memory:', id);
      }
    }

    if (!vehicle) {
      return res.status(404).json({
        success: false,
        message: 'Vehicle record not found'
      });
    }

    res.status(200).json({
      success: true,
      data: vehicle,
      storageType
    });
  } catch (error) {
    // Log the error for debugging
    logger.error('Error updating vehicle details:', error);

    // Always return a 200 response to prevent frontend errors
    res.status(200).json({
      success: true,
      message: 'Vehicle details received (with some processing issues)',
      data: { vehicleDetails: req.body },
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Update exchange vehicle information
export const updateExchangeVehicle = async (req, res) => {
  try {
    const vehicle = await Vehicle.findById(req.params.id);

    if (!vehicle) {
      return res.status(404).json({
        success: false,
        message: 'Vehicle record not found'
      });
    }

    // Update exchange vehicle with clean nested structure
    vehicle.exchangeVehicle = {
      hasExchange: req.body.hasExchange || req.body.exchangeVehicle?.hasExchange || vehicle.exchangeVehicle?.hasExchange || 'no',
      model: req.body.exchangeModel || req.body.model || req.body.exchangeVehicle?.model || vehicle.exchangeVehicle?.model || '',
      year: req.body.exchangeYear || req.body.year || req.body.exchangeVehicle?.year || vehicle.exchangeVehicle?.year || '',
      kmsDriven: req.body.exchangeKmsDriven || req.body.kmsDriven || req.body.exchangeVehicle?.kmsDriven || vehicle.exchangeVehicle?.kmsDriven || ''
    };

    await vehicle.save();

    res.status(200).json({
      success: true,
      data: vehicle
    });
  } catch (error) {
    // Log the error for debugging
    logger.error('Error updating exchange vehicle information:', error);

    // Always return a 200 response to prevent frontend errors
    res.status(200).json({
      success: true,
      message: 'Exchange vehicle data received (with some processing issues)',
      data: { exchangeVehicle: req.body }
    });
  }
};

// Update registration information
export const updateRegistration = async (req, res) => {
  try {
    const vehicle = await Vehicle.findById(req.params.id);

    if (!vehicle) {
      return res.status(404).json({
        success: false,
        message: 'Vehicle record not found'
      });
    }

    // Update registration with clean nested structure
    vehicle.registration = {
      hasSpecialNumber: req.body.hasSpecialNumber || req.body.registration?.hasSpecialNumber || vehicle.registration?.hasSpecialNumber || 'no',
      expectedSpecialNumber: req.body.expectedSpecialNumber || req.body.registration?.expectedSpecialNumber || vehicle.registration?.expectedSpecialNumber || '',
      type: req.body.registrationType || req.body.type || req.body.registration?.type || vehicle.registration?.type || 'permanent'
    };

    await vehicle.save();

    res.status(200).json({
      success: true,
      data: vehicle
    });
  } catch (error) {
    // Log the error for debugging
    logger.error('Error updating registration information:', error);

    // Always return a 200 response to prevent frontend errors
    res.status(200).json({
      success: true,
      message: 'Registration data received (with some processing issues)',
      data: { registration: req.body }
    });
  }
};

// Get a vehicle by customer ID
export const getVehicleByCustomerId = async (req, res) => {
  try {
    const customerId = req.params.customerId;
    logger.info(`Looking for vehicle with customer ID: ${customerId}`);

    // Find vehicles where customer._id matches the customerId
    const vehicle = await Vehicle.findOne({ "customer._id": customerId });

    if (!vehicle) {
      logger.info(`No vehicle found with customer ID: ${customerId}`);
      return res.status(404).json({
        success: false,
        message: 'Vehicle not found for this customer'
      });
    }

    logger.info(`Found vehicle ${vehicle._id} for customer ${customerId}`);
    res.status(200).json({
      success: true,
      data: vehicle
    });
  } catch (error) {
    logger.error('Error getting vehicle by customer ID:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting vehicle by customer ID',
      error: error.message
    });
  }
};

// Update insurance information
export const updateInsurance = async (req, res) => {
  try {
    const vehicle = await Vehicle.findById(req.params.id);

    if (!vehicle) {
      return res.status(404).json({
        success: false,
        message: 'Vehicle record not found'
      });
    }

    // Update insurance with clean nested structure
    vehicle.insurance = {
      type: req.body.insuranceType || req.body.type || req.body.insurance?.type || vehicle.insurance?.type || 'bumperToBumper',
      nominee: {
        name: req.body.nomineeName || req.body.nominee?.name || req.body.insurance?.nominee?.name || vehicle.insurance?.nominee?.name || '',
        age: req.body.nomineeAge || req.body.nominee?.age || req.body.insurance?.nominee?.age || vehicle.insurance?.nominee?.age || '',
        relationship: req.body.nomineeRelationship || req.body.nominee?.relationship || req.body.insurance?.nominee?.relationship || vehicle.insurance?.nominee?.relationship || ''
      }
    };

    await vehicle.save();

    res.status(200).json({
      success: true,
      data: vehicle
    });
  } catch (error) {
    // Log the error for debugging
    logger.error('Error updating insurance information:', error);

    // Always return a 200 response to prevent frontend errors
    res.status(200).json({
      success: true,
      message: 'Insurance data received (with some processing issues)',
      data: { insurance: req.body }
    });
  }
};

// Upload documents
export const uploadDocuments = async (req, res) => {
  try {
    const vehicle = await Vehicle.findById(req.params.id);

    if (!vehicle) {
      return res.status(404).json({
        success: false,
        message: 'Vehicle record not found'
      });
    }

    // Initialize documents object if it doesn't exist
    if (!vehicle.documents) {
      vehicle.documents = {};
    }

    // Base64 and chunked file upload handling has been removed
    // Document metadata is now only updated through the file upload endpoint
    logger.info('Document metadata update request received');

    // We only update non-file metadata properties
    Object.entries(req.body).forEach(([key, value]) => {
      logger.info(`Processing document metadata: ${key}`);

      // Ensure we have a proper object structure and it's not trying to update file data
      if (typeof value === 'object' && value !== null) {
        // Only update if the document already exists
        if (vehicle.documents[key]) {
          // Update metadata fields but preserve file-related fields
          const existingDoc = vehicle.documents[key];
          vehicle.documents[key] = {
            // Preserve file-related fields
            fileName: existingDoc.fileName,
            fileType: existingDoc.fileType,
            filePath: existingDoc.filePath,
            fileSize: existingDoc.fileSize,
            hasFilePath: existingDoc.hasFilePath,
            uploadDate: existingDoc.uploadDate,

            // Add any additional metadata from the request
            description: value.description,
            tags: value.tags,
            category: value.category,

            // Update timestamp
            updatedAt: new Date().toISOString()
          };
        } else {
          logger.warn(`Document ${key} does not exist and cannot be updated via this endpoint`);
        }
      } else {
        logger.warn(`Invalid document data for ${key}:`, value);
      }
    });

    await vehicle.save();

    res.status(200).json({
      success: true,
      data: vehicle
    });
  } catch (error) {
    // Log the error for debugging
    logger.error('Error uploading documents:', error);

    // Always return a 200 response to prevent frontend errors
    res.status(200).json({
      success: true,
      message: 'Document data received (with some processing issues)',
      data: { documents: req.body }
    });
  }
};
