import Admin from '../models/Admin.js';
import { isMongoConnected } from '../config/db.js';
import crypto from 'crypto';
import logger from '../utils/logger.js';

/**
 * Register a new admin
 * @route POST /api/auth/register
 * @access Public
 */
export const register = async (req, res) => {
  try {
    if (!isMongoConnected()) {
      return res.status(503).json({
        success: false,
        message: 'Database connection error'
      });
    }

    const { name, email, password, role } = req.body;

    // Check if admin already exists
    const adminExists = await Admin.findOne({ email });
    if (adminExists) {
      return res.status(400).json({
        success: false,
        message: 'Admin with this email already exists'
      });
    }

    // Create new admin
    const admin = await Admin.create({
      name,
      email,
      password,
      role: role || 'admin' // Default to admin if role not provided
    });

    // Generate token
    const token = admin.generateAuthToken();

    // Set cookie options
    const cookieOptions = {
      expires: new Date(
        Date.now() + (process.env.JWT_COOKIE_EXPIRE || 1) * 24 * 60 * 60 * 1000
      ),
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production'
    };

    // Send response with cookie
    res
      .status(201)
      .cookie('token', token, cookieOptions)
      .json({
        success: true,
        token,
        admin: {
          id: admin._id,
          name: admin.name,
          email: admin.email,
          role: admin.role
        }
      });

    logger.logAuth('register', admin, { requestId: req.id });

  } catch (error) {
    logger.logError(error, {
      operation: 'register',
      email: req.body.email,
      requestId: req.id
    });

    res.status(500).json({
      success: false,
      message: 'Error registering admin',
      error: error.message,
      requestId: req.id
    });
  }
};

/**
 * Login admin - Real authentication
 * @route POST /api/auth/login
 * @access Public
 */
export const login = async (req, res) => {
  try {
    if (!isMongoConnected()) {
      return res.status(503).json({
        success: false,
        message: 'Database connection error'
      });
    }

    const { email, password } = req.body;

    // Validate email and password
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Please provide email and password'
      });
    }

    // Find admin by email and include password in the result
    const admin = await Admin.findOne({ email }).select('+password');

    // Check if admin exists
    if (!admin) {
      logger.warn('Login attempt with invalid email', {
        email,
        ip: req.ip,
        requestId: req.id
      });
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    // Check if password matches
    const isMatch = await admin.comparePassword(password);
    if (!isMatch) {
      logger.warn('Login attempt with invalid password', {
        email,
        adminId: admin._id,
        ip: req.ip,
        requestId: req.id
      });
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    // Update last login time
    admin.lastLogin = new Date();
    await admin.save();

    // Generate token
    const token = admin.generateAuthToken();

    // Set cookie options
    const cookieOptions = {
      expires: new Date(
        Date.now() + (process.env.JWT_COOKIE_EXPIRE || 1) * 24 * 60 * 60 * 1000
      ),
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production'
    };

    // Send response with cookie
    res
      .status(200)
      .cookie('token', token, cookieOptions)
      .json({
        success: true,
        token,
        admin: {
          id: admin._id,
          name: admin.name,
          email: admin.email,
          role: admin.role
        }
      });

    logger.logAuth('login', admin, {
      ip: req.ip,
      requestId: req.id
    });

  } catch (error) {
    logger.logError(error, {
      operation: 'login',
      email: req.body.email,
      ip: req.ip,
      requestId: req.id
    });

    res.status(500).json({
      success: false,
      message: 'Error logging in',
      error: error.message,
      requestId: req.id
    });
  }
};

/**
 * Logout admin
 * @route POST /api/auth/logout
 * @access Private
 */
export const logout = (req, res) => {
  logger.logAuth('logout', req.admin, {
    ip: req.ip,
    requestId: req.id
  });

  // Clear cookie
  res.clearCookie('token');

  res.status(200).json({
    success: true,
    message: 'Logged out successfully'
  });
};

/**
 * Get current admin profile
 * @route GET /api/auth/me
 * @access Private
 */
export const getMe = async (req, res) => {
  try {
    if (!isMongoConnected()) {
      return res.status(503).json({
        success: false,
        message: 'Database connection error'
      });
    }

    // Get admin from middleware
    const admin = await Admin.findById(req.admin.id);

    if (!admin) {
      return res.status(404).json({
        success: false,
        message: 'Admin not found'
      });
    }

    res.status(200).json({
      success: true,
      data: admin
    });
  } catch (error) {
    logger.logError(error, {
      operation: 'getMe',
      adminId: req.admin?.id,
      requestId: req.id
    });

    res.status(500).json({
      success: false,
      message: 'Error getting admin profile',
      error: error.message,
      requestId: req.id
    });
  }
};
