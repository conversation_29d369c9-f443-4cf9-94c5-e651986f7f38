import React, { useState, useEffect } from 'react';

interface SuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  message?: string;
  showIndefinitely?: boolean; // New prop to control auto-close behavior
}

export const SuccessModal: React.FC<SuccessModalProps> = ({
  isOpen,
  onClose,
  title = "Form Submitted Successfully",
  message = "Your vehicle has been successfully registered.",
  showIndefinitely = false
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [checkmarkAnimationComplete, setCheckmarkAnimationComplete] = useState(false);

  useEffect(() => {
    if (isOpen) {
      // Small delay to trigger the fade-in animation
      setTimeout(() => {
        setIsVisible(true);
      }, 50);

      // Set checkmark animation to complete after animation duration
      // Use a longer duration for a slower, more satisfying animation
      setTimeout(() => {
        setCheckmarkAnimationComplete(true);
      }, 2500); // Slower animation (2.5 seconds to match the tailwind animation)

      // Only auto-close if showIndefinitely is false
      if (!showIndefinitely) {
        const timer = setTimeout(() => {
          setIsVisible(false);
          setTimeout(() => {
            onClose();
          }, 300);
        }, 5000);

        return () => clearTimeout(timer);
      }
    } else {
      setIsVisible(false);
      setCheckmarkAnimationComplete(false);
    }
  }, [isOpen, onClose, showIndefinitely]);

  if (!isOpen) return null;

  return (
    <div
      className={`fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-70 backdrop-blur-sm transition-opacity duration-300 ${
        isVisible ? 'opacity-100' : 'opacity-0'
      }`}
    >
      <div
        className={`bg-[#e9e9e9] rounded-lg shadow-xl max-w-md w-[90%] max-w-[400px] text-center p-6 transition-all duration-300 transform ${
          isVisible ? 'opacity-100 scale-100 translate-y-0' : 'opacity-0 scale-95 translate-y-4'
        }`}
      >
        {/* Centered Content */}
        <div className="flex flex-col items-center">
          {/* Checkmark Circle */}
          <div className="mx-auto mb-6">
            <div className="w-20 h-20 bg-white rounded-full flex items-center justify-center shadow-md animate-circle">
              <svg
                className="w-10 h-10 text-[#640064]"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M5 13L9 17L19 7"
                  stroke="currentColor"
                  strokeWidth="2.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="animate-checkmark"
                  style={{
                    strokeDasharray: 40,
                    strokeDashoffset: checkmarkAnimationComplete ? 0 : 40
                  }}
                />
              </svg>
            </div>
          </div>

          {/* Title */}
          <h2 className="text-gray-800 text-xl font-medium mb-2">{title}</h2>

          {/* Message */}
          <p className="text-gray-600">{message}</p>
        </div>
      </div>
    </div>
  );
};

export default SuccessModal;
