import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer,
  LineChart, Line, PieChart, Pie, Cell, Area, AreaChart
} from 'recharts';
import { 
  Download, FileText, Calendar, BarChart3, RefreshCw, Eye, 
  TrendingUp, Users, Car, MapPin, Clock
} from 'lucide-react';

interface CSVData {
  timestamp: string;
  vehicleId: string;
  customerName: string;
  customerPhone: string;
  vehicleModel: string;
  financeMode: string;
  registrationType: string;
  status: string;
  customerCity: string;
  customerState: string;
  createdAt: string;
  completedAt: string;
}

interface AnalyticsData {
  date: string;
  hour: number;
  vehicleModel: string;
  financeMode: string;
  customerCity: string;
  customerState: string;
  status: string;
}

const CSVDataVisualization: React.FC = () => {
  const [csvData, setCsvData] = useState<CSVData[]>([]);
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedFile, setSelectedFile] = useState<string>('');
  const [availableFiles, setAvailableFiles] = useState<string[]>([]);
  const [viewMode, setViewMode] = useState<'table' | 'charts'>('table');

  const API_BASE = process.env.REACT_APP_API_URL || 'http://localhost:10000';

  useEffect(() => {
    fetchAvailableFiles();
  }, []);

  const fetchAvailableFiles = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE}/api/csv/files?type=daily`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      const data = await response.json();
      if (data.success) {
        const csvFiles = data.data.files.map((file: any) => file.name);
        setAvailableFiles(csvFiles);
        if (csvFiles.length > 0) {
          setSelectedFile(csvFiles[0]);
        }
      }
    } catch (error) {
      console.error('Error fetching files:', error);
    }
  };

  const loadCSVData = async (filename: string) => {
    if (!filename) return;
    
    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      
      // Preview the CSV file content
      const response = await fetch(`${API_BASE}/api/csv/preview/${filename}?type=daily&rows=1000`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      const data = await response.json();
      if (data.success) {
        // Parse CSV content
        const lines = data.data.content.split('\n');
        const headers = lines[0].split(',');
        const rows = lines.slice(1).filter(line => line.trim());
        
        const parsedData = rows.map(row => {
          const values = row.split(',');
          const obj: any = {};
          headers.forEach((header, index) => {
            obj[header.trim()] = values[index]?.trim() || '';
          });
          return obj;
        });
        
        if (filename.includes('summary') || filename.includes('detailed')) {
          setCsvData(parsedData);
        } else if (filename.includes('analytics')) {
          setAnalyticsData(parsedData);
        }
      }
    } catch (error) {
      console.error('Error loading CSV data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (selectedFile) {
      loadCSVData(selectedFile);
    }
  }, [selectedFile]);

  // Data processing for charts
  const getVehicleModelStats = () => {
    const modelCounts: { [key: string]: number } = {};
    csvData.forEach(row => {
      const model = row.vehicleModel || 'Unknown';
      modelCounts[model] = (modelCounts[model] || 0) + 1;
    });
    return Object.entries(modelCounts).map(([name, value]) => ({ name, value }));
  };

  const getFinanceModeStats = () => {
    const financeCounts: { [key: string]: number } = {};
    csvData.forEach(row => {
      const mode = row.financeMode || 'Unknown';
      financeCounts[mode] = (financeCounts[mode] || 0) + 1;
    });
    return Object.entries(financeCounts).map(([name, value]) => ({ name, value }));
  };

  const getStatusStats = () => {
    const statusCounts: { [key: string]: number } = {};
    csvData.forEach(row => {
      const status = row.status || 'Unknown';
      statusCounts[status] = (statusCounts[status] || 0) + 1;
    });
    return Object.entries(statusCounts).map(([name, value]) => ({ name, value }));
  };

  const getCityStats = () => {
    const cityCounts: { [key: string]: number } = {};
    csvData.forEach(row => {
      const city = row.customerCity || 'Unknown';
      cityCounts[city] = (cityCounts[city] || 0) + 1;
    });
    return Object.entries(cityCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([name, value]) => ({ name, value }));
  };

  const getHourlyStats = () => {
    const hourlyCounts: { [key: number]: number } = {};
    csvData.forEach(row => {
      if (row.createdAt) {
        const hour = new Date(row.createdAt).getHours();
        hourlyCounts[hour] = (hourlyCounts[hour] || 0) + 1;
      }
    });
    return Array.from({ length: 24 }, (_, hour) => ({
      hour: `${hour}:00`,
      count: hourlyCounts[hour] || 0
    }));
  };

  const downloadCSV = async (filename: string) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE}/api/csv/download/${filename}?type=daily`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('Error downloading file:', error);
    }
  };

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-800">CSV Data Visualization</h2>
          <p className="text-gray-600">View and analyze your form data stored in CSV files</p>
        </div>
        <div className="flex gap-2">
          <button
            onClick={() => setViewMode(viewMode === 'table' ? 'charts' : 'table')}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-2"
          >
            {viewMode === 'table' ? <BarChart3 className="w-4 h-4" /> : <FileText className="w-4 h-4" />}
            {viewMode === 'table' ? 'Show Charts' : 'Show Table'}
          </button>
          <button
            onClick={fetchAvailableFiles}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center gap-2"
          >
            <RefreshCw className="w-4 h-4" />
            Refresh
          </button>
        </div>
      </div>

      {/* File Selector */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <div className="flex items-center gap-4">
          <label className="text-sm font-medium text-gray-700">Select CSV File:</label>
          <select
            value={selectedFile}
            onChange={(e) => setSelectedFile(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Choose a file...</option>
            {availableFiles.map(file => (
              <option key={file} value={file}>{file}</option>
            ))}
          </select>
          {selectedFile && (
            <button
              onClick={() => downloadCSV(selectedFile)}
              className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-2"
            >
              <Download className="w-4 h-4" />
              Download
            </button>
          )}
        </div>
        
        {/* File Location Info */}
        <div className="mt-3 p-3 bg-blue-50 rounded border-l-4 border-blue-400">
          <h4 className="font-medium text-blue-800">📁 CSV File Locations:</h4>
          <ul className="text-sm text-blue-700 mt-1 space-y-1">
            <li><strong>Daily Files:</strong> <code>backend/csv-exports/daily/</code></li>
            <li><strong>Archive Files:</strong> <code>backend/csv-exports/archive/YYYY-MM-DD/</code></li>
            <li><strong>Backup Files:</strong> <code>backend/csv-exports/backups/</code></li>
          </ul>
          <p className="text-sm text-blue-600 mt-2">
            💡 <strong>No special permissions needed!</strong> Files are automatically generated and can be downloaded directly.
          </p>
        </div>
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-12">
          <RefreshCw className="w-8 h-8 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-600">Loading CSV data...</span>
        </div>
      ) : csvData.length === 0 ? (
        <div className="text-center py-12 text-gray-500">
          <FileText className="w-16 h-16 mx-auto mb-4 opacity-50" />
          <p>No data available. Select a CSV file to view data.</p>
        </div>
      ) : (
        <>
          {/* Summary Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="flex items-center gap-2">
                <Users className="w-5 h-5 text-blue-600" />
                <h3 className="text-sm font-medium text-blue-800">Total Records</h3>
              </div>
              <p className="text-2xl font-bold text-blue-600">{csvData.length}</p>
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="flex items-center gap-2">
                <Car className="w-5 h-5 text-green-600" />
                <h3 className="text-sm font-medium text-green-800">Vehicle Models</h3>
              </div>
              <p className="text-2xl font-bold text-green-600">
                {new Set(csvData.map(d => d.vehicleModel)).size}
              </p>
            </div>
            <div className="bg-purple-50 p-4 rounded-lg">
              <div className="flex items-center gap-2">
                <MapPin className="w-5 h-5 text-purple-600" />
                <h3 className="text-sm font-medium text-purple-800">Cities</h3>
              </div>
              <p className="text-2xl font-bold text-purple-600">
                {new Set(csvData.map(d => d.customerCity)).size}
              </p>
            </div>
            <div className="bg-orange-50 p-4 rounded-lg">
              <div className="flex items-center gap-2">
                <Clock className="w-5 h-5 text-orange-600" />
                <h3 className="text-sm font-medium text-orange-800">Completed</h3>
              </div>
              <p className="text-2xl font-bold text-orange-600">
                {csvData.filter(d => d.status === 'completed').length}
              </p>
            </div>
          </div>

          {viewMode === 'table' ? (
            /* Table View */
            <div className="overflow-x-auto">
              <table className="w-full border-collapse border border-gray-300">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="border border-gray-300 px-3 py-2 text-left text-xs font-medium">Customer</th>
                    <th className="border border-gray-300 px-3 py-2 text-left text-xs font-medium">Phone</th>
                    <th className="border border-gray-300 px-3 py-2 text-left text-xs font-medium">Vehicle</th>
                    <th className="border border-gray-300 px-3 py-2 text-left text-xs font-medium">Finance</th>
                    <th className="border border-gray-300 px-3 py-2 text-left text-xs font-medium">City</th>
                    <th className="border border-gray-300 px-3 py-2 text-left text-xs font-medium">Status</th>
                    <th className="border border-gray-300 px-3 py-2 text-left text-xs font-medium">Created</th>
                  </tr>
                </thead>
                <tbody>
                  {csvData.slice(0, 100).map((row, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="border border-gray-300 px-3 py-2 text-sm">{row.customerName}</td>
                      <td className="border border-gray-300 px-3 py-2 text-sm">{row.customerPhone}</td>
                      <td className="border border-gray-300 px-3 py-2 text-sm">{row.vehicleModel}</td>
                      <td className="border border-gray-300 px-3 py-2 text-sm">{row.financeMode}</td>
                      <td className="border border-gray-300 px-3 py-2 text-sm">{row.customerCity}</td>
                      <td className="border border-gray-300 px-3 py-2 text-sm">
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          row.status === 'completed' ? 'bg-green-100 text-green-800' :
                          row.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {row.status}
                        </span>
                      </td>
                      <td className="border border-gray-300 px-3 py-2 text-sm">
                        {row.createdAt ? new Date(row.createdAt).toLocaleDateString() : '-'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              {csvData.length > 100 && (
                <p className="text-center text-gray-500 mt-4">
                  Showing first 100 records of {csvData.length} total records
                </p>
              )}
            </div>
          ) : (
            /* Charts View */
            <div className="space-y-8">
              {/* Vehicle Models Chart */}
              <div className="bg-white p-6 rounded-lg border">
                <h3 className="text-lg font-semibold mb-4">Vehicle Models Distribution</h3>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={getVehicleModelStats()}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="value" fill="#0088FE" />
                  </BarChart>
                </ResponsiveContainer>
              </div>

              {/* Finance Mode & Status Charts */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-white p-6 rounded-lg border">
                  <h3 className="text-lg font-semibold mb-4">Finance Mode</h3>
                  <ResponsiveContainer width="100%" height={250}>
                    <PieChart>
                      <Pie
                        data={getFinanceModeStats()}
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label
                      >
                        {getFinanceModeStats().map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>

                <div className="bg-white p-6 rounded-lg border">
                  <h3 className="text-lg font-semibold mb-4">Status Distribution</h3>
                  <ResponsiveContainer width="100%" height={250}>
                    <PieChart>
                      <Pie
                        data={getStatusStats()}
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        fill="#82ca9d"
                        dataKey="value"
                        label
                      >
                        {getStatusStats().map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </div>

              {/* Top Cities Chart */}
              <div className="bg-white p-6 rounded-lg border">
                <h3 className="text-lg font-semibold mb-4">Top 10 Cities</h3>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={getCityStats()} layout="horizontal">
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" />
                    <YAxis dataKey="name" type="category" width={100} />
                    <Tooltip />
                    <Bar dataKey="value" fill="#00C49F" />
                  </BarChart>
                </ResponsiveContainer>
              </div>

              {/* Hourly Activity Chart */}
              <div className="bg-white p-6 rounded-lg border">
                <h3 className="text-lg font-semibold mb-4">Hourly Activity Pattern</h3>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={getHourlyStats()}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="hour" />
                    <YAxis />
                    <Tooltip />
                    <Area type="monotone" dataKey="count" stroke="#FFBB28" fill="#FFBB28" fillOpacity={0.6} />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default CSVDataVisualization;
