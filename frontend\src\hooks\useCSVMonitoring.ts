import { useState, useEffect, useCallback } from 'react';

interface CSVStats {
  queueSize: number;
  isProcessing: boolean;
  batchSize: number;
  batchTimeout: number;
  retryAttempts: number;
  uptime: number;
  memoryUsage: {
    rss: number;
    heapTotal: number;
    heapUsed: number;
    external: number;
  };
}

interface CSVMonitoringOptions {
  enabled?: boolean;
  interval?: number; // in milliseconds
  onQueueSizeChange?: (size: number) => void;
  onProcessingStateChange?: (isProcessing: boolean) => void;
  onError?: (error: Error) => void;
}

/**
 * Custom hook for real-time CSV processing monitoring
 */
export const useCSVMonitoring = (options: CSVMonitoringOptions = {}) => {
  const {
    enabled = true,
    interval = 10000, // 10 seconds default
    onQueueSizeChange,
    onProcessingStateChange,
    onError
  } = options;

  const [stats, setStats] = useState<CSVStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  const API_BASE = process.env.REACT_APP_API_URL || 'http://localhost:10000';

  const fetchStats = useCallback(async () => {
    if (!enabled) return;

    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${API_BASE}/api/csv/stats`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.message || 'Failed to fetch CSV stats');
      }

      const newStats = data.data;
      
      // Trigger callbacks if values changed
      if (stats) {
        if (stats.queueSize !== newStats.queueSize && onQueueSizeChange) {
          onQueueSizeChange(newStats.queueSize);
        }
        
        if (stats.isProcessing !== newStats.isProcessing && onProcessingStateChange) {
          onProcessingStateChange(newStats.isProcessing);
        }
      }

      setStats(newStats);
      setLastUpdate(new Date());
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      
      if (onError && err instanceof Error) {
        onError(err);
      }
      
      console.error('CSV monitoring error:', err);
    } finally {
      setLoading(false);
    }
  }, [enabled, API_BASE, stats, onQueueSizeChange, onProcessingStateChange, onError]);

  // Force process current batch
  const forceProcessBatch = useCallback(async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${API_BASE}/api/csv/force-process`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.message || 'Failed to force process batch');
      }

      // Refresh stats after processing
      setTimeout(fetchStats, 1000);
      
      return data.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      throw err;
    }
  }, [API_BASE, fetchStats]);

  // Trigger bulk export
  const triggerBulkExport = useCallback(async (options: {
    startDate?: string;
    endDate?: string;
    status?: string;
    limit?: number;
  } = {}) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const params = new URLSearchParams();
      if (options.startDate) params.append('startDate', options.startDate);
      if (options.endDate) params.append('endDate', options.endDate);
      if (options.status) params.append('status', options.status);
      if (options.limit) params.append('limit', options.limit.toString());

      const response = await fetch(`${API_BASE}/api/csv/bulk-export?${params}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.message || 'Failed to trigger bulk export');
      }

      // Refresh stats after triggering export
      setTimeout(fetchStats, 1000);
      
      return data.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      throw err;
    }
  }, [API_BASE, fetchStats]);

  // Export single vehicle
  const exportVehicle = useCallback(async (vehicleId: string, operation = 'manual') => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${API_BASE}/api/csv/export/${vehicleId}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ operation })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.message || 'Failed to export vehicle');
      }

      // Refresh stats after export
      setTimeout(fetchStats, 1000);
      
      return data.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      throw err;
    }
  }, [API_BASE, fetchStats]);

  // Set up polling interval
  useEffect(() => {
    if (!enabled) return;

    // Initial fetch
    fetchStats();

    // Set up interval
    const intervalId = setInterval(fetchStats, interval);

    return () => {
      clearInterval(intervalId);
    };
  }, [enabled, interval, fetchStats]);

  // Calculate derived values
  const isHealthy = stats ? stats.queueSize < 100 && !error : false;
  const queueStatus = stats ? 
    stats.queueSize === 0 ? 'empty' :
    stats.queueSize < 10 ? 'low' :
    stats.queueSize < 50 ? 'medium' :
    'high' : 'unknown';

  const memoryUsagePercent = stats ? 
    Math.round((stats.memoryUsage.heapUsed / stats.memoryUsage.heapTotal) * 100) : 0;

  return {
    // State
    stats,
    loading,
    error,
    lastUpdate,
    
    // Derived values
    isHealthy,
    queueStatus,
    memoryUsagePercent,
    
    // Actions
    fetchStats,
    forceProcessBatch,
    triggerBulkExport,
    exportVehicle,
    
    // Utilities
    clearError: () => setError(null)
  };
};

export default useCSVMonitoring;
