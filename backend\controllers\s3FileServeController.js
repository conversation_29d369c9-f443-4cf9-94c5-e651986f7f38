/**
 * S3 File Serving Controller - Cloud Storage File Access
 * Handles file serving from Amazon S3 with presigned URLs
 */

import Vehicle from '../models/Vehicle.js';
import s3Service from '../services/s3Service.js';
import logger from '../utils/logger.js';

/**
 * Serve file from S3 using presigned URLs
 */
export const serveFile = async (req, res) => {
  try {
    // Handle OPTIONS preflight request
    if (req.method === 'OPTIONS') {
      const requestOrigin = req.headers.origin;
      const allowedOrigins = ['http://localhost:5173', 'http://localhost:3000', 'https://q5-x-preview.vercel.app'];

      if (allowedOrigins.includes(requestOrigin)) {
        res.setHeader('Access-Control-Allow-Origin', requestOrigin);
      } else {
        res.setHeader('Access-Control-Allow-Origin', 'http://localhost:5173');
      }

      res.setHeader('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');
      res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
      res.setHeader('Access-Control-Allow-Credentials', 'true');
      res.setHeader('Cross-Origin-Resource-Policy', 'cross-origin');
      return res.status(200).end();
    }

    const { id: vehicleId, documentType } = req.params;
    const { download } = req.query;

    logger.info(`Serving S3 file for vehicle ${vehicleId}, document type: ${documentType}, download: ${download}`);

    // Get vehicle record from database
    const vehicle = await Vehicle.findById(vehicleId);
    if (!vehicle) {
      return res.status(404).json({
        success: false,
        message: 'Vehicle not found'
      });
    }

    // Check if document exists
    if (!vehicle.documents || !vehicle.documents[documentType]) {
      return res.status(404).json({
        success: false,
        message: 'Document not found'
      });
    }

    const fileInfo = vehicle.documents[documentType];

    // Check if file is stored in S3
    if (fileInfo.storageType !== 's3' || !fileInfo.s3Key) {
      return res.status(404).json({
        success: false,
        message: 'File not found in S3 storage'
      });
    }

    // Initialize S3 service
    const s3Initialized = await s3Service.initialize();
    if (!s3Initialized) {
      return res.status(500).json({
        success: false,
        message: 'Failed to initialize S3 service'
      });
    }

    // Check if file exists in S3
    const fileExists = await s3Service.fileExists(fileInfo.s3Key);
    if (!fileExists) {
      return res.status(404).json({
        success: false,
        message: 'File not found in S3 bucket'
      });
    }

    // Get the file from S3 and stream it through our backend
    const fileStream = await s3Service.getFileStream(fileInfo.s3Key);

    // Set appropriate headers for CORS - use specific origin instead of *
    const requestOrigin = req.headers.origin;
    const allowedOrigins = ['http://localhost:5173', 'http://localhost:3000', 'https://q5-x-preview.vercel.app'];

    if (allowedOrigins.includes(requestOrigin)) {
      res.setHeader('Access-Control-Allow-Origin', requestOrigin);
    } else {
      res.setHeader('Access-Control-Allow-Origin', 'http://localhost:5173'); // Default to dev origin
    }

    res.setHeader('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    res.setHeader('Access-Control-Allow-Credentials', 'true');
    res.setHeader('Cross-Origin-Resource-Policy', 'cross-origin');

    // Set content headers
    res.setHeader('Content-Type', fileInfo.fileType || 'application/octet-stream');

    // Handle download vs view
    if (download === 'true') {
      res.setHeader('Content-Disposition', `attachment; filename="${fileInfo.fileName}"`);
    } else {
      res.setHeader('Content-Disposition', `inline; filename="${fileInfo.fileName}"`);
    }

    // Set cache headers
    res.setHeader('Cache-Control', 'public, max-age=3600');

    // Stream the file
    fileStream.pipe(res);

  } catch (error) {
    logger.error('Error serving file from S3:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to serve file',
      error: error.message
    });
  }
};

/**
 * Get file information and presigned URL (JSON response)
 */
export const getFileInfo = async (req, res) => {
  try {
    const { id: vehicleId, documentType } = req.params;

    logger.info(`Getting S3 file info for vehicle ${vehicleId}, document type: ${documentType}`);

    // Get vehicle record from database
    const vehicle = await Vehicle.findById(vehicleId);
    if (!vehicle) {
      return res.status(404).json({
        success: false,
        message: 'Vehicle not found'
      });
    }

    // Check if document exists
    if (!vehicle.documents || !vehicle.documents[documentType]) {
      return res.status(404).json({
        success: false,
        message: 'Document not found'
      });
    }

    const fileInfo = vehicle.documents[documentType];

    // Check if file is stored in S3
    if (fileInfo.storageType !== 's3' || !fileInfo.s3Key) {
      return res.status(404).json({
        success: false,
        message: 'File not found in S3 storage'
      });
    }

    // Initialize S3 service
    const s3Initialized = await s3Service.initialize();
    if (!s3Initialized) {
      return res.status(500).json({
        success: false,
        message: 'Failed to initialize S3 service'
      });
    }

    // Check if file exists in S3
    const fileExists = await s3Service.fileExists(fileInfo.s3Key);
    if (!fileExists) {
      return res.status(404).json({
        success: false,
        message: 'File not found in S3 bucket'
      });
    }

    // Get file metadata from S3
    const metadata = await s3Service.getFileMetadata(fileInfo.s3Key);

    // Generate presigned URL (valid for 1 hour)
    const presignedUrl = await s3Service.getPresignedUrl(fileInfo.s3Key, 3600);

    res.json({
      success: true,
      data: {
        fileName: fileInfo.fileName,
        fileType: fileInfo.fileType,
        fileSize: fileInfo.fileSize,
        uploadDate: fileInfo.uploadDate,
        s3Key: fileInfo.s3Key,
        presignedUrl: presignedUrl,
        metadata: {
          contentType: metadata.contentType,
          contentLength: metadata.contentLength,
          lastModified: metadata.lastModified
        },
        urlExpiresIn: 3600 // seconds
      }
    });

  } catch (error) {
    logger.error('Error getting S3 file info:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get file information',
      error: error.message
    });
  }
};

/**
 * Delete file from S3
 */
export const deleteFile = async (req, res) => {
  try {
    const { id: vehicleId, documentType } = req.params;

    logger.info(`Deleting S3 file for vehicle ${vehicleId}, document type: ${documentType}`);

    // Get vehicle record from database
    const vehicle = await Vehicle.findById(vehicleId);
    if (!vehicle) {
      return res.status(404).json({
        success: false,
        message: 'Vehicle not found'
      });
    }

    // Check if document exists
    if (!vehicle.documents || !vehicle.documents[documentType]) {
      return res.status(404).json({
        success: false,
        message: 'Document not found'
      });
    }

    const fileInfo = vehicle.documents[documentType];

    // Check if file is stored in S3
    if (fileInfo.storageType !== 's3' || !fileInfo.s3Key) {
      return res.status(404).json({
        success: false,
        message: 'File not found in S3 storage'
      });
    }

    // Initialize S3 service
    const s3Initialized = await s3Service.initialize();
    if (!s3Initialized) {
      return res.status(500).json({
        success: false,
        message: 'Failed to initialize S3 service'
      });
    }

    // Delete file from S3
    await s3Service.deleteFile(fileInfo.s3Key);

    // Remove document reference from vehicle
    delete vehicle.documents[documentType];
    await vehicle.save();

    res.json({
      success: true,
      message: 'File deleted successfully',
      data: {
        vehicleId: vehicleId,
        documentType: documentType,
        deletedS3Key: fileInfo.s3Key
      }
    });

  } catch (error) {
    logger.error('Error deleting S3 file:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete file',
      error: error.message
    });
  }
};
