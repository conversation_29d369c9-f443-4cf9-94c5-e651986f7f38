/**
 * Logging configuration for the application
 * Centralizes all logging-related settings
 */

export const loggingConfig = {
  // Log levels in order of priority
  levels: {
    error: 0,
    warn: 1,
    info: 2,
    http: 3,
    debug: 4
  },

  // Colors for each log level
  colors: {
    error: 'red',
    warn: 'yellow',
    info: 'green',
    http: 'magenta',
    debug: 'blue'
  },

  // File rotation settings
  rotation: {
    maxSize: '20m',        // Maximum file size before rotation
    maxFiles: '30d',       // Keep logs for 30 days
    errorMaxFiles: '14d',  // Keep error logs for 14 days
    httpMaxFiles: '7d',    // Keep HTTP logs for 7 days
    zippedArchive: true    // Compress old log files
  },

  // Log format settings
  format: {
    timestamp: 'YYYY-MM-DD HH:mm:ss',
    consoleTimestamp: 'HH:mm:ss'
  },

  // Environment-specific settings
  environments: {
    development: {
      level: 'debug',
      console: true,
      files: true
    },
    production: {
      level: 'info',
      console: false,
      files: true
    },
    test: {
      level: 'error',
      console: false,
      files: false
    }
  },

  // File paths (relative to logs directory)
  files: {
    error: 'error-%DATE%.log',
    combined: 'combined-%DATE%.log',
    http: 'http-%DATE%.log',
    exceptions: 'exceptions-%DATE%.log',
    rejections: 'rejections-%DATE%.log'
  }
};

export default loggingConfig;
