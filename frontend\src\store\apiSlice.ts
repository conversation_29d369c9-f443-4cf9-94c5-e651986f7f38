import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { vehicleAPI } from "../services/api";
import { RootState } from "./store";
import { clearState } from "../utils/localStorage";

interface ApiState {
  vehicleId: string | null;
  loading: boolean;
  error: string | null;
  success: boolean;
  formSubmitted: boolean;
  storageType: 'mongodb' | 'memory' | 'local' | null;
  retryCount: number;
  offlineMode: boolean;
  submissionProgress: number; // Track submission progress percentage (0-100)
  submissionStatus: string; // Status message for the submission process
  duplicateCheck: {
    isChecking: boolean;
    isDuplicate: boolean;
    customer: any;
    error: string | null;
  };
}

const initialState: ApiState = {
  vehicleId: null,
  loading: false,
  error: null,
  success: false,
  formSubmitted: false,
  storageType: null,
  retryCount: 0,
  offlineMode: false,
  submissionProgress: 0,
  submissionStatus: '',
  duplicateCheck: {
    isChecking: false,
    isDuplicate: false,
    customer: null,
    error: null
  }
};

/**
 * Check for duplicate customers before creating vehicle
 */
export const checkDuplicate = createAsyncThunk(
  "api/checkDuplicate",
  async (data: { phone?: string; email?: string; vehicleId?: string }, { rejectWithValue }) => {
    try {
      const response = await vehicleAPI.checkDuplicate(data);
      return response.data;
    } catch (error: any) {
      const errorMessage = error.response?.data?.message ||
                          error.message ||
                          "Failed to check for duplicates";
      return rejectWithValue(errorMessage);
    }
  }
);

/**
 * Create a new vehicle record when the first form section is submitted
 * Includes retry logic and detailed error handling
 */
export const createVehicle = createAsyncThunk(
  "api/createVehicle",
  async (data: any, { rejectWithValue, dispatch, getState }) => {
    try {
      const response = await vehicleAPI.createVehicle(data);

      // Check if we got a response from the mock API (offline mode)
      const state = getState() as RootState;
      if (response.data.data._id.startsWith('mock_')) {
        dispatch({ type: 'api/setOfflineMode', payload: true });
      }

      return response.data;
    } catch (error: any) {
      // Handle duplicate customer error specifically
      if (error.response?.status === 409) {
        const errorData = error.response.data;
        return rejectWithValue({
          message: errorData.message,
          isDuplicate: true,
          customer: errorData.customer
        });
      }

      // Extract the error message
      const errorMessage = error.response?.data?.message ||
                          error.message ||
                          "Failed to create vehicle record";

      return rejectWithValue(errorMessage);
    }
  }
);

/**
 * Update a specific section of the form
 * Includes retry logic and detailed error handling
 */
export const updateFormSection = createAsyncThunk(
  "api/updateFormSection",
  async (
    { section, data }: { section: string; data: any },
    { getState, rejectWithValue, dispatch }
  ) => {
    try {
      const state = getState() as RootState;
      const vehicleId = state.api.vehicleId;
      const { offlineMode } = state.api;

      if (!vehicleId) {
        return rejectWithValue("No vehicle ID available");
      }

      let response;
      try {
        switch (section) {
          case "customer":
            response = await vehicleAPI.updateCustomerInfo(vehicleId, data);
            break;
          case "vehicleDetails":
            response = await vehicleAPI.updateVehicleDetails(vehicleId, data);
            break;
          case "exchangeVehicle":
            response = await vehicleAPI.updateExchangeVehicle(vehicleId, data);
            break;
          case "registration":
            response = await vehicleAPI.updateRegistration(vehicleId, data);
            break;
          case "insurance":
            response = await vehicleAPI.updateInsurance(vehicleId, data);
            break;
          case "documents":
            // Since we're now storing base64 data directly, we can just use the regular API
            // No need to use FormData or special file handling
            response = await vehicleAPI.uploadDocuments(vehicleId, data);
            break;
          case "combinedForms":
            // Handle the combined forms data (forms 2-5)
            // This is a special case where we update multiple sections at once

            // Extract the vehicle details
            const vehicleDetailsData = {
              model: data.vehicleDetails?.model || "",
              colour: data.vehicleDetails?.colour || "",
              financeMode: data.vehicleDetails?.financeMode || ""
            };

            // Extract exchange vehicle data
            const exchangeVehicleData = {
              hasExchange: data.hasExchange || "no",
              exchangeModel: data.exchangeModel || "",
              exchangeYear: data.exchangeYear || "",
              exchangeKmsDriven: data.exchangeKmsDriven || ""
            };

            // Extract registration data
            const registrationData = {
              hasSpecialNumber: data.hasSpecialNumber || "no",
              expectedSpecialNumber: data.expectedSpecialNumber || "",
              registrationType: data.registrationType || "permanent"
            };

            // Extract insurance data
            const insuranceData = {
              insuranceType: data.insuranceType || "bumperToBumper",
              nomineeName: data.nomineeName || "",
              nomineeAge: data.nomineeAge || "",
              nomineeRelationship: data.nomineeRelationship || ""
            };

            // Update all sections in one API call
            response = await vehicleAPI.updateVehicle(vehicleId, {
              vehicleDetails: vehicleDetailsData,
              ...exchangeVehicleData,
              ...registrationData,
              ...insuranceData
            });
            break;
          default:
            response = await vehicleAPI.updateVehicle(vehicleId, data);
        }

        // Check storage type from response
        if (response.data.storageType) {
          dispatch({
            type: 'api/setStorageType',
            payload: response.data.storageType
          });

          // If we're using memory storage, set offline mode
          if (response.data.storageType === 'memory') {
            dispatch({ type: 'api/setOfflineMode', payload: true });
          }
        }

        return response.data;
      } catch (apiError: any) {
        // If we're already in offline mode, don't show errors
        if (offlineMode) {
          return {
            success: true,
            data: { [section]: data },
            storageType: 'local'
          };
        }

        // If we get a 404 error (vehicle not found), the ID in localStorage might be invalid
        // Clear localStorage and create a new vehicle record
        if (apiError.response && apiError.response.status === 404) {
          clearState(); // Clear localStorage

          // Create a new vehicle record
          try {
            const newVehicleResponse = await vehicleAPI.createVehicle({});
            const newVehicleId = newVehicleResponse.data.data._id;

            // Update the vehicleId in the store
            dispatch(setVehicleId(newVehicleId));

            // Try again with the new vehicle ID
            return await vehicleAPI.updateVehicle(newVehicleId, data);
          } catch (createError) {
            // Fall back to offline mode
            dispatch(setOfflineMode(true));
            return {
              success: true,
              data: { [section]: data },
              storageType: 'local'
            };
          }
        }

        throw apiError; // Re-throw to be caught by the outer catch
      }
    } catch (error: any) {
      // Extract the error message
      const errorMessage = error.response?.data?.message ||
                          error.message ||
                          `Failed to update ${section}`;

      return rejectWithValue(errorMessage);
    }
  }
);

/**
 * Submit the entire form
 * Marks the form as completed and handles offline mode
 * Also handles uploading any files that were selected but not yet uploaded
 * Reports progress during the submission process
 */
export const submitForm = createAsyncThunk(
  "api/submitForm",
  async (data: any, { getState, rejectWithValue, dispatch }) => {
    try {
      // Reset submission progress
      dispatch(updateSubmissionProgress({ progress: 0, status: 'Starting submission...' }));

      const state = getState() as RootState;
      const vehicleId = state.api.vehicleId;
      const { offlineMode } = state.api;

      if (!vehicleId) {
        dispatch(updateSubmissionProgress({ progress: 0, status: 'Error: No vehicle ID available' }));
        return rejectWithValue("No vehicle ID available");
      }

      // Update progress - initial step
      dispatch(updateSubmissionProgress({ progress: 10, status: 'Preparing data...' }));

      // Check if there are any files that need to be uploaded
      if (data.documents && Object.keys(data.documents).length > 0) {
        dispatch(updateSubmissionProgress({ progress: 20, status: 'Processing document files...' }));

        // Count files that need to be uploaded
        const filesToUpload = Object.entries(data.documents).filter(([_, doc]) => doc.file && doc.fileSelected);
        const totalFiles = filesToUpload.length;
        let uploadedFiles = 0;

        // Upload ALL files in single request (this fixes the path saving issue!)
        try {
          dispatch(updateSubmissionProgress({
            progress: 40,
            status: `Uploading ${totalFiles} files...`
          }));

          // Create single FormData for ALL files
          const allFilesFormData = new FormData();

          // Add all files to single FormData
          for (const [key, doc] of filesToUpload) {
            allFilesFormData.append(key, doc.file);
            allFilesFormData.append(`${key}_metadata`, JSON.stringify({
              fileName: doc.fileName,
              fileType: doc.fileType
            }));
          }

          // Upload ALL files in single request
          await vehicleAPI.uploadDocumentsWithFiles(vehicleId, allFilesFormData);

          // Update progress after successful upload
          dispatch(updateSubmissionProgress({
            progress: 80,
            status: `Successfully uploaded ${totalFiles} files`
          }));

        } catch (error) {
          dispatch(updateSubmissionProgress({
            progress: 80,
            status: 'Error uploading files, continuing with form submission...'
          }));
        }
      } else {
        // Skip to 80% if no files to upload
        dispatch(updateSubmissionProgress({ progress: 80, status: 'No files to upload, finalizing submission...' }));
      }

      try {
        // Update progress - final submission
        dispatch(updateSubmissionProgress({ progress: 85, status: 'Finalizing submission...' }));

        // Remove documents from final update to prevent overwriting file paths
        const { documents, ...dataWithoutDocuments } = data;

        const response = await vehicleAPI.updateVehicle(vehicleId, {
          ...dataWithoutDocuments,
          status: "completed",
        });

        // Update progress - complete
        dispatch(updateSubmissionProgress({ progress: 100, status: 'Submission complete!' }));

        // Check storage type from response
        if (response.data.storageType) {
          dispatch({
            type: 'api/setStorageType',
            payload: response.data.storageType
          });
        }

        // Clear localStorage after successful submission
        clearState();

        // Set vehicleId to null to prevent any further operations on this vehicle
        // This ensures that when we start a new form, we don't accidentally modify this one
        state.vehicleId = null;

        return response.data;
      } catch (apiError: any) {
        // Update progress - error
        dispatch(updateSubmissionProgress({
          progress: 90,
          status: `Error during final submission: ${apiError.message || 'Unknown error'}`
        }));

        // If we're in offline mode, simulate a successful submission
        if (offlineMode) {
          dispatch(updateSubmissionProgress({ progress: 100, status: 'Submission complete (offline mode)' }));
          return {
            success: true,
            data: {
              ...data,
              status: "completed",
              _id: vehicleId
            },
            storageType: 'local'
          };
        }

        // If we get a 404 error (vehicle not found), the ID in localStorage might be invalid
        // Clear localStorage and create a new vehicle record
        if (apiError.response && apiError.response.status === 404) {
          clearState(); // Clear localStorage

          // Create a new vehicle record
          try {
            const newVehicleResponse = await vehicleAPI.createVehicle({});
            const newVehicleId = newVehicleResponse.data.data._id;

            // Update the vehicleId in the store
            dispatch(setVehicleId(newVehicleId));

            // Try again with the new vehicle ID (without documents to prevent overwriting)
            const { documents, ...dataWithoutDocuments } = data;
            return await vehicleAPI.updateVehicle(newVehicleId, {
              ...dataWithoutDocuments,
              status: "completed",
            });
          } catch (createError) {
            // Fall back to offline mode
            dispatch(setOfflineMode(true));
            return {
              success: true,
              data: {
                ...data,
                status: "completed",
                _id: "temp_" + Date.now()
              },
              storageType: 'local'
            };
          }
        }

        throw apiError; // Re-throw to be caught by the outer catch
      }
    } catch (error: any) {
      // Extract the error message
      const errorMessage = error.response?.data?.message ||
                          error.message ||
                          "Failed to submit form";

      return rejectWithValue(errorMessage);
    }
  }
);

const apiSlice = createSlice({
  name: "api",
  initialState,
  reducers: {
    resetApiState: (state) => {
      // Reset all state properties to initial values
      Object.assign(state, initialState);

      // Explicitly set vehicleId to null to ensure it's cleared
      state.vehicleId = null;

      // Reset form submission status
      state.formSubmitted = false;

      // Reset submission progress
      state.submissionProgress = 0;
      state.submissionStatus = '';

      // Clear any errors
      state.error = null;
      state.success = false;
      state.loading = false;

      // Reset offline mode and storage type
      state.offlineMode = false;
      state.storageType = 'mongodb';

      return state;
    },
    setVehicleId: (state, action: PayloadAction<string>) => {
      state.vehicleId = action.payload;
    },
    clearErrors: (state) => {
      state.error = null;
    },
    setStorageType: (state, action: PayloadAction<'mongodb' | 'memory' | 'local'>) => {
      state.storageType = action.payload;
    },
    setOfflineMode: (state, action: PayloadAction<boolean>) => {
      state.offlineMode = action.payload;
      if (action.payload && !state.storageType) {
        state.storageType = 'local';
      }
    },
    incrementRetryCount: (state) => {
      state.retryCount += 1;
    },
    resetRetryCount: (state) => {
      state.retryCount = 0;
    },
    updateSubmissionProgress: (state, action: PayloadAction<{ progress: number, status: string }>) => {
      state.submissionProgress = action.payload.progress;
      state.submissionStatus = action.payload.status;
    },
    resetSubmissionProgress: (state) => {
      state.submissionProgress = 0;
      state.submissionStatus = '';
    },
    clearDuplicateCheck: (state) => {
      state.duplicateCheck = {
        isChecking: false,
        isDuplicate: false,
        customer: null,
        error: null
      };
    }
  },
  extraReducers: (builder) => {
    // Check duplicate
    builder.addCase(checkDuplicate.pending, (state) => {
      state.duplicateCheck.isChecking = true;
      state.duplicateCheck.error = null;
    });
    builder.addCase(checkDuplicate.fulfilled, (state, action) => {
      state.duplicateCheck.isChecking = false;
      state.duplicateCheck.isDuplicate = action.payload.isDuplicate;
      state.duplicateCheck.customer = action.payload.customer || null;
      state.duplicateCheck.error = null;
    });
    builder.addCase(checkDuplicate.rejected, (state, action) => {
      state.duplicateCheck.isChecking = false;
      state.duplicateCheck.error = action.payload as string;
    });

    // Create vehicle
    builder.addCase(createVehicle.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(createVehicle.fulfilled, (state, action) => {
      state.loading = false;
      state.vehicleId = action.payload.data._id;
      state.success = true;

      // Set storage type if provided in response
      if (action.payload.storageType) {
        state.storageType = action.payload.storageType;
      }

      // Reset retry count on success
      state.retryCount = 0;
    });
    builder.addCase(createVehicle.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;

      // Increment retry count on failure
      state.retryCount += 1;

      // If we've tried too many times, go into offline mode
      if (state.retryCount >= 3) {
        state.offlineMode = true;
        state.storageType = 'local';
      }
    });

    // Update form section
    builder.addCase(updateFormSection.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(updateFormSection.fulfilled, (state, action) => {
      state.loading = false;
      state.success = true;

      // Set storage type if provided in response
      if (action.payload.storageType) {
        state.storageType = action.payload.storageType;
      }

      // Reset retry count on success
      state.retryCount = 0;
    });
    builder.addCase(updateFormSection.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;

      // Increment retry count on failure
      state.retryCount += 1;

      // If we've tried too many times, go into offline mode
      if (state.retryCount >= 3) {
        state.offlineMode = true;
        state.storageType = 'local';

        // Clear error in offline mode to allow proceeding
        state.error = null;
      }
    });

    // Submit form
    builder.addCase(submitForm.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(submitForm.fulfilled, (state, action) => {
      state.loading = false;
      state.success = true;
      state.formSubmitted = true;

      // Clear vehicleId to prevent any further operations on this vehicle
      // This ensures that when we start a new form, we don't accidentally modify this one
      state.vehicleId = null;

      // Set storage type if provided in response
      if (action.payload.storageType) {
        state.storageType = action.payload.storageType;
      }
    });
    builder.addCase(submitForm.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;

      // If we're in offline mode, still mark as submitted
      if (state.offlineMode) {
        state.formSubmitted = true;
        state.error = null;
      }
    });
  },
});

export const {
  resetApiState,
  setVehicleId,
  clearErrors,
  setStorageType,
  setOfflineMode,
  incrementRetryCount,
  resetRetryCount,
  updateSubmissionProgress,
  resetSubmissionProgress,
  clearDuplicateCheck
} = apiSlice.actions;

export default apiSlice.reducer;
