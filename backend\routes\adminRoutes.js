import express from 'express';
import {
  getDashboardStats,
  getAllCustomers,
  getCustomerById,
  createCustomer,
  updateCustomer,
  deleteCustomer,
  getAllVehicles,
  getVehicleById,
  updateVehicleStatus,
  linkCustomerToVehicle,
  getCustomerVehicles,
  getFilterOptions
} from '../controllers/adminController.js';


const router = express.Router();

// Dashboard routes
router.get('/dashboard', getDashboardStats);

// Filter options route
router.get('/filter-options', getFilterOptions);

// Customer routes
router.get('/customers', getAllCustomers);
router.get('/customers/:id', getCustomerById);
router.post('/customers', createCustomer);
router.put('/customers/:id', updateCustomer);
router.delete('/customers/:id', deleteCustomer);
router.get('/customers/:id/vehicles', getCustomerVehicles);

// Vehicle routes
router.get('/vehicles', getAllVehicles);
router.get('/vehicles/:id', getVehicleById);
router.put('/vehicles/:id/status', updateVehicleStatus);

// Link customer to vehicle
router.post('/link-customer-vehicle', linkCustomerToVehicle);



export default router;
