import React from 'react';
import { FileText, Clock, AlertCircle, CheckCircle, Activity } from 'lucide-react';
import { useCSVMonitoring } from '../../hooks/useCSVMonitoring';

interface CSVStatusWidgetProps {
  className?: string;
  showDetails?: boolean;
  onManageClick?: () => void;
}

const CSVStatusWidget: React.FC<CSVStatusWidgetProps> = ({
  className = '',
  showDetails = true,
  onManageClick
}) => {
  const {
    stats,
    loading,
    error,
    lastUpdate,
    isHealthy,
    queueStatus,
    memoryUsagePercent,
    forceProcessBatch
  } = useCSVMonitoring({
    enabled: true,
    interval: 15000, // 15 seconds
    onQueueSizeChange: (size) => {
      if (size > 50) {
        console.warn(`CSV queue size is high: ${size}`);
      }
    },
    onProcessingStateChange: (isProcessing) => {
      console.log(`CSV processing state changed: ${isProcessing ? 'started' : 'stopped'}`);
    }
  });

  const getStatusColor = () => {
    if (error) return 'text-red-600 bg-red-50 border-red-200';
    if (!isHealthy) return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    return 'text-green-600 bg-green-50 border-green-200';
  };

  const getStatusIcon = () => {
    if (error) return <AlertCircle className="w-5 h-5" />;
    if (loading || (stats?.isProcessing)) return <Activity className="w-5 h-5 animate-spin" />;
    if (isHealthy) return <CheckCircle className="w-5 h-5" />;
    return <Clock className="w-5 h-5" />;
  };

  const getQueueStatusText = () => {
    switch (queueStatus) {
      case 'empty': return 'Queue Empty';
      case 'low': return 'Low Queue';
      case 'medium': return 'Medium Queue';
      case 'high': return 'High Queue';
      default: return 'Unknown';
    }
  };

  const formatUptime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  const formatMemoryUsage = (bytes: number) => {
    const mb = bytes / (1024 * 1024);
    return `${mb.toFixed(1)} MB`;
  };

  return (
    <div className={`bg-white rounded-lg shadow-lg border ${getStatusColor()} ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <FileText className="w-6 h-6 text-blue-600" />
            <div>
              <h3 className="text-lg font-semibold text-gray-800">CSV Export Status</h3>
              {lastUpdate && (
                <p className="text-sm text-gray-500">
                  Last updated: {lastUpdate.toLocaleTimeString()}
                </p>
              )}
            </div>
          </div>
          <div className="flex items-center gap-2">
            {getStatusIcon()}
            <span className="text-sm font-medium">
              {error ? 'Error' : stats?.isProcessing ? 'Processing' : 'Ready'}
            </span>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        {error ? (
          <div className="text-red-600 text-sm">
            <p className="font-medium">Error occurred:</p>
            <p>{error}</p>
          </div>
        ) : loading && !stats ? (
          <div className="flex items-center justify-center py-8">
            <Activity className="w-6 h-6 animate-spin text-blue-600" />
            <span className="ml-2 text-gray-600">Loading CSV status...</span>
          </div>
        ) : stats ? (
          <div className="space-y-4">
            {/* Quick Stats */}
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{stats.queueSize}</div>
                <div className="text-sm text-gray-600">{getQueueStatusText()}</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {stats.isProcessing ? 'Active' : 'Idle'}
                </div>
                <div className="text-sm text-gray-600">Processing Status</div>
              </div>
            </div>

            {/* Detailed Stats */}
            {showDetails && (
              <div className="space-y-3 pt-4 border-t border-gray-200">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Batch Size:</span>
                  <span className="text-sm font-medium">{stats.batchSize}</span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Batch Timeout:</span>
                  <span className="text-sm font-medium">{stats.batchTimeout / 1000}s</span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Retry Attempts:</span>
                  <span className="text-sm font-medium">{stats.retryAttempts}</span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Uptime:</span>
                  <span className="text-sm font-medium">{formatUptime(stats.uptime)}</span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Memory Usage:</span>
                  <span className="text-sm font-medium">
                    {formatMemoryUsage(stats.memoryUsage.heapUsed)} ({memoryUsagePercent}%)
                  </span>
                </div>
              </div>
            )}

            {/* Progress Bar for Queue */}
            {stats.queueSize > 0 && (
              <div className="pt-4 border-t border-gray-200">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm text-gray-600">Queue Progress</span>
                  <span className="text-sm font-medium">{stats.queueSize} items</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full transition-all duration-300 ${
                      queueStatus === 'high' ? 'bg-red-500' :
                      queueStatus === 'medium' ? 'bg-yellow-500' :
                      'bg-green-500'
                    }`}
                    style={{ 
                      width: `${Math.min((stats.queueSize / stats.batchSize) * 100, 100)}%` 
                    }}
                  />
                </div>
              </div>
            )}

            {/* Memory Usage Bar */}
            {showDetails && (
              <div className="pt-2">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm text-gray-600">Memory Usage</span>
                  <span className="text-sm font-medium">{memoryUsagePercent}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full transition-all duration-300 ${
                      memoryUsagePercent > 80 ? 'bg-red-500' :
                      memoryUsagePercent > 60 ? 'bg-yellow-500' :
                      'bg-green-500'
                    }`}
                    style={{ width: `${memoryUsagePercent}%` }}
                  />
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <FileText className="w-12 h-12 mx-auto mb-2 opacity-50" />
            <p>CSV status unavailable</p>
          </div>
        )}
      </div>

      {/* Actions */}
      <div className="p-4 border-t border-gray-200 bg-gray-50">
        <div className="flex gap-2">
          {stats?.queueSize > 0 && (
            <button
              onClick={forceProcessBatch}
              className="flex-1 px-3 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors"
            >
              Process Queue
            </button>
          )}
          
          {onManageClick && (
            <button
              onClick={onManageClick}
              className="flex-1 px-3 py-2 bg-gray-600 text-white text-sm rounded-lg hover:bg-gray-700 transition-colors"
            >
              Manage Files
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default CSVStatusWidget;
