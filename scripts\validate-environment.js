#!/usr/bin/env node

/**
 * Environment Validation Script
 * Validates that both development and production environments are properly configured
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.join(__dirname, '..');

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

// Helper functions
const log = (message, color = 'white') => console.log(`${colors[color]}${message}${colors.reset}`);
const success = (message) => log(`✅ ${message}`, 'green');
const error = (message) => log(`❌ ${message}`, 'red');
const warning = (message) => log(`⚠️  ${message}`, 'yellow');
const info = (message) => log(`ℹ️  ${message}`, 'blue');

// Load environment file
const loadEnvFile = (filePath) => {
  try {
    if (!fs.existsSync(filePath)) {
      return null;
    }
    
    const content = fs.readFileSync(filePath, 'utf8');
    const env = {};
    
    content.split('\n').forEach(line => {
      line = line.trim();
      if (line && !line.startsWith('#')) {
        const [key, ...valueParts] = line.split('=');
        if (key && valueParts.length > 0) {
          env[key.trim()] = valueParts.join('=').trim();
        }
      }
    });
    
    return env;
  } catch (err) {
    error(`Failed to load ${filePath}: ${err.message}`);
    return null;
  }
};

// Validation rules
const requiredBackendVars = {
  development: [
    'PORT', 'NODE_ENV', 'MONGODB_URI', 'JWT_SECRET', 
    'FRONTEND_URL', 'CORS_ORIGINS', 'RECAPTCHA_SITE_KEY', 'RECAPTCHA_SECRET_KEY'
  ],
  production: [
    'PORT', 'NODE_ENV', 'MONGODB_URI', 'JWT_SECRET', 
    'FRONTEND_URL', 'CORS_ORIGINS', 'RECAPTCHA_SITE_KEY', 'RECAPTCHA_SECRET_KEY'
  ]
};

const requiredFrontendVars = {
  development: ['NODE_ENV', 'VITE_BACKEND_URL', 'VITE_RECAPTCHA_SITE_KEY'],
  production: ['NODE_ENV', 'VITE_BACKEND_URL', 'VITE_RECAPTCHA_SITE_KEY']
};

// URL validation patterns
const urlPatterns = {
  development: {
    backend: /^http:\/\/localhost:\d+$/,
    frontend: /^http:\/\/localhost:\d+$/
  },
  production: {
    backend: /^https:\/\/[a-zA-Z0-9.-]+\.(com|net|org|app)$/,
    frontend: /^https:\/\/[a-zA-Z0-9.-]+\.(com|net|org|app|vercel\.app)$/
  }
};

// Validate environment variables
const validateEnvVars = (env, required, envName, envType) => {
  let isValid = true;
  
  info(`\n📋 Validating ${envName} ${envType} environment variables:`);
  
  required.forEach(varName => {
    if (!env || !env[varName]) {
      error(`  Missing required variable: ${varName}`);
      isValid = false;
    } else {
      success(`  ${varName}: ✓`);
    }
  });
  
  return isValid;
};

// Validate URLs
const validateUrls = (env, envType, isBackend = false) => {
  let isValid = true;
  
  if (!env) return false;
  
  info(`\n🔗 Validating ${envType} URLs:`);
  
  if (isBackend) {
    // Validate CORS origins
    if (env.CORS_ORIGINS) {
      const origins = env.CORS_ORIGINS.split(',').map(o => o.trim());
      origins.forEach(origin => {
        if (urlPatterns[envType].frontend.test(origin)) {
          success(`  CORS Origin: ${origin} ✓`);
        } else {
          error(`  Invalid CORS Origin: ${origin}`);
          isValid = false;
        }
      });
    }
    
    // Validate frontend URL
    if (env.FRONTEND_URL) {
      if (urlPatterns[envType].frontend.test(env.FRONTEND_URL)) {
        success(`  Frontend URL: ${env.FRONTEND_URL} ✓`);
      } else {
        error(`  Invalid Frontend URL: ${env.FRONTEND_URL}`);
        isValid = false;
      }
    }
  } else {
    // Validate backend URL
    if (env.VITE_BACKEND_URL) {
      if (urlPatterns[envType].backend.test(env.VITE_BACKEND_URL)) {
        success(`  Backend URL: ${env.VITE_BACKEND_URL} ✓`);
      } else {
        error(`  Invalid Backend URL: ${env.VITE_BACKEND_URL}`);
        isValid = false;
      }
    }
  }
  
  return isValid;
};

// Validate reCAPTCHA configuration
const validateRecaptcha = (backendEnv, frontendEnv, envType) => {
  let isValid = true;
  
  info(`\n🔐 Validating ${envType} reCAPTCHA configuration:`);
  
  if (!backendEnv || !frontendEnv) {
    error('  Missing environment files');
    return false;
  }
  
  // Check if site keys match
  if (backendEnv.RECAPTCHA_SITE_KEY === frontendEnv.VITE_RECAPTCHA_SITE_KEY) {
    success(`  Site keys match: ${backendEnv.RECAPTCHA_SITE_KEY} ✓`);
  } else {
    error(`  Site key mismatch:`);
    error(`    Backend: ${backendEnv.RECAPTCHA_SITE_KEY}`);
    error(`    Frontend: ${frontendEnv.VITE_RECAPTCHA_SITE_KEY}`);
    isValid = false;
  }
  
  // Check if secret key exists
  if (backendEnv.RECAPTCHA_SECRET_KEY) {
    success(`  Secret key configured ✓`);
  } else {
    error(`  Missing secret key`);
    isValid = false;
  }
  
  return isValid;
};

// Main validation function
const validateEnvironment = () => {
  log('\n🚀 Environment Configuration Validation\n', 'cyan');
  
  let overallValid = true;
  
  // Load environment files
  const backendDev = loadEnvFile(path.join(rootDir, 'backend', '.env'));
  const backendProd = loadEnvFile(path.join(rootDir, 'backend', '.env.production'));
  const frontendDev = loadEnvFile(path.join(rootDir, 'frontend', '.env'));
  const frontendProd = loadEnvFile(path.join(rootDir, 'frontend', '.env.production'));
  
  // Validate development environment
  log('\n🔧 DEVELOPMENT ENVIRONMENT', 'magenta');
  const devBackendValid = validateEnvVars(backendDev, requiredBackendVars.development, 'Backend', 'Development');
  const devFrontendValid = validateEnvVars(frontendDev, requiredFrontendVars.development, 'Frontend', 'Development');
  const devUrlsBackendValid = validateUrls(backendDev, 'development', true);
  const devUrlsFrontendValid = validateUrls(frontendDev, 'development', false);
  const devRecaptchaValid = validateRecaptcha(backendDev, frontendDev, 'development');
  
  const devValid = devBackendValid && devFrontendValid && devUrlsBackendValid && devUrlsFrontendValid && devRecaptchaValid;
  
  // Validate production environment
  log('\n🚀 PRODUCTION ENVIRONMENT', 'magenta');
  const prodBackendValid = validateEnvVars(backendProd, requiredBackendVars.production, 'Backend', 'Production');
  const prodFrontendValid = validateEnvVars(frontendProd, requiredFrontendVars.production, 'Frontend', 'Production');
  const prodUrlsBackendValid = validateUrls(backendProd, 'production', true);
  const prodUrlsFrontendValid = validateUrls(frontendProd, 'production', false);
  const prodRecaptchaValid = validateRecaptcha(backendProd, frontendProd, 'production');
  
  const prodValid = prodBackendValid && prodFrontendValid && prodUrlsBackendValid && prodUrlsFrontendValid && prodRecaptchaValid;
  
  overallValid = devValid && prodValid;
  
  // Summary
  log('\n📊 VALIDATION SUMMARY', 'cyan');
  if (devValid) {
    success('Development Environment: ✅ VALID');
  } else {
    error('Development Environment: ❌ INVALID');
  }
  
  if (prodValid) {
    success('Production Environment: ✅ VALID');
  } else {
    error('Production Environment: ❌ INVALID');
  }
  
  if (overallValid) {
    success('\n🎉 All environments are properly configured!');
  } else {
    error('\n💥 Environment configuration issues detected!');
    warning('Please fix the issues above before deploying.');
  }
  
  return overallValid;
};

// Run validation
const isValid = validateEnvironment();
process.exit(isValid ? 0 : 1);
