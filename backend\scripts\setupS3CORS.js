/**
 * Setup S3 CORS Configuration
 * This script configures CORS settings for the S3 bucket to allow frontend access
 */

import { S3Client, PutBucketCorsCommand, GetBucketCorsCommand } from '@aws-sdk/client-s3';
import { getS3Client } from '../config/s3Config.js';
import logger from '../utils/logger.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const bucketName = process.env.AWS_S3_BUCKET_NAME;

const corsConfiguration = {
  CORSRules: [
    {
      ID: 'AllowFrontendAccess',
      AllowedHeaders: ['*'],
      AllowedMethods: ['GET', 'HEAD'],
      AllowedOrigins: [
        'http://localhost:5173',  // Development frontend
        'http://localhost:3000',  // Alternative dev port
        'https://q5-x-preview.vercel.app',  // Production frontend
        'https://*.vercel.app'    // Any Vercel deployment
      ],
      ExposeHeaders: [
        'ETag',
        'Content-Length',
        'Content-Type',
        'Last-Modified'
      ],
      MaxAgeSeconds: 3600
    },
    {
      ID: 'AllowPresignedURLs',
      AllowedHeaders: ['*'],
      AllowedMethods: ['GET', 'HEAD'],
      AllowedOrigins: ['*'],  // Allow all origins for presigned URLs
      MaxAgeSeconds: 3600
    }
  ]
};

async function setupCORS() {
  try {
    logger.info('Setting up S3 CORS configuration...');
    
    // Get S3 client
    const s3Client = getS3Client();
    
    // Apply CORS configuration
    const putCorsCommand = new PutBucketCorsCommand({
      Bucket: bucketName,
      CORSConfiguration: corsConfiguration
    });
    
    await s3Client.send(putCorsCommand);
    logger.info('✅ CORS configuration applied successfully');
    
    // Verify CORS configuration
    const getCorsCommand = new GetBucketCorsCommand({
      Bucket: bucketName
    });
    
    const corsResult = await s3Client.send(getCorsCommand);
    logger.info('📋 Current CORS configuration:');
    console.log(JSON.stringify(corsResult.CORSRules, null, 2));
    
    logger.info('🎉 S3 CORS setup completed successfully!');
    
  } catch (error) {
    logger.error('❌ Error setting up S3 CORS:', error);
    
    if (error.name === 'AccessDenied') {
      logger.error('💡 Make sure your AWS credentials have s3:PutBucketCors and s3:GetBucketCors permissions');
    }
  }
}

// Run the setup
setupCORS();
