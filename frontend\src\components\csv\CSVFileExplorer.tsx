import React, { useState, useEffect } from 'react';
import { 
  Folder, File, Download, Eye, Trash2, Calendar, 
  HardDrive, Database, FileText, BarChart3, 
  Info, MapPin, Clock, RefreshCw
} from 'lucide-react';

interface FileInfo {
  name: string;
  size: number;
  created: string;
  modified: string;
  type: 'detailed' | 'summary' | 'analytics' | 'unknown';
  path: string;
}

interface DirectoryInfo {
  name: string;
  type: 'directory';
  path: string;
}

const CSVFileExplorer: React.FC = () => {
  const [currentPath, setCurrentPath] = useState<'daily' | 'monthly' | 'archive'>('daily');
  const [selectedDate, setSelectedDate] = useState<string>('');
  const [files, setFiles] = useState<FileInfo[]>([]);
  const [directories, setDirectories] = useState<DirectoryInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedFile, setSelectedFile] = useState<FileInfo | null>(null);
  const [previewData, setPreviewData] = useState<string>('');

  const API_BASE = process.env.REACT_APP_API_URL || 'http://localhost:10000';

  useEffect(() => {
    fetchDirectoryContents();
  }, [currentPath, selectedDate]);

  const fetchDirectoryContents = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      const params = new URLSearchParams({
        type: currentPath,
        ...(selectedDate && { date: selectedDate })
      });

      const response = await fetch(`${API_BASE}/api/csv/files?${params}`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      const data = await response.json();
      if (data.success) {
        setFiles(data.data.files || []);
        setDirectories(data.data.directories || []);
      }
    } catch (error) {
      console.error('Error fetching directory contents:', error);
    } finally {
      setLoading(false);
    }
  };

  const downloadFile = async (filename: string) => {
    try {
      const token = localStorage.getItem('token');
      const params = new URLSearchParams({
        type: currentPath,
        ...(selectedDate && { date: selectedDate })
      });

      const response = await fetch(`${API_BASE}/api/csv/download/${filename}?${params}`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('Error downloading file:', error);
    }
  };

  const previewFile = async (filename: string) => {
    try {
      const token = localStorage.getItem('token');
      const params = new URLSearchParams({
        type: currentPath,
        rows: '50',
        ...(selectedDate && { date: selectedDate })
      });

      const response = await fetch(`${API_BASE}/api/csv/preview/${filename}?${params}`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      const data = await response.json();
      if (data.success) {
        setPreviewData(data.data.content);
        setSelectedFile(files.find(f => f.name === filename) || null);
      }
    } catch (error) {
      console.error('Error previewing file:', error);
    }
  };

  const deleteFile = async (filename: string) => {
    if (!confirm(`Are you sure you want to delete ${filename}?`)) return;

    try {
      const token = localStorage.getItem('token');
      const params = new URLSearchParams({
        type: currentPath,
        ...(selectedDate && { date: selectedDate })
      });

      const response = await fetch(`${API_BASE}/api/csv/files/${filename}?${params}`, {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${token}` }
      });

      const data = await response.json();
      if (data.success) {
        fetchDirectoryContents();
        if (selectedFile?.name === filename) {
          setSelectedFile(null);
          setPreviewData('');
        }
      }
    } catch (error) {
      console.error('Error deleting file:', error);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getFileTypeIcon = (type: string) => {
    switch (type) {
      case 'detailed': return <FileText className="w-5 h-5 text-blue-500" />;
      case 'summary': return <BarChart3 className="w-5 h-5 text-green-500" />;
      case 'analytics': return <BarChart3 className="w-5 h-5 text-purple-500" />;
      default: return <File className="w-5 h-5 text-gray-500" />;
    }
  };

  const getFileTypeColor = (type: string) => {
    switch (type) {
      case 'detailed': return 'bg-blue-50 border-blue-200 text-blue-800';
      case 'summary': return 'bg-green-50 border-green-200 text-green-800';
      case 'analytics': return 'bg-purple-50 border-purple-200 text-purple-800';
      default: return 'bg-gray-50 border-gray-200 text-gray-800';
    }
  };

  const getCurrentPath = () => {
    if (currentPath === 'archive' && selectedDate) {
      return `backend/csv-exports/archive/${selectedDate}/`;
    }
    return `backend/csv-exports/${currentPath}/`;
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-800">CSV File Explorer</h2>
          <p className="text-gray-600">Browse and manage your CSV export files</p>
        </div>
        <button
          onClick={fetchDirectoryContents}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-2"
        >
          <RefreshCw className="w-4 h-4" />
          Refresh
        </button>
      </div>

      {/* File System Info */}
      <div className="mb-6 p-4 bg-blue-50 rounded-lg border-l-4 border-blue-400">
        <div className="flex items-center gap-2 mb-2">
          <HardDrive className="w-5 h-5 text-blue-600" />
          <h3 className="font-semibold text-blue-800">CSV Storage Locations</h3>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div className="bg-white p-3 rounded border">
            <div className="flex items-center gap-2 mb-1">
              <Folder className="w-4 h-4 text-yellow-600" />
              <strong>Daily Files</strong>
            </div>
            <code className="text-xs text-gray-600">backend/csv-exports/daily/</code>
            <p className="text-xs text-gray-500 mt-1">Current day's exports</p>
          </div>
          <div className="bg-white p-3 rounded border">
            <div className="flex items-center gap-2 mb-1">
              <Folder className="w-4 h-4 text-green-600" />
              <strong>Archive Files</strong>
            </div>
            <code className="text-xs text-gray-600">backend/csv-exports/archive/YYYY-MM-DD/</code>
            <p className="text-xs text-gray-500 mt-1">Historical exports by date</p>
          </div>
          <div className="bg-white p-3 rounded border">
            <div className="flex items-center gap-2 mb-1">
              <Database className="w-4 h-4 text-purple-600" />
              <strong>No Permissions Needed</strong>
            </div>
            <p className="text-xs text-gray-600">Direct file system access</p>
            <p className="text-xs text-gray-500 mt-1">Download via API or direct access</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="flex gap-4 mb-6">
        <div className="flex gap-2">
          {['daily', 'monthly', 'archive'].map((path) => (
            <button
              key={path}
              onClick={() => {
                setCurrentPath(path as any);
                setSelectedDate('');
              }}
              className={`px-4 py-2 rounded-lg capitalize flex items-center gap-2 ${
                currentPath === path
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              <Folder className="w-4 h-4" />
              {path}
            </button>
          ))}
        </div>

        {/* Archive Date Selector */}
        {currentPath === 'archive' && directories.length > 0 && (
          <select
            value={selectedDate}
            onChange={(e) => setSelectedDate(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg"
          >
            <option value="">Select Date</option>
            {directories.map((dir) => (
              <option key={dir.name} value={dir.name}>{dir.name}</option>
            ))}
          </select>
        )}
      </div>

      {/* Current Path Display */}
      <div className="mb-4 p-3 bg-gray-50 rounded-lg">
        <div className="flex items-center gap-2">
          <MapPin className="w-4 h-4 text-gray-600" />
          <span className="text-sm text-gray-600">Current Path:</span>
          <code className="text-sm font-mono bg-white px-2 py-1 rounded border">
            {getCurrentPath()}
          </code>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* File List */}
        <div className="lg:col-span-2">
          <h3 className="text-lg font-semibold mb-4">Files</h3>
          
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <RefreshCw className="w-6 h-6 animate-spin text-blue-600" />
              <span className="ml-2 text-gray-600">Loading files...</span>
            </div>
          ) : files.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <File className="w-12 h-12 mx-auto mb-2 opacity-50" />
              <p>No CSV files found in this directory</p>
            </div>
          ) : (
            <div className="space-y-2">
              {files.map((file) => (
                <div
                  key={file.name}
                  className={`p-4 border rounded-lg hover:shadow-md transition-shadow ${getFileTypeColor(file.type)}`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {getFileTypeIcon(file.type)}
                      <div>
                        <h4 className="font-medium">{file.name}</h4>
                        <div className="flex items-center gap-4 text-sm opacity-75">
                          <span>{formatFileSize(file.size)}</span>
                          <span className="flex items-center gap-1">
                            <Clock className="w-3 h-3" />
                            {formatDate(file.modified)}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <button
                        onClick={() => previewFile(file.name)}
                        className="p-2 text-blue-600 hover:bg-blue-100 rounded"
                        title="Preview"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => downloadFile(file.name)}
                        className="p-2 text-green-600 hover:bg-green-100 rounded"
                        title="Download"
                      >
                        <Download className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => deleteFile(file.name)}
                        className="p-2 text-red-600 hover:bg-red-100 rounded"
                        title="Delete"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* File Preview */}
        <div className="lg:col-span-1">
          <h3 className="text-lg font-semibold mb-4">File Preview</h3>
          
          {selectedFile ? (
            <div className="border rounded-lg">
              {/* File Info */}
              <div className="p-4 border-b bg-gray-50">
                <div className="flex items-center gap-2 mb-2">
                  {getFileTypeIcon(selectedFile.type)}
                  <h4 className="font-medium">{selectedFile.name}</h4>
                </div>
                <div className="text-sm text-gray-600 space-y-1">
                  <div>Size: {formatFileSize(selectedFile.size)}</div>
                  <div>Type: <span className="capitalize">{selectedFile.type}</span></div>
                  <div>Modified: {formatDate(selectedFile.modified)}</div>
                </div>
              </div>
              
              {/* Preview Content */}
              <div className="p-4">
                <h5 className="text-sm font-medium mb-2">Content Preview (First 50 rows):</h5>
                <div className="bg-gray-50 p-3 rounded text-xs font-mono overflow-auto max-h-96">
                  <pre className="whitespace-pre-wrap">{previewData}</pre>
                </div>
              </div>
            </div>
          ) : (
            <div className="border rounded-lg p-8 text-center text-gray-500">
              <Info className="w-12 h-12 mx-auto mb-2 opacity-50" />
              <p>Select a file to preview its contents</p>
            </div>
          )}
        </div>
      </div>

      {/* File Type Legend */}
      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <h4 className="font-medium mb-3">File Types:</h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div className="flex items-center gap-2">
            <FileText className="w-4 h-4 text-blue-500" />
            <strong>Detailed CSV:</strong> Complete form data with all fields
          </div>
          <div className="flex items-center gap-2">
            <BarChart3 className="w-4 h-4 text-green-500" />
            <strong>Summary CSV:</strong> Key metrics and essential data
          </div>
          <div className="flex items-center gap-2">
            <BarChart3 className="w-4 h-4 text-purple-500" />
            <strong>Analytics CSV:</strong> Business intelligence data
          </div>
        </div>
      </div>
    </div>
  );
};

export default CSVFileExplorer;
