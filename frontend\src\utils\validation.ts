/**
 * Validation rules for form fields
 */
export interface ValidationRules {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  email?: boolean;
  phone?: boolean;
  numeric?: boolean;
  min?: number;
  max?: number;
}

/**
 * Validation result
 */
export interface ValidationResult {
  isValid: boolean;
  error?: string;
}

/**
 * Validate a single field value against rules
 * @param value The field value to validate
 * @param rules The validation rules to apply
 * @param fieldName The name of the field (for error messages)
 * @returns Validation result with isValid flag and optional error message
 */
export const validateField = (
  value: any,
  rules: ValidationRules,
  fieldName: string
): ValidationResult => {
  // Convert field name to readable format
  const readableFieldName = fieldName
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, (str) => str.toUpperCase());

  // Required field validation
  if (rules.required && (value === undefined || value === null || value === '')) {
    return {
      isValid: false,
      error: `${readableFieldName} is required`,
    };
  }

  // Skip other validations if value is empty and not required
  if (value === undefined || value === null || value === '') {
    return { isValid: true };
  }

  // String validations
  if (typeof value === 'string') {
    // Min length validation
    if (rules.minLength && value.length < rules.minLength) {
      return {
        isValid: false,
        error: `${readableFieldName} must be at least ${rules.minLength} characters`,
      };
    }

    // Max length validation
    if (rules.maxLength && value.length > rules.maxLength) {
      return {
        isValid: false,
        error: `${readableFieldName} cannot exceed ${rules.maxLength} characters`,
      };
    }

    // Pattern validation
    if (rules.pattern && !rules.pattern.test(value)) {
      return {
        isValid: false,
        error: `${readableFieldName} format is invalid`,
      };
    }

    // Email validation
    if (rules.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
      return {
        isValid: false,
        error: `Please enter a valid email address`,
      };
    }

    // Phone validation
    if (rules.phone && !/^\d{10}$/.test(value.replace(/\D/g, ''))) {
      return {
        isValid: false,
        error: `Please enter a valid 10-digit phone number`,
      };
    }

    // Numeric validation
    if (rules.numeric) {
      // For pincode, allow a space in the middle (format: 000 000)
      if (fieldName === 'pincode') {
        // Remove spaces and check if it's all digits and has correct length
        const digitsOnly = value.replace(/\s/g, '');
        if (!/^\d+$/.test(digitsOnly) || digitsOnly.length !== 6) {
          return {
            isValid: false,
            error: `${readableFieldName} must contain 6 digits in format 000 000`,
          };
        }
      } else if (fieldName === 'exchangeKmsDriven') {
        // Special handling for exchangeKmsDriven
        if (!/^\d+$/.test(value)) {
          return {
            isValid: false,
            error: `${readableFieldName} must contain only numbers`,
          };
        }

        // Check if the value is too large
        if (Number(value) > 9999999) {
          return {
            isValid: false,
            error: `${readableFieldName} value is too large`,
          };
        }
      } else if (!/^\d+$/.test(value)) {
        // For other numeric fields, no spaces allowed
        return {
          isValid: false,
          error: `${readableFieldName} must contain only numbers`,
        };
      }
    }
  }

  // Number validations
  if (typeof value === 'number' || (rules.numeric && !isNaN(Number(value)))) {
    const numValue = typeof value === 'number' ? value : Number(value);

    // Min value validation
    if (rules.min !== undefined && numValue < rules.min) {
      return {
        isValid: false,
        error: `${readableFieldName} must be at least ${rules.min}`,
      };
    }

    // Max value validation
    if (rules.max !== undefined && numValue > rules.max) {
      return {
        isValid: false,
        error: `${readableFieldName} cannot exceed ${rules.max}`,
      };
    }
  }

  // All validations passed
  return { isValid: true };
};

/**
 * Validation rules for each form section
 */
export const validationRules = {
  // Customer Information
  customer: {
    name: { required: true, minLength: 2, maxLength: 50 },
    phone: { required: true, phone: true },
    email: { required: true, email: true },
    altPhone: { phone: true },
    houseNo: { required: true, maxLength: 20 },
    street: { required: true, maxLength: 100 },
    city: { required: true, maxLength: 50 },
    state: { required: true, maxLength: 50 },
    pincode: { required: true, numeric: true, minLength: 6, maxLength: 7 }, // 7 to account for space in "000 000" format
  },

  // Vehicle Details
  vehicleDetails: {
    model: { required: true },
    colour: { required: true },
    financeMode: { required: true },
  },

  // Exchange Vehicle Details
  exchangeVehicle: {
    hasExchange: { required: true },
    exchangeModel: { required: true },
    exchangeYear: { required: true, numeric: true, min: 1900, max: new Date().getFullYear() },
    exchangeKmsDriven: { required: true, numeric: true, min: 0 },
  },

  // Registration Requirements
  registration: {
    hasSpecialNumber: { required: true },
    registrationType: { required: true },
    expectedSpecialNumber: { required: false },
  },

  // Insurance Details
  insurance: {
    insuranceType: { required: true },
    nomineeName: { required: true, minLength: 2, maxLength: 50 },
    nomineeAge: { required: true, numeric: true, min: 18, max: 100 },
    nomineeRelationship: { required: true },
  },

  // Documents
  documents: {
    // Document validation will be handled separately
  },
};

/**
 * Validate an entire form section
 * @param data The form data to validate
 * @param section The form section name
 * @returns Object with isValid flag and errors object
 */
export const validateFormSection = (data: any, section: string) => {
  const sectionRules = validationRules[section as keyof typeof validationRules];
  if (!sectionRules) {
    return { isValid: true, errors: {} };
  }

  const errors: Record<string, string> = {};
  let isValid = true;

  // Special handling for customer section
  if (section === "customer") {
    // Special handling for phone number
    if (data.phone) {
      // Remove any non-digit characters
      const digitsOnly = data.phone.replace(/\D/g, '');

      // Check if it's a valid 10-digit number
      if (digitsOnly.length !== 10) {
        errors["phone"] = "Phone number must be 10 digits";
        isValid = false;
      }
    }

    // Special handling for pincode
    if (data.pincode) {
      // Remove any non-digit characters
      const digitsOnly = data.pincode.replace(/\D/g, '');

      // Check if it's a valid 6-digit number
      if (digitsOnly.length !== 6) {
        errors["pincode"] = "Pincode must be 6 digits";
        isValid = false;
      }
    }

    // Special handling for email
    if (data.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
      errors["email"] = "Please enter a valid email address";
      isValid = false;
    }
  }

  // Special handling for registration section
  if (section === "registration") {
    // Make expectedSpecialNumber optional even when hasSpecialNumber is "yes"
    // No validation needed for expectedSpecialNumber
  }

  // Special handling for exchange vehicle section
  if (section === "exchangeVehicle") {
    // If hasExchange is "no", then no other fields are required
    if (data.hasExchange === "no") {
      // Skip validation for other fields
      return { isValid: true, errors: {} };
    }

    // If exchangeModel is "none" or null, it means hasExchange is "no"
    if (data.exchangeModel === "none" || data.exchangeModel === null) {
      // Skip validation for other fields
      return { isValid: true, errors: {} };
    }

    // If hasExchange is "yes" but not set, mark it as required
    if (!data.hasExchange) {
      errors["hasExchange"] = "Please select whether you want to exchange a vehicle";
      isValid = false;
    }

    // If hasExchange is "yes", all other fields are required
    if (data.hasExchange === "yes") {
      // Check if exchangeModel is filled
      if (!data.exchangeModel || data.exchangeModel === "") {
        errors["exchangeModel"] = "Please select a model for your exchange vehicle";
        isValid = false;
      }

      // Check if exchangeYear is filled
      if (!data.exchangeYear || data.exchangeYear === "") {
        errors["exchangeYear"] = "Please select a year for your exchange vehicle";
        isValid = false;
      }

      // Check if exchangeKmsDriven is filled
      if (!data.exchangeKmsDriven || data.exchangeKmsDriven === "") {
        errors["exchangeKmsDriven"] = "Please enter kilometers driven for your exchange vehicle";
        isValid = false;
      }
      // Special handling for exchangeKmsDriven
      else if (data.exchangeKmsDriven) {
        // Validate that it's a number
        if (!/^\d+$/.test(data.exchangeKmsDriven)) {
          errors["exchangeKmsDriven"] = "Kilometers driven must contain only numbers";
          isValid = false;
        } else if (Number(data.exchangeKmsDriven) > 9999999) {
          errors["exchangeKmsDriven"] = "Kilometers driven value is too large";
          isValid = false;
        }
      }
    }
  }

  // Special handling for insurance section
  if (section === "insurance") {
    // If insuranceType is "none", then no other fields are required
    if (data.insuranceType === "none") {
      // Skip validation for other fields
      return { isValid: true, errors: {} };
    }

    // Special handling for nomineeAge
    if (data.insuranceType !== "none" && data.nomineeAge) {
      // Validate that it's a number
      if (!/^\d+$/.test(data.nomineeAge)) {
        errors["nomineeAge"] = "Nominee age must contain only numbers";
        isValid = false;
      } else if (Number(data.nomineeAge) < 18) {
        errors["nomineeAge"] = "Nominee age must be at least 18";
        isValid = false;
      } else if (Number(data.nomineeAge) > 100) {
        errors["nomineeAge"] = "Nominee age cannot exceed 100";
        isValid = false;
      }
    }
  }

  // Validate each field in the section
  Object.entries(sectionRules).forEach(([field, rules]) => {
    // Skip expectedSpecialNumber if hasSpecialNumber is "no" since we already handled it above
    if (section === "registration" && field === "expectedSpecialNumber" && data.hasSpecialNumber === "no") {
      return;
    }

    // Skip insurance fields if insuranceType is "none"
    if (section === "insurance" && data.insuranceType === "none" && field !== "insuranceType") {
      return;
    }

    const value = data[field];
    const result = validateField(value, rules, field);

    if (!result.isValid) {
      errors[field] = result.error || `Invalid ${field}`;
      isValid = false;
    }
  });

  return { isValid, errors };
};

/**
 * Special validation for file uploads
 * @param files The files to validate
 * @returns Object with isValid flag and errors object
 */
export const validateFiles = (files: Record<string, any>) => {
  const errors: Record<string, string> = {};
  let isValid = true;

  // Only Aadhar card and PAN card are required
  const requiredDocs = ['aadhar', 'pan'];

  // Document display names for error messages
  const docDisplayNames = {
    'aadhar': 'Aadhar card',
    'pan': 'PAN card',
    'photo': 'Photo',
    'license': 'Driving License',
    'other': 'Other documents'
  };

  requiredDocs.forEach(docType => {
    if (!files[docType] || !files[docType].fileSelected) {
      errors[docType] = `${docDisplayNames[docType]} document is required`;
      isValid = false;
    }
  });

  // Add attemptedSubmit flag to errors to track if form submission was attempted
  if (!isValid) {
    errors.attemptedSubmit = 'true';
  }

  return { isValid, errors };
};
