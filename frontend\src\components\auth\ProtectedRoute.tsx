import React from 'react';
import { Navigate, useLocation, Outlet } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import logger from '../../utils/logger';

interface ProtectedRouteProps {
  children?: React.ReactNode;
  requireAuth?: boolean;
  redirectTo?: string;
  redirectPath?: string; // For compatibility with existing usage
}

/**
 * Protected Route Component
 * Handles authentication-based route protection
 */
export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requireAuth = true,
  redirectTo = '/login',
  redirectPath = '/login' // For compatibility
}) => {
  const { isAuthenticated, isLoading } = useAuth();
  const location = useLocation();

  // Use redirectPath if provided (for compatibility), otherwise use redirectTo
  const redirectTarget = redirectPath || redirectTo;

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#1e1f20] flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#640064]"></div>
      </div>
    );
  }

  // If authentication is required but user is not authenticated
  if (requireAuth && !isAuthenticated) {
    logger.auth('route_access_denied', false, {
      route: location.pathname,
      reason: 'not_authenticated'
    });

    // Redirect to login with return URL
    return <Navigate to={redirectTarget} state={{ from: location }} replace />;
  }

  // If user is authenticated but trying to access auth pages (login)
  if (!requireAuth && isAuthenticated) {
    logger.auth('auth_page_redirect', true, {
      route: location.pathname,
      reason: 'already_authenticated'
    });

    // Redirect to dashboard if already authenticated
    return <Navigate to="/dashboard" replace />;
  }

  // Log successful route access
  if (requireAuth && isAuthenticated) {
    logger.auth('route_access_granted', true, {
      route: location.pathname
    });
  }

  // If children are provided, render them (wrapper pattern)
  // If no children, render Outlet (route pattern)
  return children ? <>{children}</> : <Outlet />;
};

/**
 * Public Route Component
 * For routes that should only be accessible when NOT authenticated (like login)
 */
export const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <ProtectedRoute requireAuth={false}>
      {children}
    </ProtectedRoute>
  );
};

export default ProtectedRoute;
