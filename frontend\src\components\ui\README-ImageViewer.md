# Image Viewer Component

A lightweight and minimal image viewer component for displaying documents and images in a modal overlay.

## Features

- **Lightweight & Minimal**: Clean, simple design that focuses on the image
- **Zoom Controls**: Zoom in/out with buttons or keyboard shortcuts
- **Pan & Drag**: Click and drag to pan around zoomed images
- **Keyboard Shortcuts**: 
  - `+` or `=`: Zoom in
  - `-`: Zoom out
  - `0`: Reset zoom
  - `Esc`: Close viewer
- **Loading States**: Shows loading spinner while image loads
- **Error Handling**: Displays error message if image fails to load
- **Responsive**: Works on all screen sizes
- **Accessibility**: Proper ARIA labels and keyboard navigation

## Usage

```tsx
import ImageViewer from './components/ui/image-viewer';

const MyComponent = () => {
    const [imageViewer, setImageViewer] = useState({
        isOpen: false,
        imageUrl: '',
        title: ''
    });

    const openImage = (url: string, title: string) => {
        setImageViewer({
            isOpen: true,
            imageUrl: url,
            title: title
        });
    };

    const closeImage = () => {
        setImageViewer({
            isOpen: false,
            imageUrl: '',
            title: ''
        });
    };

    return (
        <div>
            <button onClick={() => openImage('path/to/image.jpg', 'My Image')}>
                View Image
            </button>
            
            <ImageViewer
                imageUrl={imageViewer.imageUrl}
                isOpen={imageViewer.isOpen}
                onClose={closeImage}
                title={imageViewer.title}
            />
        </div>
    );
};
```

## Props

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `imageUrl` | string | Yes | URL of the image to display |
| `isOpen` | boolean | Yes | Whether the viewer is open |
| `onClose` | function | Yes | Callback function when viewer is closed |
| `title` | string | No | Title to display in the header (default: "Document") |

## Integration with Document Viewer

The component is automatically integrated with the CustomerDetailed modal. When viewing documents:

1. **Image files** (JPG, PNG, GIF, etc.) open in the image viewer
2. **Non-image files** (PDF, DOC, etc.) open in a new tab

The file type detection is handled by the `isImageFile` utility function in `utils/fileUtils.ts`.

## Styling

The component uses Tailwind CSS classes and follows the existing design system:
- Dark overlay background
- White controls with hover effects
- Smooth transitions and animations
- Consistent with the existing modal system

## Demo

A demo component is available at `components/ui/image-viewer-demo.tsx` for testing and development purposes.

## Browser Support

- Modern browsers with ES6+ support
- Requires React 16.8+ (uses hooks)
- Uses React Portals for modal rendering
