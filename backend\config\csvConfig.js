/**
 * Simple CSV Configuration
 * Basic configuration for monthly CSV file storage
 */

export const csvConfig = {
  // Basic settings
  enabled: process.env.CSV_ENABLED !== 'false',

  // File configuration
  files: {
    baseDir: 'csv-exports',
    monthlyPattern: 'vehicle-registrations-{year}-{month}.csv'
  },

  // CSV Headers for the monthly file
  headers: [
    'Timestamp',
    'Vehicle ID',
    'Status',
    'Customer Name',
    'Phone',
    'Email',
    'Alt Phone',
    'House No',
    'Street',
    'City',
    'State',
    'Pincode',
    'Vehicle Model',
    'Vehicle Colour',
    'Finance Mode',
    'Has Special Number',
    'Expected Special Number',
    'Registration Type',
    'Has Exchange',
    'Exchange Model',
    'Exchange Year',
    'Exchange KMs Driven',
    'Insurance Type',
    'Nominee Name',
    'Nominee Age',
    'Nominee Relationship',
    'Documents Uploaded',
    'Documents List',
    'Created At',
    'Updated At',
    'Completed At',
    'Processing Time (seconds)'
  ]
};

export default csvConfig;
