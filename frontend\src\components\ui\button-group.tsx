import React from 'react';
import { Button } from './button';

interface ButtonGroupProps {
  currentStep: number;
  onNext: () => void;
  onBack: () => void;
  onSubmit?: () => void;
  totalSteps: number;
  disabled?: boolean;
  nextDisabled?: boolean; // Additional condition to disable next button
}

export const ButtonGroup = ({ currentStep, onNext, onBack, onSubmit, totalSteps, disabled = false, nextDisabled = false }: ButtonGroupProps) => {
  // First form (step 1) only shows Next button
  if (currentStep === 1) {
    return (
      <div className="mt-6 sm:mt-8 md:mt-12 w-full transition-all duration-300 ease-in-out">
        <Button
          variant="next"
          onClick={onNext}
          disabled={disabled || nextDisabled}
        >
          Next
        </Button>
      </div>
    );
  }

  // Last form (step 6) shows Back and Submit buttons
  if (currentStep === totalSteps) {
    return (
      <div className="mt-6 sm:mt-8 md:mt-12 grid grid-cols-2 gap-3 sm:gap-4 md:gap-6 w-full transition-all duration-300 ease-in-out">
        <Button
          variant="back"
          onClick={onBack}
          disabled={disabled}
        >
          Back
        </Button>
        <Button
          variant="next"
          onClick={onSubmit}
          disabled={disabled}
        >
          Submit
        </Button>
      </div>
    );
  }

  // Forms 2-5 show both Back and Next buttons
  return (
    <div className="mt-6 sm:mt-8 md:mt-12 grid grid-cols-2 gap-3 sm:gap-4 md:gap-6 w-full">
      <Button
        variant="back"
        onClick={onBack}
        disabled={disabled}
      >
        Back
      </Button>
      <Button
        variant="next"
        onClick={onNext}
        disabled={disabled || nextDisabled}
      >
        Next
      </Button>
    </div>
  );
};