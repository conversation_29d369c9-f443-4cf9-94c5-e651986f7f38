import mongoose from 'mongoose';
import logger from '../utils/logger.js';

// Track connection state
let isConnected = false;
let connectionAttempts = 0;
const MAX_RETRIES = 5;

/**
 * Connect to MongoDB Atlas with retry logic
 * @returns {Promise<boolean>} Connection status
 */
const connectDB = async () => {
  try {
    // If already connected, return
    if (isConnected) {
      return true;
    }

    // Increment connection attempts
    connectionAttempts++;

    // Set connection options optimized for MongoDB Atlas
    const options = {
      serverSelectionTimeoutMS: 10000, // Timeout after 10 seconds (increased for Atlas)
      socketTimeoutMS: 45000, // Close sockets after 45 seconds of inactivity
      family: 4, // Use IPv4, skip trying IPv6
      retryWrites: true,
      w: 'majority', // Write concern for better data durability
      maxPoolSize: 10, // Connection pool size
      minPoolSize: 5, // Minimum connections maintained in the pool
      maxIdleTimeMS: 30000 // Close idle connections after 30 seconds
    };

    // Get MongoDB URI from environment variables
    const uri = process.env.MONGODB_URI;

    await mongoose.connect(uri, options);

    isConnected = true;
    connectionAttempts = 0;

    // Add connection event listeners
    mongoose.connection.on('error', (err) => {
      logger.error('MongoDB Atlas connection error:', err);
      isConnected = false;
    });

    mongoose.connection.on('disconnected', () => {
      logger.warn('Database disconnected');
      isConnected = false;
      // Try to reconnect
      setTimeout(() => {
        connectDB();
      }, 5000);
    });

    // Log when successfully reconnected
    mongoose.connection.on('reconnected', () => {
      logger.info('Database reconnected');
      isConnected = true;
    });

    return true;
  } catch (error) {
    logger.error(`MongoDB Atlas Connection Error: ${error.message}`);
    isConnected = false;

    // Retry connection with exponential backoff
    if (connectionAttempts < MAX_RETRIES) {
      const retryDelay = Math.min(1000 * 2 ** connectionAttempts, 30000);

      setTimeout(() => {
        connectDB();
      }, retryDelay);

      return false;
    } else {
      logger.error(`Failed to connect to database after ${MAX_RETRIES} attempts`);
      // Don't exit the process, let the application run in offline mode
      return false;
    }
  }
};

/**
 * Check if MongoDB Atlas is connected
 * @returns {boolean} Connection status
 */
export const isMongoConnected = () => {
  return isConnected && mongoose.connection.readyState === 1;
};

export default connectDB;
