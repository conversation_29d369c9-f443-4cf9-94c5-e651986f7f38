import React, { useState, useEffect } from "react";
import { UploadIcon } from "lucide-react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../store/store";
import { updateFormField, validateSection, resetSection } from "../../store/formSlice";
import { vehicleAPI } from "../../services/api";

interface FileSection {
  id: string;
  name: string;
  file: File | null;
  fileName?: string;
  fileType?: string;
  uploadStatus?: 'idle' | 'uploading' | 'success' | 'error';
  uploadProgress?: number;
  longDescription?: string;
  errorMessage?: string; // Added to store specific error messages
}

export const FileUploads = () => {
  const dispatch = useDispatch();
  const { formData, errors } = useSelector((state: RootState) => state.form);
  const vehicleId = useSelector((state: RootState) => state.api.vehicleId);
  const [sections, setSections] = useState<FileSection[]>([
    { id: "aadhar", name: "<PERSON><PERSON><PERSON> card", file: null, uploadStatus: 'idle', uploadProgress: 0 },
    { id: "pan", name: "PAN card", file: null, uploadStatus: 'idle', uploadProgress: 0 },
    { id: "photo", name: "Photo", file: null, uploadStatus: 'idle', uploadProgress: 0 },
    { id: "license", name: "Driving License", file: null, uploadStatus: 'idle', uploadProgress: 0 },
    { id: "other", name: "Other documents", file: null, uploadStatus: 'idle', uploadProgress: 0, longDescription: "Salary Statement, etc..." },
  ]);

  // Initialize documents object if it doesn't exist
  useEffect(() => {
    if (!formData.documents) {
      dispatch(updateFormField({
        field: "documents",
        value: {}
      }));
    }

    // No automatic validation on load
  }, [dispatch, formData]);

  useEffect(() => {
    if (formData.documents && Object.keys(formData.documents).length > 0) {
      const updatedSections = [...sections];
      Object.entries(formData.documents).forEach(([key, value]: any) => {
        const sectionIndex = updatedSections.findIndex(s => s.id === key);
        if (sectionIndex !== -1 && value) {
          updatedSections[sectionIndex] = {
            ...updatedSections[sectionIndex],
            fileName: value.fileName,
            fileType: value.fileType,
            uploadStatus: value.fileSelected ? 'success' : 'idle'
          };
        }
      });
      setSections(updatedSections);
    }
  }, [formData.documents]);

  const handleDrop = (id: string) => async (e: React.DragEvent) => {
    e.preventDefault();
    const file = e.dataTransfer.files[0];
    if (file) await updateFile(id, file);
  };

  const handleFileInput = (id: string) => async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) await updateFile(id, e.target.files[0]);
  };

  // Base64 conversion has been removed as per requirements

  // Function to compress a PDF file (reduces file size)
  const compressFile = async (file: File): Promise<File> => {
    // If it's not a PDF, just return the original file
    if (!file.type.includes('pdf')) {
      return file;
    }

    try {
      // For PDFs, we'll use a simple approach - just return the original for now
      // In a production app, you would use a library like pdf-lib to compress PDFs
      console.log(`File ${file.name} size: ${file.size / 1024} KB`);
      return file;
    } catch (error) {
      console.error('Error compressing file:', error);
      return file;
    }
  };

  const updateFile = async (id: string, file: File) => {
    try {
      // Update section status to uploading
      updateSectionStatus(id, 'uploading', 0);

      // Check for video files first (prevent video uploads)
      const videoMimeTypes = [
        'video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/flv',
        'video/webm', 'video/mkv', 'video/3gp', 'video/m4v'
      ];

      const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv', '.3gp', '.m4v'];
      const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();

      if (videoMimeTypes.includes(file.type) || videoExtensions.includes(fileExtension)) {
        updateSectionStatus(id, 'error', 0, 'Video files are not allowed. Please upload documents only (PDF, JPG, PNG, DOC, DOCX).');
        return;
      }

      // Check file size and enforce 2MB limit
      const fileSizeInMB = file.size / (1024 * 1024);

      // Check if file exceeds 2MB limit
      if (fileSizeInMB > 2) {
        // Update section status to error with a specific message about file size
        updateSectionStatus(id, 'error', 0, `File is too large (${fileSizeInMB.toFixed(2)} MB). Maximum allowed size is 2MB.`);

        // Don't proceed with the file upload
        return;
      }

      // Try to compress the file if it's a PDF (though we already enforce 2MB limit)
      const processedFile = await compressFile(file);

      // Update local state with the file object for UI purposes
      const updatedSections = sections.map(section =>
        section.id === id ? {
          ...section,
          file: processedFile,
          fileName: processedFile.name,
          fileType: processedFile.type,
          uploadStatus: 'success', // Set to success immediately for UI feedback
          uploadProgress: 100,
          errorMessage: undefined // Clear any previous error message
        } : section
      );

      setSections(updatedSections);

      // For Redux, store only metadata and the file object (but not in localStorage)
      const documentsData = { ...formData.documents };

      documentsData[id] = {
        fileName: processedFile.name,
        fileType: processedFile.type,
        fileSize: processedFile.size,
        fileSelected: true,
        file: processedFile, // Store the file object for later use
        uploadDate: new Date().toISOString()
      };

      // Update Redux store with the file data
      dispatch(updateFormField({ field: "documents", value: documentsData }));

      // Files will be uploaded to server when the form is submitted (final submission only)
      // Update status to show file is ready for submission
      updateSectionStatus(id, 'success', 100, 'File selected - will upload on form submission');

    } catch (error) {
      updateSectionStatus(id, 'error', 0, `Error processing file: ${error}`);
    }
  };

  // Helper function to update section status
  const updateSectionStatus = (
    id: string,
    status: 'idle' | 'uploading' | 'success' | 'error',
    progress: number,
    errorMessage?: string
  ) => {
    setSections(prevSections =>
      prevSections.map(section =>
        section.id === id
          ? {
              ...section,
              uploadStatus: status,
              uploadProgress: progress,
              // Only set errorMessage if provided and status is error
              ...(status === 'error' && errorMessage ? { errorMessage } : {})
            }
          : section
      )
    );

    // Also update the progress bar element directly for immediate feedback
    setTimeout(() => {
      const progressElement = document.getElementById(`upload-progress-${id}`);
      if (progressElement && status === 'uploading') {
        progressElement.style.width = `${progress}%`;
      }
    }, 0);
  };

  // Check if a field has an error and should show validation
  const hasError = (fieldId: string) => {
    // Only show errors if the form has been attempted to be submitted
    return errors && errors[fieldId] && errors.attemptedSubmit;
  };

  // Handle form reset
  const handleResetForm = () => {
    // Reset the documents section in Redux
    dispatch(resetSection("documents"));

    // Reset local state
    setSections(sections.map(section => ({
      ...section,
      file: null,
      fileName: undefined,
      fileType: undefined,
      uploadStatus: 'idle',
      uploadProgress: 0
    })));
  };

  const renderSection = (section: FileSection) => {
    // Only Aadhar card and PAN card are required
    const isRequired = section.id === 'aadhar' || section.id === 'pan';
    const hasFieldError = hasError(section.id);

    // Required field marker component - consistent with other forms
    const RequiredMarker = () => (
      <span className="text-red-500 ml-1">*</span>
    );

    return (
      <div key={section.id} className="w-full h-full space-y-1">
        <label className="text-[#e3e3e3] text-sm font-normal">
          {section.name}
          {isRequired && <RequiredMarker />}
          {section.longDescription && (
            <span className="text-[10px] sm:text-xs text-[#999999] ml-2 font-normal">
              {section.longDescription}
            </span>
          )}
        </label>

        <div
          className={`border border-dashed rounded-lg p-3 flex flex-col items-center justify-center space-y-1 h-[160px] sm:h-[160px] w-full overflow-hidden ${
            isRequired && hasFieldError && !section.file ? "border-red-500" : "border-[#333333]"
          }`}
          onDrop={handleDrop(section.id)}
          onDragOver={e => e.preventDefault()}
        >
          <UploadIcon className="w-5 h-5 text-[#640064] mb-1" />
          <p className="text-xs text-center text-[#e3e3e3]">
            select your file or drag and drop
          </p>
          <p className="text-[10px] text-center text-[#666666]">only pdf, jpg, png, doc accepted (max 2MB) • no videos</p>

          {/* Browse button section */}
          <div className="flex items-center justify-center mt-1">
            {section.uploadStatus !== 'uploading' && (
              <label className="cursor-pointer">
                <input
                  type="file"
                  className="hidden"
                  accept=".pdf,.jpg,.jpeg,.png,.doc,.docx,application/pdf,image/jpeg,image/png,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                  onChange={handleFileInput(section.id)}
                />
                <span className="bg-[#640064] text-white px-4 py-1 rounded-md text-xs font-medium hover:bg-[#7a007a] transition-colors">
                  Browse
                </span>
              </label>
            )}
          </div>

          {/* Upload progress and status section - minimized to match screenshot */}
          <div className="w-full h-[10px] flex flex-col justify-center">
            {section.uploadStatus === 'uploading' ? (
              <>
                <div className="h-1 bg-gray-300 rounded-full overflow-hidden">
                  <div
                    id={`upload-progress-${section.id}`}
                    className="h-full bg-[#640064] transition-all duration-300"
                    style={{ width: `${section.uploadProgress}%` }}
                  ></div>
                </div>
                <p className="text-[8px] text-center mt-0.5 text-[#e3e3e3]">Uploading... {section.uploadProgress}%</p>
              </>
            ) : section.file ? (
              <>
                {section.uploadStatus === 'success' && (
                  <p className="text-[9px] text-green-500 text-center">
                    {section.errorMessage || 'File ready for submission'}
                  </p>
                )}
                {section.uploadStatus === 'error' && (
                  <p className="text-[9px] text-red-500 text-center">
                    {section.errorMessage ?
                      (section.errorMessage.includes('too large') ? 'File too large' : 'Selection failed')
                      : 'Selection failed'}
                  </p>
                )}
                {section.uploadStatus === 'idle' && section.file && (
                  <p className="text-[9px] text-blue-500 text-center">File selected</p>
                )}
              </>
            ) : (
              <div className="h-full"></div> /* Empty div to maintain height when no file */
            )}
          </div>
        </div>

        {/* Error message below the upload box */}
        {isRequired && !section.file && hasFieldError && (
          <p className="text-red-500 text-[10px] mt-1">
            {`${section.name} is required`}
          </p>
        )}
      </div>
    );
  };

  return (
    <div className="max-w-[800px] mx-auto">
      <div className="space-y-4">
        {/* First row with 3 upload boxes */}
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          {sections.slice(0, 3).map(renderSection)}
        </div>

        {/* Second row with 2 upload boxes */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div className="w-full">
            {renderSection(sections[3])}
          </div>
          <div className="w-full">
            {renderSection(sections[4])}
          </div>
        </div>
      </div>
    </div>
  );
};
