PORT=10000
MONGODB_URI=mongodb+srv://zaidkhan4717:<EMAIL>/q5xt1?retryWrites=true&w=majority&appName=Cluster0
NODE_ENV=production
JWT_SECRET=q5xt1-secure-jwt-secret-for-production
JWT_EXPIRE=7d
JWT_COOKIE_EXPIRE=7
# CORS Configuration - Production
FRONTEND_URL=https://q5-x-preview.vercel.app
CORS_ORIGINS=https://q5-x-preview.vercel.app,https://q5x-bsem-zaidgit26s-projects.vercel.app,https://q5x-bsem.vercel.app,https://frontend-rwwb.onrender.com

# File Storage Configuration
USE_S3_STORAGE=true

# AWS S3 Configuration
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=8AQDCorRZppEsDSruz5szborGDurTmXGS8is1DFG
AWS_REGION=eu-north-1
AWS_S3_BUCKET_NAME=quant5x

# reCAPTCHA Configuration (works on localhost and production)
RECAPTCHA_SITE_KEY=6Le3yVErAAAAAGjTP9KwEzMyE5f5s_rsLUUHekl8
RECAPTCHA_SECRET_KEY=6Le3yVErAAAAAEyyeq51I3ECkRc-P0LBYtpCwgcD
