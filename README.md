# Q5XT1 Vehicle Registration Management System

Enterprise-grade vehicle registration management system built with modern web technologies for streamlined vehicle registration processes.

## 🏗️ System Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React.js      │    │   Node.js       │    │   MongoDB       │
│   Frontend      │◄──►│   Backend       │◄──►│   Atlas         │
│   (TypeScript)  │    │   (Express)     │    │   (Cloud DB)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Technology Stack
- **Frontend**: React 18 + TypeScript + Tailwind CSS + Vite
- **Backend**: Node.js + Express + MongoDB + JWT Authentication
- **Database**: MongoDB Atlas (Cloud-hosted)
- **Deployment**: Vercel (Frontend) + Render/Railway (Backend)

## 🚀 Quick Start

### Prerequisites
- **Node.js** v18+ (LTS recommended)
- **MongoDB Atlas** account
- **Git** for version control

### One-Command Setup

```bash
# Clone repository
git clone <repository-url>
cd Q5XT1-Vehicle-Registration

# Setup backend
cd backend && npm install && cp .env.example .env
# Configure MongoDB URI in .env file
npm run dev &

# Setup frontend (in new terminal)
cd ../frontend && npm install && cp .env.example .env
# Configure API URL in .env file
npm run dev
```

### Access Points
- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:5000
- **API Health**: http://localhost:5000/health

## 📁 Project Structure

```
Q5XT1-Vehicle-Registration/
├── 📁 frontend/                    # React.js Application
│   ├── 📁 src/
│   │   ├── 📁 components/          # Reusable UI components
│   │   ├── 📁 screens/             # Page-level components
│   │   ├── 📁 services/            # API integration
│   │   ├── 📁 hooks/               # Custom React hooks
│   │   ├── 📁 utils/               # Utility functions
│   │   └── 📁 assets/              # Static assets
│   ├── 📄 package.json
│   ├── 📄 tailwind.config.js
│   ├── 📄 vite.config.ts
│   └── 📄 README.md                # Frontend documentation
│
├── 📁 backend/                     # Node.js API Server
│   ├── 📁 controllers/             # Route controllers
│   ├── 📁 models/                  # Database models
│   ├── 📁 routes/                  # API route definitions
│   ├── 📁 middleware/              # Custom middleware
│   ├── 📁 services/                # Business logic
│   ├── 📁 utils/                   # Utility functions
│   ├── 📁 config/                  # Configuration files
│   ├── 📁 docs/                    # API documentation
│   ├── 📁 scripts/                 # Maintenance scripts
│   ├── 📁 logs/                    # Application logs
│   ├── 📁 uploads/                 # File storage
│   ├── 📁 csv-exports/             # CSV export files
│   ├── 📄 package.json
│   ├── 📄 server.js
│   └── 📄 README.md                # Backend documentation
│
├── 📄 README.md                    # Main project documentation
└── 📄 .gitignore
```

## 🔧 Core Features

### 🎯 Business Features
- **Multi-Step Registration Form** - Progressive vehicle registration process
- **Document Management** - Secure file upload and storage
- **Admin Dashboard** - Comprehensive vehicle management interface
- **CSV Export System** - Automated data export for auditing
- **Real-time Validation** - Instant form validation and feedback
- **Progress Tracking** - Visual completion indicators
- **Mobile Responsive** - Optimized for all device sizes

### ⚙️ Technical Features
- **JWT Authentication** - Secure token-based authentication
- **RESTful API** - Clean, predictable API design
- **File Upload Security** - Type validation and size limits
- **Comprehensive Logging** - Winston-based logging system
- **Error Handling** - Centralized error management
- **Rate Limiting** - API abuse prevention
- **CORS Support** - Cross-origin resource sharing
- **Security Headers** - Helmet.js protection
- **Input Validation** - Comprehensive data validation
- **Auto-save Functionality** - Prevent data loss

## 🚀 Deployment Guide

### 🌐 Production Deployment

#### Frontend (Vercel)
```bash
cd frontend
npm run build
vercel --prod
```

#### Backend (Render)
```bash
# Connect GitHub repository to Render
# Configure environment variables
# Auto-deploy on git push
```

#### Alternative: Railway
```bash
cd backend
npm install -g @railway/cli
railway login && railway init && railway up
```

### 🔧 Environment Configuration

#### Backend Environment (.env)
```bash
# Server Configuration
NODE_ENV=production
PORT=5000

# Database
MONGODB_URI=mongodb+srv://user:<EMAIL>/q5xt1-prod

# Authentication
JWT_SECRET=your-super-secure-256-bit-secret-key
JWT_EXPIRE=7d

# CORS & Security
FRONTEND_URL=https://your-domain.com
CORS_ORIGINS=https://your-domain.com,https://www.your-domain.com

# Features
CSV_ENABLED=true
UPLOAD_PATH=uploads
```

#### Frontend Environment (.env)
```bash
# API Configuration
VITE_API_BASE_URL=https://your-backend.com/api
VITE_BACKEND_URL=https://your-backend.com

# Environment
VITE_NODE_ENV=production

# Features
VITE_ENABLE_MOCK_API=false
VITE_ENABLE_DEBUG=false
```

## 📡 API Documentation

### Base Endpoints
- **Health Check**: `GET /health`
- **Authentication**: `POST /api/auth/login`
- **Vehicle Management**: `GET|POST|PUT /api/vehicle`
- **File Upload**: `POST /api/vehicle/:id/documents`
- **CSV Export**: `GET /api/csv/files`

### Example API Usage
```bash
# Login
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}'

# Create Vehicle
curl -X POST http://localhost:5000/api/vehicle \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{"customer":{"name":"John Doe","phone":"**********"}}'

# Get All Vehicles
curl -X GET http://localhost:5000/api/vehicle \
  -H "Authorization: Bearer <token>"
```

## 🛠️ Development Workflow

### Backend Development
```bash
cd backend

# Development with hot reload
npm run dev

# Run tests
npm test

# Clean up old records
npm run cleanup

# Check logs
tail -f logs/combined-$(date +%Y-%m-%d).log
```

### Frontend Development
```bash
cd frontend

# Development server
npm run dev

# Type checking
npm run type-check

# Build for production
npm run build

# Preview production build
npm run preview
```

## 📚 Documentation

### Comprehensive Documentation
- **📖 Main Documentation** - This README
- **🔧 Backend Documentation** - [backend/README.md](backend/README.md)
- **🎨 Frontend Documentation** - [frontend/README.md](frontend/README.md)
- **📊 CSV System Guide** - [backend/docs/SIMPLE_CSV_SYSTEM.md](backend/docs/SIMPLE_CSV_SYSTEM.md)

### API Reference
- **Complete API Documentation** - See backend README
- **Authentication Endpoints** - JWT-based auth system
- **Vehicle Management** - CRUD operations
- **File Upload System** - Document management
- **CSV Export System** - Data export functionality

## 🔒 Security Features

### Authentication & Authorization
- **JWT Tokens** - Secure, stateless authentication
- **Route Guards** - Protected route access
- **Session Management** - Automatic token refresh
- **Role-based Access** - Admin/user permissions

### Data Security
- **Input Sanitization** - XSS prevention
- **File Upload Security** - Type and size validation
- **HTTPS Enforcement** - Secure data transmission
- **Environment Variables** - Secure configuration management

### API Security
- **Rate Limiting** - Prevent API abuse
- **CORS Protection** - Cross-origin security
- **Security Headers** - Helmet.js middleware
- **Request Validation** - Comprehensive input validation

## 📊 Monitoring & Logging

### Logging System
- **Winston Logger** - Professional logging framework
- **Structured Logs** - JSON-formatted log entries
- **Log Rotation** - Daily rotation with compression
- **Request Tracing** - Unique request IDs
- **Error Tracking** - Comprehensive error logging

### Log Files
```
backend/logs/
├── combined-YYYY-MM-DD.log    # All log levels
├── error-YYYY-MM-DD.log       # Error logs only
├── http-YYYY-MM-DD.log        # HTTP requests
├── exceptions-YYYY-MM-DD.log  # Uncaught exceptions
└── rejections-YYYY-MM-DD.log  # Promise rejections
```

### Health Monitoring
- **Health Check Endpoint** - `/health`
- **Database Connectivity** - MongoDB connection status
- **API Performance** - Response time monitoring
- **Error Rate Tracking** - Error frequency analysis

## 🐛 Troubleshooting

### Common Issues & Solutions

#### 🔌 Connection Issues
```bash
# MongoDB Connection Failed
# ✅ Check MongoDB Atlas connection string
# ✅ Verify network access settings in Atlas
# ✅ Ensure correct username/password

# CORS Errors
# ✅ Update FRONTEND_URL in backend .env
# ✅ Verify CORS_ORIGINS configuration
# ✅ Check API URL in frontend .env
```

#### 📁 File Upload Issues
```bash
# Upload Failures
# ✅ Check upload directory permissions
# ✅ Verify file size limits (5MB default)
# ✅ Ensure correct file types (.pdf, .jpg, .png)
# ✅ Check available disk space
```

#### 🔧 Build Issues
```bash
# Frontend Build Errors
cd frontend
rm -rf node_modules package-lock.json
npm install
npm run build

# Backend Issues
cd backend
npm run cleanup
npm run verify-atlas
```

### Debug Mode
```bash
# Enable debug logging
DEBUG=* npm run dev

# Frontend debug mode
VITE_ENABLE_DEBUG=true npm run dev

# Check application logs
tail -f backend/logs/combined-$(date +%Y-%m-%d).log
```

## 🤝 Contributing

### Development Setup
1. **Fork** the repository
2. **Clone** your fork locally
3. **Create** a feature branch
4. **Make** your changes
5. **Test** thoroughly
6. **Submit** a pull request

### Code Standards
- **ESLint** - Code linting
- **Prettier** - Code formatting
- **TypeScript** - Type safety
- **JSDoc** - Code documentation

## 📞 Support & Maintenance

### Getting Help
1. **📖 Check Documentation** - Review README files
2. **🔍 Check Logs** - Review application logs
3. **⚙️ Verify Configuration** - Check environment variables
4. **🏥 Health Check** - Test `/health` endpoint
5. **🌐 Network Testing** - Verify API connectivity

### Maintenance Tasks
```bash
# Clean up old records
npm run cleanup

# Verify database connection
npm run verify-atlas

# Update dependencies
npm audit fix

# Backup database
# (Use MongoDB Atlas backup features)
```

---

## 🎯 Project Status

**✅ Production Ready** | **🔒 Secure** | **📊 Monitored** | **📚 Documented** | **🚀 Scalable**

### Latest Updates
- ✅ **CSV Export System** - Simplified monthly export
- ✅ **Security Enhancements** - JWT authentication
- ✅ **Logging System** - Comprehensive logging
- ✅ **Documentation** - Complete documentation
- ✅ **Deployment Ready** - Production configuration

---

**Built with ❤️ using modern web technologies for efficient vehicle registration management**
