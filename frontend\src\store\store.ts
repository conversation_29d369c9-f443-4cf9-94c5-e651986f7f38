import { configureStore } from "@reduxjs/toolkit";
import formReducer from "./formSlice";
import apiReducer from "./apiSlice";
import { dashboardApiSlice } from "./dashboardApiSlice";
import { customerApiSlice } from "./customerApiSlice";
import { loadState, saveState } from "../utils/localStorage";

// Load persisted state from localStorage
const persistedState = loadState();

export const store = configureStore({
  reducer: {
    form: formReducer,
    api: apiReducer,
    [dashboardApiSlice.reducerPath]: dashboardApiSlice.reducer,
    [customerApiSlice.reducerPath]: customerApiSlice.reducer,
  },
  preloadedState: persistedState,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        // Ignore these action types
        ignoredActions: ['form/updateFormField', 'form/validateSection'],
        // Ignore these field paths in the state
        ignoredPaths: ['form.formData.documents'],
      },
    }).concat(dashboardApiSlice.middleware, customerApiSlice.middleware),
});

// Subscribe to store changes to save state to localStorage
store.subscribe(() => {
  saveState({
    form: store.getState().form,
    api: {
      vehicleId: store.getState().api.vehicleId,
      formSubmitted: store.getState().api.formSubmitted,
    },
  });
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;