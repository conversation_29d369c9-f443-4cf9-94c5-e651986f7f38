import { Download, Eye, Loader2 } from "lucide-react";
import api from "../../services/api";
import <PERSON><PERSON>, { <PERSON>dalSection, <PERSON>dalField, ModalDivider } from "./Modal";
import { useGetCustomerByIdQuery, useGetCustomerVehiclesQuery } from "../../store/customerApiSlice";
import { useState, useEffect } from "react";
import ImageViewer from "../../components/ui/image-viewer";
import { isImageFile } from "../../utils/fileUtils";

interface CustomerDetailsModalProps {
    isOpen: boolean;
    onClose: () => void;
    customer: {
        id?: string;
        name: string;
        phone: string;
        altPhone: string;
        email: string;
        address: string;
        model: string;
        color: string;
        finance: string;
        date?: string;
        // Vehicle ID for document access
        vehicleId?: string | null;
        // Vehicle relationship
        vehicles?: string[];
        // Additional fields for exchange vehicle
        exchangeModel?: string;
        exchangeYear?: string;
        exchangeKmsDriven?: string;
        // Registration fields
        hasSpecialNumber?: string;
        expectedSpecialNumber?: string;
        registrationType?: string;
        // Insurance fields
        insuranceType?: string;
        nomineeName?: string;
        nomineeAge?: string;
        nomineeRelationship?: string;
        // Document fields
        documents?: {
            aadhar?: {
                fileName?: string;
                fileType?: string;
                fileSize?: number;
            };
            pan?: {
                fileName?: string;
                fileType?: string;
                fileSize?: number;
            };
            photo?: {
                fileName?: string;
                fileType?: string;
                fileSize?: number;
            };
            license?: {
                fileName?: string;
                fileType?: string;
                fileSize?: number;
            };
            other?: {
                fileName?: string;
                fileType?: string;
                fileSize?: number;
            };
        };
    };
}

const CustomerDetailed = ({ isOpen, onClose, customer }: CustomerDetailsModalProps) => {
    if (!isOpen) return null;

    // State to store full customer details
    const [fullCustomerDetails, setFullCustomerDetails] = useState<any>(null);
    const [fullVehicleDetails, setFullVehicleDetails] = useState<any[]>([]);

    // State for image viewer
    const [imageViewer, setImageViewer] = useState({
        isOpen: false,
        imageUrl: '',
        title: ''
    });

    // Fetch full customer details when modal opens
    const {
        data: customerData,
        isLoading: isLoadingCustomer,
        isError: isCustomerError
    } = useGetCustomerByIdQuery(customer.id || '', {
        skip: !isOpen || !customer.id,
    });

    // Fetch customer vehicles when modal opens
    const {
        data: vehiclesData,
        isLoading: isLoadingVehicles,
        isError: isVehiclesError
    } = useGetCustomerVehiclesQuery(customer.id || '', {
        skip: !isOpen || !customer.id,
    });

    // Update state when data is loaded
    useEffect(() => {
        if (customerData) {
            setFullCustomerDetails(customerData);
        }
    }, [customerData]);

    useEffect(() => {
        if (vehiclesData) {
            setFullVehicleDetails(vehiclesData);
        }
    }, [vehiclesData]);

    // Determine if we're still loading data
    const isLoading = isLoadingCustomer || isLoadingVehicles;
    const isError = isCustomerError || isVehiclesError;

    // Format address for display
    const formatAddress = (address: string) => {
        return address && address !== "Address not available" ? address : "Address not available";
    };

    // Functions to handle document viewing and downloading
    const getFileUrl = (documentType: string, download = false) => {
        if (!customer.id) {
            console.error('Cannot get file URL: customer ID is missing');
            return '';
        }

        // Log customer info for debugging
        console.log('Customer info for document request:', {
            id: customer.id,
            name: customer.name,
            phone: customer.phone,
            vehicles: customer.vehicles
        });

        // Use the vehicle ID from the fetched vehicle details if available
        let vehicleId = '';

        if (fullVehicleDetails && fullVehicleDetails.length > 0) {
            // Use the ID from the fetched vehicle details
            vehicleId = fullVehicleDetails[0]._id;
            console.log(`Using vehicle ID from fetched vehicle details: ${vehicleId}`);
        } else if (typeof customer.vehicleId === 'string') {
            // Use the vehicleId property if it's a string
            vehicleId = customer.vehicleId;
            console.log(`Using vehicleId property: ${vehicleId}`);
        } else if (customer.vehicles && customer.vehicles.length > 0 && typeof customer.vehicles[0] === 'string') {
            // Use the first vehicle ID from the vehicles array if it's a string
            vehicleId = customer.vehicles[0];
            console.log(`Using vehicle ID from customer's vehicles array: ${vehicleId}`);
        } else {
            console.error('No valid vehicle ID found for document request');
            return '';
        }

        const url = `${api.defaults.baseURL}/vehicle/${vehicleId}/documents/${documentType}/file${download ? '?download=true' : ''}`;
        console.log(`Generated URL for ${documentType}: ${url}`);
        return url;
    };

    const viewFile = (documentType: string) => {
        const url = getFileUrl(documentType);
        if (!url) {
            alert('Cannot view file: Missing customer information');
            return;
        }

        // Get document info to check if it's an image
        let documentInfo = null;
        if (fullVehicleDetails && fullVehicleDetails.length > 0 && fullVehicleDetails[0]?.documents) {
            documentInfo = fullVehicleDetails[0].documents[documentType];
        } else if (customer.documents) {
            documentInfo = customer.documents[documentType];
        }

        // Check if the file is an image
        const fileName = documentInfo?.fileName;
        const fileType = documentInfo?.fileType;

        if (isImageFile(fileName, fileType)) {
            // Open in image viewer
            setImageViewer({
                isOpen: true,
                imageUrl: url,
                title: `${documentType.charAt(0).toUpperCase() + documentType.slice(1)} Document`
            });
        } else {
            // Open in new tab for non-image files (PDFs, etc.)
            console.log(`Opening file for viewing: ${documentType}`);
            window.open(url, '_blank');
        }
    };

    const downloadFile = async (documentType: string) => {
        const url = getFileUrl(documentType, true);
        if (!url) {
            alert('Cannot download file: Missing customer information');
            return;
        }

        console.log(`Downloading file: ${documentType}`);

        try {
            // Get document info for filename
            let documentInfo = null;
            if (fullVehicleDetails && fullVehicleDetails.length > 0 && fullVehicleDetails[0]?.documents) {
                documentInfo = fullVehicleDetails[0].documents[documentType];
            } else if (customer.documents) {
                documentInfo = customer.documents[documentType];
            }

            const fileName = documentInfo?.fileName || `${documentType}-document`;

            // Fetch the file and trigger download
            const response = await fetch(url);
            if (response.ok) {
                const blob = await response.blob();
                const downloadUrl = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = downloadUrl;
                link.download = fileName;
                document.body.appendChild(link);
                link.click();
                window.URL.revokeObjectURL(downloadUrl);
                document.body.removeChild(link);
            } else {
                console.error('Failed to download file:', response.statusText);
                alert('Failed to download file. Please try again.');
            }
        } catch (error) {
            console.error('Error downloading file:', error);
            alert('Error downloading file. Please try again.');
        }
    };

    // Check if exchange vehicle info exists and is not just "none"
    const hasExchangeVehicleInfo = (customer.exchangeModel && customer.exchangeModel !== "none") ||
                                  customer.exchangeYear ||
                                  customer.exchangeKmsDriven;

    // Check if registration info exists
    const hasRegistrationInfo = customer.hasSpecialNumber || customer.expectedSpecialNumber || customer.registrationType;

    // Check if insurance info exists
    const hasInsuranceInfo = customer.insuranceType || customer.nomineeName || customer.nomineeAge || customer.nomineeRelationship;

    // Check if documents info exists
    const hasDocumentsInfo = customer.documents && (
        customer.documents.aadhar ||
        customer.documents.pan ||
        customer.documents.photo ||
        customer.documents.license ||
        customer.documents.other
    );

    return (
        <Modal title="Customer Details" onClose={onClose}>
            {isLoading && (
                <div className="flex justify-center items-center py-10">
                    <div className="flex flex-col items-center">
                        <Loader2 className="w-10 h-10 text-[#ff00ff] animate-spin mb-4" />
                        <p className="text-gray-600">Loading customer details...</p>
                    </div>
                </div>
            )}

            {isError && (
                <div className="p-6 text-center">
                    <p className="text-red-500 mb-2">Error loading customer details</p>
                    <p className="text-gray-600">Please try again later</p>
                </div>
            )}

            {!isLoading && !isError && (
                <>
                    {/* Customer Info */}
                    <ModalSection title="Customer Information">
                        <div className="grid grid-cols-2 gap-y-2">
                            <div className="text-sm text-gray-700">Name</div>
                            <div className="text-sm text-right font-medium">
                                {(fullCustomerDetails?.name || customer.name || "").split('_')[0]}
                            </div>

                            <div className="text-sm text-gray-700">Phone Number</div>
                            <div className="text-sm text-right">
                                {(fullCustomerDetails?.phone || customer.phone || "").split('_')[0]}
                            </div>

                            <div className="text-sm text-gray-700">Alt Phone Number</div>
                            <div className="text-sm text-right">
                                {(fullCustomerDetails?.altPhone || customer.altPhone || "N/A") === "N/A"
                                    ? "N/A"
                                    : (fullCustomerDetails?.altPhone || customer.altPhone || "").split('_')[0]
                                }
                            </div>

                            <div className="text-sm text-gray-700">Email</div>
                            <div className="text-sm text-right">
                                {(fullCustomerDetails?.email || customer.email || "").split('_')[0]}
                            </div>

                            <div className="text-sm text-gray-700">Address</div>
                            <div className="text-sm text-right">
                                {fullCustomerDetails?.address?.fullAddress
                                    ? fullCustomerDetails.address.fullAddress
                                    : formatAddress(customer.address)}
                            </div>
                        </div>
                    </ModalSection>

            <ModalDivider />

            {/* Vehicle Info */}
            <ModalSection title="Vehicle Details">
                {fullVehicleDetails && fullVehicleDetails.length > 0 ? (
                    <div className="grid grid-cols-2 gap-y-2">
                        <div className="text-sm text-gray-700">Model</div>
                        <div className="text-sm text-right">{fullVehicleDetails[0]?.vehicleDetails?.model || customer.model}</div>

                        <div className="text-sm text-gray-700">Colour</div>
                        <div className="text-sm text-right">{fullVehicleDetails[0]?.vehicleDetails?.colour || customer.color}</div>

                        <div className="text-sm text-gray-700">Finance Mode</div>
                        <div className="text-sm text-right">{fullVehicleDetails[0]?.vehicleDetails?.financeMode || customer.finance}</div>

                        {fullVehicleDetails.length > 1 && (
                            <>
                                <div className="text-sm text-gray-700 col-span-2 mt-4 mb-2 font-medium">Additional Vehicles</div>
                                {fullVehicleDetails.slice(1).map((vehicle, index) => (
                                    <div key={index} className="col-span-2 bg-gray-50 p-2 rounded mb-2">
                                        <div className="grid grid-cols-2 gap-y-1">
                                            <div className="text-xs text-gray-700">Model</div>
                                            <div className="text-xs text-right">{vehicle.vehicleDetails?.model || "N/A"}</div>

                                            <div className="text-xs text-gray-700">Colour</div>
                                            <div className="text-xs text-right">{vehicle.vehicleDetails?.colour || "N/A"}</div>

                                            <div className="text-xs text-gray-700">Finance Mode</div>
                                            <div className="text-xs text-right">{vehicle.vehicleDetails?.financeMode || "N/A"}</div>
                                        </div>
                                    </div>
                                ))}
                            </>
                        )}
                    </div>
                ) : (
                    <div className="grid grid-cols-2 gap-y-2">
                        <div className="text-sm text-gray-700">Model</div>
                        <div className="text-sm text-right">{customer.model}</div>

                        <div className="text-sm text-gray-700">Colour</div>
                        <div className="text-sm text-right">{customer.color}</div>

                        <div className="text-sm text-gray-700">Finance Mode</div>
                        <div className="text-sm text-right">{customer.finance}</div>
                    </div>
                )}
            </ModalSection>

            <ModalDivider />

            {/* Exchange Vehicle Info */}
            <ModalSection title="Exchange Vehicle Details">
                {(() => {
                    // Try to get exchange vehicle data from nested structure first, then flat structure
                    const vehicleData = fullVehicleDetails && fullVehicleDetails.length > 0 ? fullVehicleDetails[0] : null;
                    const exchangeData = vehicleData?.exchangeVehicle || vehicleData || customer;

                    // Fix field mapping - database uses "model", "year", "kmsDriven" not "exchangeModel", etc.
                    const exchangeModel = exchangeData?.model || exchangeData?.exchangeModel;
                    const exchangeYear = exchangeData?.year || exchangeData?.exchangeYear;
                    const exchangeKmsDriven = exchangeData?.kmsDriven || exchangeData?.exchangeKmsDriven;
                    const hasExchange = exchangeData?.hasExchange;

                    // Clean logic: If hasExchange is "no" or no exchange data, only show status
                    if (hasExchange === "no" || (!exchangeModel && !exchangeYear && !exchangeKmsDriven && hasExchange !== "yes")) {
                        return (
                            <div className="grid grid-cols-2 gap-y-2">
                                <div className="text-sm text-gray-700">Status</div>
                                <div className="text-sm text-right">None</div>
                            </div>
                        );
                    }

                    // If exchange vehicle is selected, show all fields
                    if (exchangeModel || exchangeYear || exchangeKmsDriven || hasExchange === "yes") {
                        return (
                            <div className="grid grid-cols-2 gap-y-2">
                                <div className="text-sm text-gray-700">Model</div>
                                <div className="text-sm text-right">
                                    {exchangeModel || "N/A"}
                                </div>

                                <div className="text-sm text-gray-700">Year</div>
                                <div className="text-sm text-right">
                                    {exchangeYear || "N/A"}
                                </div>

                                <div className="text-sm text-gray-700">Kms Driven</div>
                                <div className="text-sm text-right">
                                    {exchangeKmsDriven || "N/A"}
                                </div>
                            </div>
                        );
                    } else {
                        return (
                            <div className="grid grid-cols-2 gap-y-2">
                                <div className="text-sm text-gray-700">Status</div>
                                <div className="text-sm text-right">None</div>
                            </div>
                        );
                    }
                })()}
            </ModalSection>

            <ModalDivider />

            {/* Registration Info */}
            <ModalSection title="Registration Details">
                {(() => {
                    // Try to get registration data from nested structure first, then flat structure
                    const vehicleData = fullVehicleDetails && fullVehicleDetails.length > 0 ? fullVehicleDetails[0] : null;
                    const registrationData = vehicleData?.registration || vehicleData || customer;

                    const hasSpecialNumber = registrationData?.hasSpecialNumber;
                    const expectedSpecialNumber = registrationData?.expectedSpecialNumber;
                    // Fix field mapping - database uses "type" not "registrationType"
                    const registrationType = registrationData?.type || registrationData?.registrationType;

                    return (
                        <div className="grid grid-cols-2 gap-y-2">
                            <div className="text-sm text-gray-700">Special Number</div>
                            <div className="text-sm text-right">
                                {hasSpecialNumber === "yes" ? "Yes" : "No"}
                            </div>

                            {expectedSpecialNumber && (
                                <>
                                    <div className="text-sm text-gray-700">Expected Number</div>
                                    <div className="text-sm text-right">{expectedSpecialNumber}</div>
                                </>
                            )}

                            <div className="text-sm text-gray-700">Registration Type</div>
                            <div className="text-sm text-right">
                                {registrationType || "permanent"}
                            </div>
                        </div>
                    );
                })()}
            </ModalSection>

            {/* Insurance Info */}
            <ModalDivider />
            <ModalSection title="Insurance Details">
                {(() => {
                    // Try to get insurance data from nested structure first, then flat structure
                    const vehicleData = fullVehicleDetails && fullVehicleDetails.length > 0 ? fullVehicleDetails[0] : null;
                    const insuranceData = vehicleData?.insurance || vehicleData || customer;

                    // Fix field mapping - database uses "type" not "insuranceType" and nominee data is nested
                    const insuranceType = insuranceData?.type || insuranceData?.insuranceType;
                    const nomineeName = insuranceData?.nominee?.name || insuranceData?.nomineeName;
                    const nomineeAge = insuranceData?.nominee?.age || insuranceData?.nomineeAge;
                    const nomineeRelationship = insuranceData?.nominee?.relationship || insuranceData?.nomineeRelationship;

                    // Clean logic: If "No Insurance" is selected, only show insurance type
                    if (insuranceType === "none") {
                        return (
                            <div className="grid grid-cols-2 gap-y-2">
                                <div className="text-sm text-gray-700">Insurance Type</div>
                                <div className="text-sm text-right">No Insurance</div>
                            </div>
                        );
                    }

                    // If insurance is selected, show all fields
                    if (insuranceType || nomineeName || nomineeAge || nomineeRelationship) {
                        return (
                            <div className="grid grid-cols-2 gap-y-2">
                                <div className="text-sm text-gray-700">Insurance Type</div>
                                <div className="text-sm text-right">
                                    {insuranceType === "bumperToBumper" ? "Bumper to Bumper" :
                                     insuranceType === "normal" ? "Normal Insurance" :
                                     insuranceType || "N/A"}
                                </div>

                                <div className="text-sm text-gray-700">Nominee Name</div>
                                <div className="text-sm text-right">
                                    {nomineeName || "N/A"}
                                </div>

                                <div className="text-sm text-gray-700">Nominee Age</div>
                                <div className="text-sm text-right">
                                    {nomineeAge || "N/A"}
                                </div>

                                <div className="text-sm text-gray-700">Nominee Relationship</div>
                                <div className="text-sm text-right">
                                    {nomineeRelationship || "N/A"}
                                </div>
                            </div>
                        );
                    } else {
                        return (
                            <div className="text-sm text-gray-500 text-center py-2">No insurance information available</div>
                        );
                    }
                })()}
            </ModalSection>

            {/* Documents Info */}
            <ModalDivider />
            <ModalSection title="Documents">
                {fullVehicleDetails && fullVehicleDetails.length > 0 && fullVehicleDetails[0]?.documents ? (
                    <div className="grid grid-cols-3 gap-y-2">
                        {fullVehicleDetails[0].documents.aadhar && (
                            <>
                                <div className="text-sm text-gray-700">Aadhar</div>
                                <div className="text-sm truncate max-w-[150px]" title={fullVehicleDetails[0].documents.aadhar.fileName || "Uploaded"}>
                                    {"Aadhar Document"}
                                </div>
                                <div className="flex justify-end space-x-2">
                                    <button
                                        onClick={() => viewFile('aadhar')}
                                        className="p-1 bg-blue-100 text-blue-600 rounded hover:bg-blue-200"
                                        title="View Document"
                                    >
                                        <Eye size={16} />
                                    </button>
                                    <button
                                        onClick={() => downloadFile('aadhar')}
                                        className="p-1 bg-green-100 text-green-600 rounded hover:bg-green-200"
                                        title="Download Document"
                                    >
                                        <Download size={16} />
                                    </button>
                                </div>
                            </>
                        )}
                        {fullVehicleDetails[0].documents.pan && (
                            <>
                                <div className="text-sm text-gray-700">PAN</div>
                                <div className="text-sm truncate max-w-[150px]" title={fullVehicleDetails[0].documents.pan.fileName || "Uploaded"}>
                                    {"PAN Document"}
                                </div>
                                <div className="flex justify-end space-x-2">
                                    <button
                                        onClick={() => viewFile('pan')}
                                        className="p-1 bg-blue-100 text-blue-600 rounded hover:bg-blue-200"
                                        title="View Document"
                                    >
                                        <Eye size={16} />
                                    </button>
                                    <button
                                        onClick={() => downloadFile('pan')}
                                        className="p-1 bg-green-100 text-green-600 rounded hover:bg-green-200"
                                        title="Download Document"
                                    >
                                        <Download size={16} />
                                    </button>
                                </div>
                            </>
                        )}
                        {fullVehicleDetails[0].documents.photo && (
                            <>
                                <div className="text-sm text-gray-700">Photo</div>
                                <div className="text-sm truncate max-w-[150px]" title={fullVehicleDetails[0].documents.photo.fileName || "Uploaded"}>
                                    {"Photo Document"}
                                </div>
                                <div className="flex justify-end space-x-2">
                                    <button
                                        onClick={() => viewFile('photo')}
                                        className="p-1 bg-blue-100 text-blue-600 rounded hover:bg-blue-200"
                                        title="View Document"
                                    >
                                        <Eye size={16} />
                                    </button>
                                    <button
                                        onClick={() => downloadFile('photo')}
                                        className="p-1 bg-green-100 text-green-600 rounded hover:bg-green-200"
                                        title="Download Document"
                                    >
                                        <Download size={16} />
                                    </button>
                                </div>
                            </>
                        )}
                        {fullVehicleDetails[0].documents.license && (
                            <>
                                <div className="text-sm text-gray-700">Driving License</div>
                                <div className="text-sm truncate max-w-[150px]" title={fullVehicleDetails[0].documents.license.fileName || "Uploaded"}>
                                    {"License Document"}
                                </div>
                                <div className="flex justify-end space-x-2">
                                    <button
                                        onClick={() => viewFile('license')}
                                        className="p-1 bg-blue-100 text-blue-600 rounded hover:bg-blue-200"
                                        title="View Document"
                                    >
                                        <Eye size={16} />
                                    </button>
                                    <button
                                        onClick={() => downloadFile('license')}
                                        className="p-1 bg-green-100 text-green-600 rounded hover:bg-green-200"
                                        title="Download Document"
                                    >
                                        <Download size={16} />
                                    </button>
                                </div>
                            </>
                        )}
                        {fullVehicleDetails[0].documents.other && (
                            <>
                                <div className="text-sm text-gray-700">Other Documents</div>
                                <div className="text-sm truncate max-w-[150px]" title={fullVehicleDetails[0].documents.other.fileName || "Uploaded"}>
                                    {"Other Document"}
                                </div>
                                <div className="flex justify-end space-x-2">
                                    <button
                                        onClick={() => viewFile('other')}
                                        className="p-1 bg-blue-100 text-blue-600 rounded hover:bg-blue-200"
                                        title="View Document"
                                    >
                                        <Eye size={16} />
                                    </button>
                                    <button
                                        onClick={() => downloadFile('other')}
                                        className="p-1 bg-green-100 text-green-600 rounded hover:bg-green-200"
                                        title="Download Document"
                                    >
                                        <Download size={16} />
                                    </button>
                                </div>
                            </>
                        )}
                    </div>
                ) : hasDocumentsInfo ? (
                    <div className="grid grid-cols-3 gap-y-2">
                        {customer.documents?.aadhar && (
                            <>
                                <div className="text-sm text-gray-700">Aadhar</div>
                                <div className="text-sm truncate max-w-[150px]" title={customer.documents.aadhar.fileName || "Uploaded"}>
                                    {"Aadhar Document"}
                                </div>
                                <div className="flex justify-end space-x-2">
                                    <button
                                        onClick={() => viewFile('aadhar')}
                                        className="p-1 bg-blue-100 text-blue-600 rounded hover:bg-blue-200"
                                        title="View Document"
                                    >
                                        <Eye size={16} />
                                    </button>
                                    <button
                                        onClick={() => downloadFile('aadhar')}
                                        className="p-1 bg-green-100 text-green-600 rounded hover:bg-green-200"
                                        title="Download Document"
                                    >
                                        <Download size={16} />
                                    </button>
                                </div>
                            </>
                        )}
                        {/* Similar blocks for other document types */}
                        {customer.documents?.pan && (
                            <>
                                <div className="text-sm text-gray-700">PAN</div>
                                <div className="text-sm truncate max-w-[150px]">{"PAN Document"}</div>
                                <div className="flex justify-end space-x-2">
                                    <button onClick={() => viewFile('pan')} className="p-1 bg-blue-100 text-blue-600 rounded hover:bg-blue-200">
                                        <Eye size={16} />
                                    </button>
                                    <button onClick={() => downloadFile('pan')} className="p-1 bg-green-100 text-green-600 rounded hover:bg-green-200">
                                        <Download size={16} />
                                    </button>
                                </div>
                            </>
                        )}
                        {customer.documents?.photo && (
                            <>
                                <div className="text-sm text-gray-700">Photo</div>
                                <div className="text-sm truncate max-w-[150px]">{"Photo Document"}</div>
                                <div className="flex justify-end space-x-2">
                                    <button onClick={() => viewFile('photo')} className="p-1 bg-blue-100 text-blue-600 rounded hover:bg-blue-200">
                                        <Eye size={16} />
                                    </button>
                                    <button onClick={() => downloadFile('photo')} className="p-1 bg-green-100 text-green-600 rounded hover:bg-green-200">
                                        <Download size={16} />
                                    </button>
                                </div>
                            </>
                        )}
                    </div>
                ) : (
                    <div className="text-sm text-gray-500 text-center py-2">No documents available</div>
                )}
            </ModalSection>
                </>
            )}

            {/* Image Viewer */}
            <ImageViewer
                imageUrl={imageViewer.imageUrl}
                isOpen={imageViewer.isOpen}
                onClose={() => setImageViewer({ isOpen: false, imageUrl: '', title: '' })}
                title={imageViewer.title}
            />
        </Modal>
    );
};

export default CustomerDetailed;
