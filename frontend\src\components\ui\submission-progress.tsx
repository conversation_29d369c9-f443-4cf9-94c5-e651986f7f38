import React from 'react';

interface SubmissionProgressProps {
  progress: number;
  status: string;
}

export const SubmissionProgress: React.FC<SubmissionProgressProps> = ({ progress, status }) => {
  // Ensure progress is between 0 and 100
  const normalizedProgress = Math.min(100, Math.max(0, progress));
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50">
      <div className="bg-[#0e0e0e] p-6 rounded-lg max-w-md w-full border border-[#333333] shadow-xl">
        <h2 className="text-xl text-[#e3e3e3] font-medium mb-4">Submitting Form</h2>
        
        {/* Progress bar */}
        <div className="w-full h-4 bg-[#333333] rounded-full overflow-hidden mb-2">
          <div 
            className="h-full bg-gradient-to-r from-[#640064] to-[#C524C5] transition-all duration-300 ease-out"
            style={{ width: `${normalizedProgress}%` }}
          />
        </div>
        
        {/* Progress percentage */}
        <div className="flex justify-between text-sm text-[#999999] mb-4">
          <span>{normalizedProgress}% Complete</span>
          <span>{status}</span>
        </div>
        
        {/* Status message */}
        <p className="text-[#e3e3e3] text-center">
          {status || 'Processing your submission...'}
        </p>
        
        {/* Animated loading indicator */}
        <div className="flex justify-center mt-6">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[#C524C5]"></div>
        </div>
      </div>
    </div>
  );
};
