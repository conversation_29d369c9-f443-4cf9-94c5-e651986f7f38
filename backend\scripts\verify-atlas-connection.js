import mongoose from 'mongoose';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';
import logger from '../utils/logger.js';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables from .env file
dotenv.config({ path: join(__dirname, '..', '.env') });

// Get MongoDB URI from environment variables
const MONGODB_URI = process.env.MONGODB_URI;

if (!MONGODB_URI) {
  logger.error('MongoDB URI is not defined in the .env file');
  process.exit(1);
}

// Hide password in logs
const sanitizedUri = MONGODB_URI.replace(/:[^:]*@/, ':****@');
logger.info(`Attempting to connect to MongoDB Atlas: ${sanitizedUri}`);

// Connect to MongoDB Atlas
mongoose.connect(MONGODB_URI, {
  serverSelectionTimeoutMS: 5000,
  socketTimeoutMS: 45000,
  family: 4
})
  .then(async () => {
    logger.info('✅ Successfully connected to MongoDB Atlas!');
    
    // Get all collections
    const collections = await mongoose.connection.db.listCollections().toArray();
    logger.info('\n📊 Available collections:');
    
    if (collections.length === 0) {
      logger.info('No collections found. The database is empty.');
    } else {
      for (const collection of collections) {
        const count = await mongoose.connection.db.collection(collection.name).countDocuments();
        logger.info(`- ${collection.name}: ${count} documents`);
      }
    }
    
    // Check for required collections
    const requiredCollections = ['customers', 'vehicles', 'admins'];
    const missingCollections = requiredCollections.filter(
      name => !collections.some(col => col.name === name)
    );
    
    if (missingCollections.length > 0) {
      logger.info('\n⚠️ Missing collections:');
      for (const missing of missingCollections) {
        logger.info(`- ${missing}`);
      }
      logger.info('\nThese collections will be created automatically when data is added.');
    } else {
      logger.info('\n✅ All required collections exist.');
    }
    
    // Check database indexes
    logger.info('\n📑 Checking indexes:');
    for (const collection of collections) {
      const indexes = await mongoose.connection.db.collection(collection.name).indexes();
      logger.info(`\nIndexes for ${collection.name}:`);
      indexes.forEach(index => {
        logger.info(`- ${index.name}: ${JSON.stringify(index.key)}`);
      });
    }
    
    logger.info('\n✅ MongoDB Atlas verification complete!');
    
    // Close the connection
    await mongoose.connection.close();
    logger.info('Connection closed.');
    process.exit(0);
  })
  .catch(err => {
    logger.error('❌ MongoDB Atlas connection error:', err.message);
    process.exit(1);
  });
