import React, { useEffect, useState } from "react";
import { Input } from "../ui/input";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../store/store";
import { updateFormField, validateSection, setFieldTouched } from "../../store/formSlice";
import { validationRules } from "../../utils/validation";

export const InsuranceDetails = () => {
  const dispatch = useDispatch();
  const { formData, errors, touchedFields } = useSelector((state: RootState) => state.form);

  // State for animation
  const [showNomineeFields, setShowNomineeFields] = useState(formData.insuranceType !== "none");

  // Initialize form data when component loads
  useEffect(() => {
    // Always clear errors on component mount
    dispatch({ type: 'form/clearSectionErrors', payload: 'insurance' });

    // Initialize insuranceType if it doesn't exist
    if (!formData.insuranceType) {
      dispatch(updateFormField({
        field: "insuranceType",
        value: "bumperToBumper"
      }));

      // Don't validate on initialization to prevent showing errors before user interaction
    } else if (formData.insuranceType !== "none") {
      // If insurance type is already set and not "none", validate the fields
      if (formData.nomineeName && formData.nomineeAge && formData.nomineeRelationship) {
        // All fields are filled, clear any errors
        dispatch({ type: 'form/clearSectionErrors', payload: 'insurance' });
      }
    }
  }, [dispatch, formData.insuranceType, formData.nomineeName, formData.nomineeAge, formData.nomineeRelationship]);

  // Update animation state when insurance type changes
  useEffect(() => {
    if (formData.insuranceType !== "none") {
      setShowNomineeFields(true);

      // Check if all fields are filled and valid, then clear errors
      if (formData.nomineeName && formData.nomineeAge && formData.nomineeRelationship) {
        // Validate nomineeAge
        if (/^\d+$/.test(formData.nomineeAge) &&
            Number(formData.nomineeAge) >= 18 &&
            Number(formData.nomineeAge) <= 100) {
          // All fields are valid, clear any errors
          dispatch({ type: 'form/clearSectionErrors', payload: 'insurance' });
        }
      }
    } else {
      // Small delay before hiding to allow animation to complete
      const timer = setTimeout(() => {
        setShowNomineeFields(false);
      }, 300);
      return () => clearTimeout(timer);
    }
  }, [formData.insuranceType, formData.nomineeName, formData.nomineeAge, formData.nomineeRelationship, dispatch]);

  // Check if insurance type is "none" (No Need)
  const isNoInsurance = formData.insuranceType === "none";

  // Function to check if a field is required
  const isFieldRequired = (fieldName: string) => {
    // If insurance type is "none", no other fields are required
    if (isNoInsurance && fieldName !== "insuranceType") {
      return false;
    }
    const rules = validationRules.insurance?.[fieldName as keyof typeof validationRules.insurance];
    return rules && rules.required;
  };

  // Required field marker component
  const RequiredMarker = () => (
    <span className="text-red-500 ml-1">*</span>
  );

  // Check if a field should show validation error
  const shouldShowError = (fieldName: string) => {
    return touchedFields && touchedFields[fieldName] && errors && errors[fieldName];
  };

  // Helper function to mark a field as touched
  const markFieldAsTouched = (name: string) => {
    dispatch(setFieldTouched(name));
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    // Special handling for nomineeAge to ensure it's a valid number
    if (name === "nomineeAge") {
      // Only allow numeric input
      if (value !== "" && !/^\d+$/.test(value)) {
        return; // Don't update if not a valid number
      }

      // Limit to reasonable values
      if (Number(value) > 100) {
        return; // Don't update if too large
      }
    }

    dispatch(updateFormField({
      field: name,
      value
    }));

    // If changing insuranceType to "none", set dependent fields to null (clean data)
    if (name === "insuranceType" && value === "none") {
      dispatch(updateFormField({ field: "nomineeName", value: null }));
      dispatch(updateFormField({ field: "nomineeAge", value: null }));
      dispatch(updateFormField({ field: "nomineeRelationship", value: null }));

      // Clear any existing errors for this section when "none" is selected
      dispatch({ type: 'form/clearSectionErrors', payload: 'insurance' });

      // Skip validation when "none" is selected - we don't need to validate
      return;
    }

    // If changing insuranceType from "none" to something else, initialize fields
    if (name === "insuranceType" && value !== "none" && formData.insuranceType === "none") {
      dispatch(updateFormField({ field: "nomineeName", value: "" }));
      dispatch(updateFormField({ field: "nomineeAge", value: "" }));
      dispatch(updateFormField({ field: "nomineeRelationship", value: "" }));
    }

    // Mark field as touched
    markFieldAsTouched(name);

    // Clear any existing errors for this field
    if (errors && errors[name]) {
      dispatch({
        type: 'form/clearFieldError',
        payload: name
      });
    }

    // Force clear errors for nomineeAge if it's a valid value
    if (name === "nomineeAge" && value !== "" && /^\d+$/.test(value) && Number(value) >= 18 && Number(value) <= 100) {
      dispatch({
        type: 'form/clearFieldError',
        payload: "nomineeAge"
      });
    }

    // Check if all required fields are filled when insurance type is not "none"
    if (formData.insuranceType !== "none" &&
        formData.nomineeName &&
        formData.nomineeAge &&
        formData.nomineeRelationship) {
      // All fields are filled, clear any errors
      dispatch({ type: 'form/clearSectionErrors', payload: 'insurance' });
    }

    // Only validate if the field has been touched
    if (touchedFields && touchedFields[name]) {
      setTimeout(() => {
        dispatch(validateSection("insurance"));
      }, 0);
    }
  };

  // Handle blur event to mark field as touched and validate
  const handleBlur = (e: React.FocusEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name } = e.target;
    markFieldAsTouched(name);

    // Validate the field after it's been touched
    setTimeout(() => {
      dispatch(validateSection("insurance"));
    }, 0);
  };

  return (
    <div className="space-y-4 sm:space-y-5 md:space-y-6">
      {/* Insurance Type Dropdown */}
      <div className="space-y-1 sm:space-y-1.5">
        <label className="text-[#e3e3e3] text-lg sm:text-xl md:text-2xl font-light">
          Insurance Type
          {isFieldRequired("insuranceType") && <RequiredMarker />}
        </label>
        <div className="relative mb-6">
          <select
            className={`h-[45px] sm:h-[50px] md:h-[60px] w-full px-3 sm:px-4 rounded-lg border bg-[#0e0e0e] text-[#e3e3e3] text-sm sm:text-base placeholder:text-[#818181] focus:border-[#640064] focus:ring-1 focus:ring-[#640064] transition-colors appearance-none cursor-pointer custom-select ${
              shouldShowError("insuranceType") ? "border-red-500" : "border-[#333333]"
            }`}
            name="insuranceType"
            value={formData.insuranceType || "bumperToBumper"}
            onChange={handleChange}
            onBlur={handleBlur}
          >
            <option value="bumperToBumper">Bumper to Bumper</option>
            <option value="normal">Normal Insurance</option>
            <option value="none">No Need</option>
          </select>
        </div>
        {shouldShowError("insuranceType") && (
          <p className="text-red-500 text-sm mt-1 absolute">{errors["insuranceType"]}</p>
        )}
      </div>

      {/* Nominee Fields with Animation */}
      <div
        className={`space-y-4 sm:space-y-5 md:space-y-6 overflow-hidden transition-all duration-300 ease-in-out ${
          formData.insuranceType !== "none"
            ? "max-h-[500px] opacity-100 transform translate-y-0"
            : "max-h-0 opacity-50 transform -translate-y-4"
        }`}
      >
        {showNomineeFields && (
          <>
            {/* First row: Nominee Name */}
            <div className="space-y-1 sm:space-y-1.5">
              <label className="text-[#e3e3e3] text-lg sm:text-xl md:text-2xl font-light">
                Nominee Name
                {isFieldRequired("nomineeName") && <RequiredMarker />}
              </label>
              <Input
                className={`h-[45px] sm:h-[50px] md:h-[60px] w-full px-3 sm:px-4 rounded-lg border bg-[#0e0e0e] text-[#e3e3e3] text-sm sm:text-base placeholder:text-[#818181] focus:border-[#640064] focus:ring-1 focus:ring-[#640064] transition-colors mb-6 ${
                  shouldShowError("nomineeName") ? "border-red-500" : "border-[#333333]"
                }`}
                placeholder="Enter your Name"
                name="nomineeName"
                value={formData.nomineeName || ""}
                onChange={handleChange}
                onBlur={handleBlur}
                disabled={isNoInsurance}
              />
              {shouldShowError("nomineeName") && (
                <p className="text-red-500 text-sm mt-1 absolute">{errors["nomineeName"]}</p>
              )}
            </div>

            {/* Second row: Nominee Age and Relationship */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
              {/* Nominee Age */}
              <div className="space-y-1 sm:space-y-1.5">
                <label className="text-[#e3e3e3] text-lg sm:text-xl md:text-2xl font-light">
                  Nominee Age
                  {isFieldRequired("nomineeAge") && <RequiredMarker />}
                </label>
                <Input
                  className={`h-[45px] sm:h-[50px] md:h-[60px] w-full px-3 sm:px-4 rounded-lg border bg-[#0e0e0e] text-[#e3e3e3] text-sm sm:text-base placeholder:text-[#818181] focus:border-[#640064] focus:ring-1 focus:ring-[#640064] transition-colors mb-6 ${
                    shouldShowError("nomineeAge") ? "border-red-500" : "border-[#333333]"
                  }`}
                  placeholder="Enter Your Age"
                  type="number"
                  name="nomineeAge"
                  value={formData.nomineeAge || ""}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  disabled={isNoInsurance}
                />
                {shouldShowError("nomineeAge") && (
                  <p className="text-red-500 text-sm mt-1 absolute">{errors["nomineeAge"]}</p>
                )}
              </div>

              {/* Relationship */}
              <div className="space-y-1 sm:space-y-1.5">
                <label className="text-[#e3e3e3] text-lg sm:text-xl md:text-2xl font-light">
                  Relationship
                  {isFieldRequired("nomineeRelationship") && <RequiredMarker />}
                </label>
                <div className="relative mb-6">
                  <select
                    className={`h-[45px] sm:h-[50px] md:h-[60px] w-full px-3 sm:px-4 rounded-lg border bg-[#0e0e0e] text-[#e3e3e3] text-sm sm:text-base placeholder:text-[#818181] focus:border-[#640064] focus:ring-1 focus:ring-[#640064] transition-colors appearance-none cursor-pointer custom-select ${
                      shouldShowError("nomineeRelationship") ? "border-red-500" : "border-[#333333]"
                    }`}
                    name="nomineeRelationship"
                    value={formData.nomineeRelationship || ""}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    disabled={isNoInsurance}
                  >
                    <option value="" disabled className="text-[#818181]">
                      Select Your Relationship
                    </option>
                    <option value="spouse">Spouse</option>
                    <option value="parent">Parent</option>
                    <option value="child">Child</option>
                    <option value="sibling">Sibling</option>
                    <option value="other">Other</option>
                  </select>
                </div>
                {shouldShowError("nomineeRelationship") && (
                  <p className="text-red-500 text-sm mt-1 absolute">{errors["nomineeRelationship"]}</p>
                )}
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};
