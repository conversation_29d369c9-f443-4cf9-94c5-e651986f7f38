import mongoose from 'mongoose';
import Customer from '../models/Customer.js';
import Vehicle from '../models/Vehicle.js';
import logger from '../utils/logger.js';
import { isMongoConnected } from '../config/db.js';

/**
 * Search for customers and vehicles
 */
export const search = async (req, res) => {
  try {
    if (!isMongoConnected()) {
      return res.status(200).json({
        success: true,
        data: {
          customers: [],
          vehicles: []
        },
        message: 'MongoDB not connected, using default values'
      });
    }

    const { query, type } = req.query;

    if (!query) {
      return res.status(400).json({
        success: false,
        message: 'Search query is required'
      });
    }

    let customers = [];
    let vehicles = [];

    // Search for customers if type is 'all' or 'customers'
    if (!type || type === 'all' || type === 'customers') {
      customers = await Customer.find({
        $or: [
          { name: { $regex: query, $options: 'i' } },
          { email: { $regex: query, $options: 'i' } },
          { phone: { $regex: query, $options: 'i' } },
          { 'address.city': { $regex: query, $options: 'i' } },
          { 'address.state': { $regex: query, $options: 'i' } }
        ]
      }).limit(10);
    }

    // Search for vehicles if type is 'all' or 'vehicles'
    if (!type || type === 'all' || type === 'vehicles') {
      vehicles = await Vehicle.find({
        $or: [
          { 'customer.name': { $regex: query, $options: 'i' } },
          { 'customer.phone': { $regex: query, $options: 'i' } },
          { 'customer.email': { $regex: query, $options: 'i' } },
          { 'vehicleDetails.model': { $regex: query, $options: 'i' } },
          { 'vehicleDetails.colour': { $regex: query, $options: 'i' } }
        ]
      }).limit(10);
    }

    res.status(200).json({
      success: true,
      data: {
        customers,
        vehicles
      }
    });

    logger.info('Search completed', {
      query,
      type,
      customerResults: customers.length,
      vehicleResults: vehicles.length,
      requestId: req.id
    });

  } catch (error) {
    logger.logError(error, {
      operation: 'search',
      query,
      type,
      requestId: req.id
    });

    res.status(500).json({
      success: false,
      message: 'Error searching',
      error: error.message,
      requestId: req.id
    });
  }
};

/**
 * Advanced search with filters
 */
export const advancedSearch = async (req, res) => {
  try {
    if (!isMongoConnected()) {
      return res.status(200).json({
        success: true,
        data: {
          results: [],
          totalCount: 0
        },
        message: 'MongoDB not connected, using default values'
      });
    }

    // Extract search parameters from request body
    const {
      searchType = 'all',
      query = '',
      filters = {},
      page = 1,
      limit = 10,
      sortField = 'createdAt',
      sortOrder = 'desc'
    } = req.body;

    // Calculate pagination
    const skip = (page - 1) * limit;
    const sortDirection = sortOrder === 'asc' ? 1 : -1;

    let results = [];
    let totalCount = 0;

    // Build the search query based on searchType
    if (searchType === 'customers' || searchType === 'all') {
      // Build customer search query
      const customerQuery = {};

      // Add text search if query is provided
      if (query) {
        customerQuery.$or = [
          { name: { $regex: query, $options: 'i' } },
          { email: { $regex: query, $options: 'i' } },
          { phone: { $regex: query, $options: 'i' } }
        ];
      }

      // Add filters
      if (filters.city) {
        customerQuery['address.city'] = { $regex: filters.city, $options: 'i' };
      }

      if (filters.state) {
        customerQuery['address.state'] = { $regex: filters.state, $options: 'i' };
      }

      // Execute customer search if searchType is 'customers'
      if (searchType === 'customers') {
        results = await Customer.find(customerQuery)
          .sort({ [sortField]: sortDirection })
          .skip(skip)
          .limit(limit);

        totalCount = await Customer.countDocuments(customerQuery);
      }
    }

    if (searchType === 'vehicles' || searchType === 'all') {
      // Build vehicle search query
      const vehicleQuery = {};

      // Add text search if query is provided
      if (query) {
        vehicleQuery.$or = [
          { 'customer.name': { $regex: query, $options: 'i' } },
          { 'customer.phone': { $regex: query, $options: 'i' } },
          { 'vehicleDetails.model': { $regex: query, $options: 'i' } },
          { 'vehicleDetails.colour': { $regex: query, $options: 'i' } }
        ];
      }

      // Add filters
      if (filters.status) {
        vehicleQuery.status = filters.status;
      }

      if (filters.model) {
        vehicleQuery['vehicleDetails.model'] = { $regex: filters.model, $options: 'i' };
      }

      if (filters.colour) {
        vehicleQuery['vehicleDetails.colour'] = { $regex: filters.colour, $options: 'i' };
      }

      // Execute vehicle search if searchType is 'vehicles'
      if (searchType === 'vehicles') {
        results = await Vehicle.find(vehicleQuery)
          .sort({ [sortField]: sortDirection })
          .skip(skip)
          .limit(limit);

        totalCount = await Vehicle.countDocuments(vehicleQuery);
      }
    }

    // If searchType is 'all', we need to combine results from both collections
    if (searchType === 'all') {
      // This is a simplified approach - in a real application, you might want to
      // implement a more sophisticated way to combine and sort results from different collections
      const customerResults = await Customer.find({})
        .sort({ [sortField]: sortDirection })
        .limit(limit);

      const vehicleResults = await Vehicle.find({})
        .sort({ [sortField]: sortDirection })
        .limit(limit);

      // Combine results and sort them
      results = [...customerResults, ...vehicleResults]
        .sort((a, b) => {
          if (sortDirection === 1) {
            return a[sortField] > b[sortField] ? 1 : -1;
          } else {
            return a[sortField] < b[sortField] ? 1 : -1;
          }
        })
        .slice(skip, skip + limit);

      totalCount = await Customer.countDocuments({}) + await Vehicle.countDocuments({});
    }

    res.status(200).json({
      success: true,
      data: {
        results,
        totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit)
      }
    });
  } catch (error) {
    logger.logError(error, {
      operation: 'advancedSearch',
      searchType: req.body.searchType,
      query: req.body.query,
      requestId: req.id
    });

    res.status(500).json({
      success: false,
      message: 'Error performing advanced search',
      error: error.message,
      requestId: req.id
    });
  }
};
