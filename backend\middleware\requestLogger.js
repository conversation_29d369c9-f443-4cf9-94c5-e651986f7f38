import { v4 as uuidv4 } from 'uuid';
import logger from '../utils/logger.js';

/**
 * Middleware to add request ID and log requests/responses
 */
export const requestLogger = (req, res, next) => {
  // Generate unique request ID
  req.id = uuidv4();
  
  // Add request ID to response headers for debugging
  res.setHeader('X-Request-ID', req.id);
  
  // Only log non-GET requests to reduce verbosity
  if (req.method !== 'GET') {
    logger.logRequest(req);
  }
  
  // Capture start time for response time calculation
  const startTime = Date.now();
  
  // Override res.end to log response
  const originalEnd = res.end;
  res.end = function(...args) {
    // Calculate response time
    res.responseTime = Date.now() - startTime;

    // Only log non-GET responses to reduce verbosity
    if (req.method !== 'GET') {
      logger.logResponse(req, res);
    }
    
    // Call original end method
    originalEnd.apply(this, args);
  };
  
  next();
};

/**
 * Middleware to log errors
 */
export const errorLogger = (err, req, res, next) => {
  logger.logError(err, {
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    requestId: req.id,
    body: req.body,
    params: req.params,
    query: req.query
  });
  
  next(err);
};

export default { requestLogger, errorLogger };
