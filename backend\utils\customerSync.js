import Customer from '../models/Customer.js';
import Vehicle from '../models/Vehicle.js';
import { isMongoConnected } from '../config/db.js';
import logger from './logger.js';

/**
 * Sync customer data from vehicle to customer model
 * This is used when a vehicle is created or updated with customer information
 * Now includes strict duplicate prevention
 * @param {Object} vehicleData - The vehicle data containing customer information
 * @param {Object} options - Options for sync behavior
 * @returns {Promise<Object>} The customer object
 */
export const syncCustomerFromVehicle = async (vehicleData, options = {}) => {
  try {
    if (!isMongoConnected() || !vehicleData || !vehicleData.customer) {
      return null;
    }

    const customerData = vehicleData.customer;
    const { allowDuplicates = false } = options;

    // Check if we have enough data to identify a customer
    if (!customerData.phone && !customerData.email) {
      return null;
    }

    // Try to find existing customer by phone or email
    let customer = null;

    if (customerData.phone || customerData.email) {
      customer = await Customer.findOne({
        $or: [
          ...(customerData.phone ? [{ phone: customerData.phone }] : []),
          ...(customerData.email ? [{ email: customerData.email.toLowerCase() }] : [])
        ]
      });
    }

    if (customer) {
      // Check if this customer is already associated with the current vehicle
      const isCurrentVehicleCustomer = vehicleData.customerId &&
        customer._id.toString() === vehicleData.customerId.toString();

      // Check if this vehicle is already in the customer's vehicles list
      const isVehicleInCustomerList = customer.vehicles &&
        customer.vehicles.some(vId => vId.toString() === vehicleData._id.toString());

      if (!allowDuplicates && !isCurrentVehicleCustomer && !isVehicleInCustomerList) {
        // Only throw error if this is truly a different customer/vehicle combination
        const error = new Error('Customer with this phone number or email already exists');
        error.code = 'DUPLICATE_CUSTOMER';
        error.customer = {
          id: customer._id,
          name: customer.name,
          phone: customer.phone,
          email: customer.email
        };
        throw error;
      }

      // Legacy mode: Update existing customer (only if explicitly allowed)
      logger.info('Updating existing customer:', customer._id.toString());

      customer.name = customerData.name || customer.name;
      customer.phone = customerData.phone || customer.phone;
      customer.email = customerData.email || customer.email;
      customer.altPhone = customerData.altPhone || customer.altPhone;

      // Update address
      if (customerData.address) {
        customer.address = {
          houseNo: customerData.address.houseNo || customer.address?.houseNo || '',
          street: customerData.address.street || customer.address?.street || '',
          city: customerData.address.city || customer.address?.city || '',
          state: customerData.address.state || customer.address?.state || '',
          pincode: customerData.address.pincode || customer.address?.pincode || ''
        };
      }

      // Add vehicle to customer's vehicles array if not already present
      if (vehicleData._id && !customer.vehicles.includes(vehicleData._id)) {
        customer.vehicles.push(vehicleData._id);
      }
    } else {
      // Create new customer
      logger.info('Creating new customer record for vehicle:', vehicleData._id.toString());

      customer = new Customer({
        name: customerData.name || 'Unknown',
        phone: customerData.phone || `unknown_${Date.now()}`,
        email: customerData.email || `unknown_${Date.now()}@example.com`,
        altPhone: customerData.altPhone || '',
        address: {
          houseNo: customerData.address?.houseNo || '',
          street: customerData.address?.street || '',
          city: customerData.address?.city || '',
          state: customerData.address?.state || '',
          pincode: customerData.address?.pincode || ''
        },
        vehicles: vehicleData._id ? [vehicleData._id] : []
      });

      logger.info('Created new customer:', {
        name: customer.name,
        phone: customer.phone,
        email: customer.email
      });
    }

    // Save the customer
    await customer.save();

    // Set the customerId field in the vehicle document for new customers too
    if (vehicleData._id) {
      try {
        await Vehicle.findByIdAndUpdate(
          vehicleData._id,
          { customerId: customer._id },
          { new: true }
        );
        logger.info(`Updated vehicle ${vehicleData._id.toString()} with customerId ${customer._id.toString()}`);
      } catch (updateError) {
        logger.error('Error updating vehicle with customerId:', updateError);
        // Continue anyway - this is not critical
      }
    }

    return customer;
  } catch (error) {
    logger.error('Error syncing customer from vehicle:', error);
    // Don't throw the error, just log it and return null
    // This prevents customer sync failures from breaking vehicle creation
    return null;
  }
};

/**
 * Update vehicle with customer information
 * @param {string} vehicleId - The vehicle ID
 * @param {Object} customerData - The customer data
 * @returns {Promise<Object>} The updated vehicle
 */
export const updateVehicleWithCustomer = async (vehicleId, customerData) => {
  try {
    if (!isMongoConnected() || !vehicleId || !customerData) {
      return null;
    }

    // Find the vehicle
    const vehicle = await Vehicle.findById(vehicleId);

    if (!vehicle) {
      return null;
    }

    // Update vehicle's customer information
    vehicle.customer = {
      name: customerData.name,
      phone: customerData.phone,
      email: customerData.email,
      altPhone: customerData.altPhone,
      houseNo: customerData.address?.houseNo,
      street: customerData.address?.street,
      city: customerData.address?.city,
      state: customerData.address?.state,
      pincode: customerData.address?.pincode
    };

    // Save the vehicle
    await vehicle.save();
    return vehicle;
  } catch (error) {
    logger.error('Error updating vehicle with customer:', error);
    return null;
  }
};

export default {
  syncCustomerFromVehicle,
  updateVehicleWithCustomer
};
