import React from "react";
import { FaChevronRight } from "react-icons/fa";

interface ProgressIndicatorProps {
  currentStep: number;
  totalSteps: number;
}

export const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({ currentStep, totalSteps }) => {
  // For mobile view, only show current step and adjacent steps
  const getMobileVisibility = (index: number) => {
    // Always show first and last steps
    if (index === 0 || index === totalSteps - 1) return true;
    
    // Show current step and adjacent steps
    if (Math.abs(index + 1 - currentStep) <= 1) return true;
    
    return false;
  };

  return (
    <div className="flex flex-col items-center w-full mb-1">
      {/* Desktop view - show all steps */}
      <div className="hidden sm:flex items-center justify-center gap-1 w-full">
        {Array.from({ length: totalSteps }, (_, index) => (
          <React.Fragment key={`desktop-${index}`}>
            <div
              className={`flex items-center justify-center w-8 h-8 md:w-10 md:h-10 rounded-full text-sm md:text-base font-medium font-sans 
                transition-all duration-300 ease-in-out transform
                ${index + 1 === currentStep 
                  ? "bg-[#640064] text-white scale-110 shadow-lg" 
                  : index + 1 < currentStep
                    ? "bg-[#640064] text-white opacity-70"
                    : "bg-[#333333] text-[#818181]"}`}
            >
              {index + 1}
            </div>
            {index < totalSteps - 1 && (
              <FaChevronRight
                className={`w-4 h-4 md:w-5 md:h-5 transition-all duration-300 ease-in-out
                  ${index + 1 < currentStep 
                    ? "text-[#640064] translate-x-0 opacity-100" 
                    : index + 1 === currentStep
                      ? "text-[#640064] opacity-50"
                      : "text-[#333333]"}`}
              />
            )}
          </React.Fragment>
        ))}
      </div>

      {/* Mobile view - simplified progress indicator */}
      <div className="flex sm:hidden items-center justify-center gap-1 w-full">
        {Array.from({ length: totalSteps }, (_, index) => {
          const isVisible = getMobileVisibility(index);
          
          return (
            <React.Fragment key={`mobile-${index}`}>
              {isVisible ? (
                <>
                  <div
                    className={`flex items-center justify-center w-7 h-7 rounded-full text-xs font-medium font-sans 
                      transition-all duration-300 ease-in-out transform
                      ${index + 1 === currentStep 
                        ? "bg-[#640064] text-white scale-110 shadow-lg" 
                        : index + 1 < currentStep
                          ? "bg-[#640064] text-white opacity-70"
                          : "bg-[#333333] text-[#818181]"}`}
                  >
                    {index + 1}
                  </div>
                  {index < totalSteps - 1 && getMobileVisibility(index + 1) && (
                    <FaChevronRight
                      className={`w-3 h-3 transition-all duration-300 ease-in-out
                        ${index + 1 < currentStep 
                          ? "text-[#640064] translate-x-0 opacity-100" 
                          : index + 1 === currentStep
                            ? "text-[#640064] opacity-50"
                            : "text-[#333333]"}`}
                    />
                  )}
                </>
              ) : index > 0 && index < totalSteps - 1 && getMobileVisibility(index - 1) && getMobileVisibility(index + 1) ? (
                <div className="flex items-center justify-center px-1">
                  <span className="text-[#818181] text-xs">...</span>
                </div>
              ) : null}
            </React.Fragment>
          );
        })}
      </div>
    </div>
  );
};
