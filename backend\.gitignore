# Dependencies
node_modules/
npm-debug.log
yarn-error.log
yarn-debug.log
package-lock.json


# Note: .env.production is NOT ignored for pre-production deployment

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Directory for uploaded files
uploads/*
!uploads/.gitkeep

# Coverage directory used by tools like istanbul
coverage/

# Temporary folders
tmp/
temp/

# IDE specific files
.idea/
.vscode/
*.swp
*.swo

# OS specific files
.DS_Store
Thumbs.db
