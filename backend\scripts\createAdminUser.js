import mongoose from 'mongoose';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import Admin from '../models/Admin.js';
import logger from '../utils/logger.js';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
dotenv.config({ path: join(__dirname, '..', '.env') });

// Connect to MongoDB
const connectDB = async () => {
  try {
    const conn = await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    logger.info(`MongoDB Connected: ${conn.connection.host}`);
    return true;
  } catch (error) {
    logger.error(`Error connecting to MongoDB: ${error.message}`);
    return false;
  }
};

// Create admin user
const createAdminUser = async () => {
  try {
    // Connect to database
    const connected = await connectDB();
    if (!connected) {
      logger.error('Failed to connect to database. Exiting...');
      process.exit(1);
    }

    // Check if admin already exists
    const existingAdmin = await Admin.findOne({ email: '<EMAIL>' });
    if (existingAdmin) {
      logger.info('Admin user already exists. Updating password...');
      
      // Update password
      existingAdmin.password = 'Admin123';
      await existingAdmin.save();
      
      logger.info('Admin password updated successfully.');
    } else {
      // Create new admin user
      const admin = await Admin.create({
        name: 'Admin User',
        email: '<EMAIL>',
        password: 'Admin123',
        role: 'superadmin'
      });
      
      logger.info('Admin user created successfully:');
      logger.info({
        id: admin._id,
        name: admin.name,
        email: admin.email,
        role: admin.role
      });
    }

    // Disconnect from database
    await mongoose.disconnect();
    logger.info('Disconnected from database.');
    
    process.exit(0);
  } catch (error) {
    logger.error('Error creating admin user:', error);
    process.exit(1);
  }
};

// Run the function
createAdminUser();
