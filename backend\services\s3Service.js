/**
 * S3 Service for File Operations
 * Handles all S3 upload, download, and management operations
 */

import { 
  PutObjectCommand, 
  GetObjectCommand, 
  DeleteObjectCommand,
  HeadObjectCommand 
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { getS3Client, s3Config, generateS3Key } from '../config/s3Config.js';
import logger from '../utils/logger.js';

class S3Service {
  constructor() {
    this.s3Client = null;
    this.bucketName = s3Config.bucketName;
  }

  // Initialize S3 client
  async initialize() {
    try {
      this.s3Client = getS3Client();
      return true;
    } catch (error) {
      logger.error('Failed to initialize S3 service:', error);
      return false;
    }
  }

  // Upload file to S3
  async uploadFile(fileBuffer, customerPhone, documentType, originalFilename, mimeType) {
    try {
      if (!this.s3Client) {
        await this.initialize();
      }

      // Generate S3 key (file path in bucket)
      const s3Key = generateS3Key(customerPhone, documentType, originalFilename);

      // Prepare upload parameters
      const uploadParams = {
        Bucket: this.bucketName,
        Key: s3Key,
        Body: fileBuffer,
        ContentType: mimeType,
        CacheControl: s3Config.object.cacheControl,
        ServerSideEncryption: s3Config.object.serverSideEncryption,
        Metadata: {
          'customer-phone': customerPhone,
          'document-type': documentType,
          'original-filename': originalFilename,
          'upload-timestamp': new Date().toISOString()
        }
      };

      // Upload to S3
      const command = new PutObjectCommand(uploadParams);
      const result = await this.s3Client.send(command);

      logger.info(`File uploaded to S3: ${s3Key}`);

      return {
        success: true,
        s3Key: s3Key,
        etag: result.ETag,
        location: `https://${this.bucketName}.s3.${s3Config.region}.amazonaws.com/${s3Key}`,
        size: fileBuffer.length
      };

    } catch (error) {
      logger.error('S3 upload error:', error);
      throw new Error(`Failed to upload file to S3: ${error.message}`);
    }
  }

  // Get presigned URL for file download
  async getPresignedUrl(s3Key, expiresIn = 3600) {
    try {
      if (!this.s3Client) {
        await this.initialize();
      }

      const command = new GetObjectCommand({
        Bucket: this.bucketName,
        Key: s3Key
      });

      const presignedUrl = await getSignedUrl(this.s3Client, command, { 
        expiresIn: expiresIn // URL expires in 1 hour by default
      });

      return presignedUrl;

    } catch (error) {
      logger.error('Error generating presigned URL:', error);
      throw new Error(`Failed to generate presigned URL: ${error.message}`);
    }
  }

  // Check if file exists in S3
  async fileExists(s3Key) {
    try {
      if (!this.s3Client) {
        await this.initialize();
      }

      const command = new HeadObjectCommand({
        Bucket: this.bucketName,
        Key: s3Key
      });

      await this.s3Client.send(command);
      return true;

    } catch (error) {
      if (error.name === 'NotFound') {
        return false;
      }
      logger.error('Error checking file existence:', error);
      throw error;
    }
  }

  // Delete file from S3
  async deleteFile(s3Key) {
    try {
      if (!this.s3Client) {
        await this.initialize();
      }

      const command = new DeleteObjectCommand({
        Bucket: this.bucketName,
        Key: s3Key
      });

      await this.s3Client.send(command);
      logger.info(`File deleted from S3: ${s3Key}`);

      return true;

    } catch (error) {
      logger.error('Error deleting file from S3:', error);
      throw new Error(`Failed to delete file from S3: ${error.message}`);
    }
  }

  // Get file metadata
  async getFileMetadata(s3Key) {
    try {
      if (!this.s3Client) {
        await this.initialize();
      }

      const command = new HeadObjectCommand({
        Bucket: this.bucketName,
        Key: s3Key
      });

      const result = await this.s3Client.send(command);

      return {
        contentType: result.ContentType,
        contentLength: result.ContentLength,
        lastModified: result.LastModified,
        etag: result.ETag,
        metadata: result.Metadata
      };

    } catch (error) {
      logger.error('Error getting file metadata:', error);
      throw new Error(`Failed to get file metadata: ${error.message}`);
    }
  }

  // Get file stream from S3
  async getFileStream(s3Key) {
    try {
      if (!this.s3Client) {
        await this.initialize();
      }

      const command = new GetObjectCommand({
        Bucket: this.bucketName,
        Key: s3Key
      });

      const result = await this.s3Client.send(command);

      if (!result.Body) {
        throw new Error('No file body returned from S3');
      }

      return result.Body;

    } catch (error) {
      logger.error('Error getting file stream from S3:', error);
      throw new Error(`Failed to get file stream: ${error.message}`);
    }
  }

  // Validate file before upload
  validateFile(file, mimeType) {
    const errors = [];

    // Explicitly check for video files first
    const videoMimeTypes = [
      'video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/flv',
      'video/webm', 'video/mkv', 'video/3gp', 'video/m4v'
    ];

    if (videoMimeTypes.includes(mimeType)) {
      errors.push('Video files are not allowed. Please upload documents only (PDF, JPG, PNG, DOC, DOCX).');
      return {
        isValid: false,
        errors: errors
      };
    }

    // Check file size
    if (file.length > s3Config.upload.maxFileSize) {
      errors.push(`File size exceeds maximum limit of ${s3Config.upload.maxFileSize / (1024 * 1024)}MB`);
    }

    // Check MIME type
    if (!s3Config.upload.allowedMimeTypes.includes(mimeType)) {
      errors.push(`File type "${mimeType}" is not allowed. Only documents are accepted: PDF, JPG, PNG, DOC, DOCX.`);
    }

    return {
      isValid: errors.length === 0,
      errors: errors
    };
  }
}

// Export singleton instance
const s3Service = new S3Service();
export default s3Service;
