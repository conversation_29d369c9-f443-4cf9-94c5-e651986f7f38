2025-06-14 00:00:32 [HTTP]: Request received
Metadata: {
  "method": "POST",
  "url": "/api/vehicle/check-duplicate",
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "requestId": "8954fc25-4f4f-4cc7-ae92-401dcd21eb36"
}
2025-06-14 00:00:33 [HTTP]: Response sent
Metadata: {
  "method": "POST",
  "url": "/api/vehicle/check-duplicate",
  "statusCode": 200,
  "responseTime": 210,
  "requestId": "8954fc25-4f4f-4cc7-ae92-401dcd21eb36"
}
2025-06-14 00:00:33 [HTTP]: [0mPOST /api/vehicle/check-duplicate [32m200[0m 207.809 ms - 236[0m
