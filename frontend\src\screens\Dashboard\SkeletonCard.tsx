import React from 'react';

/**
 * Skeleton Card Component
 * Displays a loading placeholder that mimics the structure of a CustomerCard
 */
const SkeletonCard: React.FC = () => {
  return (
    <div className="bg-white shadow-sm border border-gray-100 rounded-xl p-4 flex flex-col justify-between min-h-[110px] animate-pulse">
      {/* Header with name and date */}
      <div className="flex justify-between items-start mb-1.5">
        <div className="h-5 bg-gray-200 rounded w-1/2"></div>
        <div className="h-3 bg-gray-200 rounded w-1/4"></div>
      </div>

      {/* Model and color */}
      <div className="mb-1.5">
        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
      </div>

      {/* Phone */}
      <div className="mb-1">
        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
      </div>
    </div>
  );
};

/**
 * Skeleton Grid Component
 * Displays a grid of skeleton cards
 */
export const SkeletonGrid: React.FC = () => {
  // Create an array of 16 items (4x4 grid)
  const skeletonCards = Array(16).fill(null);

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-5 px-4 sm:px-6 pb-24 mt-4">
      {skeletonCards.map((_, index) => (
        <SkeletonCard key={index} />
      ))}
    </div>
  );
};

export default SkeletonCard;
