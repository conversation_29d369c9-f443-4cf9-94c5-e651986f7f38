import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import cors from 'cors';
import xss from 'xss-clean';
import hpp from 'hpp';

const configureSecurityMiddleware = (app) => {
  app.use(helmet());

  const getCorsOrigins = () => {
    const corsOrigins = process.env.CORS_ORIGINS;
    if (!corsOrigins) {
      return process.env.FRONTEND_URL ? [process.env.FRONTEND_URL] : true;
    }
    return corsOrigins.split(',').map(origin => origin.trim()).filter(Boolean);
  };

  const corsOptions = {
    origin: getCorsOrigins(),
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    credentials: true,
    maxAge: 86400
  };
  app.use(cors(corsOptions));

  const limiter = rateLimit({
    windowMs: 15 * 60 * 1000,
    max: 100,
    standardHeaders: true,
    legacyHeaders: false,
    message: 'Too many requests from this IP, please try again after 15 minutes',
    skip: () => {
      return process.env.NODE_ENV === 'development';
    },
    keyGenerator: (req) => {
      return req.ip || 'unknown';
    }
  });
  app.use('/api/', limiter);

  app.use(xss());
  app.use(hpp({
    whitelist: ['sort', 'fields', 'page', 'limit', 'status', 'model', 'color']
  }));

  return app;
};

export default configureSecurityMiddleware;
