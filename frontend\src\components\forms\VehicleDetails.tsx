import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../store/store";
import { updateFormField, validateSection, setFieldTouched } from "../../store/formSlice";
import { validationRules } from "../../utils/validation";

export const VehicleDetails = () => {
  const dispatch = useDispatch();
  const { formData, errors, touchedFields } = useSelector((state: RootState) => state.form);

  // Initialize the form only once when it loads
  useEffect(() => {
    // Initialize vehicleDetails if it doesn't exist or is incomplete
    const initializeVehicleDetails = () => {
      // Make sure vehicleDetails exists and has all required fields
      const currentValues = formData.vehicleDetails || {};
      const hasAllRequiredFields =
        currentValues &&
        typeof currentValues === 'object' &&
        'model' in currentValues &&
        'colour' in currentValues &&
        'financeMode' in currentValues;

      if (!hasAllRequiredFields) {
        // Initializing vehicleDetails with default values
        dispatch(updateFormField({
          field: "vehicleDetails",
          value: {
            model: currentValues.model || "",
            colour: currentValues.colour || "",
            financeMode: currentValues.financeMode || ""
          }
        }));

        // Validate the section after initialization
        setTimeout(() => {
          dispatch(validateSection("vehicleDetails"));
        }, 0);
      }
    };

    // Initialize the vehicle details only once
    initializeVehicleDetails();
  }, [dispatch]);

  // Function to check if a field is required
  const isFieldRequired = (fieldName: string) => {
    const rules = validationRules.vehicleDetails[fieldName as keyof typeof validationRules.vehicleDetails];
    return rules && rules.required;
  };

  // Required field marker component
  const RequiredMarker = () => (
    <span className="text-red-500 ml-1">*</span>
  );

  // Helper function to mark a field as touched
  const markFieldAsTouched = (name: string) => {
    dispatch(setFieldTouched(`vehicleDetails.${name}`));
  };

  // Check if a field should show validation error
  const shouldShowError = (fieldName: string) => {
    return touchedFields && touchedFields[`vehicleDetails.${fieldName}`] &&
           errors && errors[`vehicleDetails.${fieldName}`];
  };

  // Handle changes for both select and input elements
  const handleChange = (e: React.ChangeEvent<HTMLSelectElement | HTMLInputElement>) => {
    const { name, value } = e.target;

    try {
      // Make sure vehicleDetails exists with all required fields
      const currentValues = formData.vehicleDetails || {};

      // Create a new object with all required fields and the updated value
      const updatedValues = {
        model: currentValues.model || "",
        colour: currentValues.colour || "",
        financeMode: currentValues.financeMode || "",
        [name]: value // Update the specific field
      };

      // Update the entire vehicleDetails object to ensure it's properly structured
      dispatch(updateFormField({
        field: "vehicleDetails",
        value: updatedValues
      }));

      // Mark field as touched
      markFieldAsTouched(name);

      // Clear any existing errors for this field
      if (errors && errors[`vehicleDetails.${name}`]) {
        dispatch({
          type: 'form/clearFieldError',
          payload: `vehicleDetails.${name}`
        });
      }

      // Validate the section after the update
      setTimeout(() => {
        dispatch(validateSection("vehicleDetails"));
      }, 0);
    } catch (error) {
      // Error updating vehicle details - handled by validation
    }
  };

  // Handle blur event to mark field as touched
  const handleBlur = (e: React.FocusEvent<HTMLSelectElement | HTMLInputElement>) => {
    const { name } = e.target;
    markFieldAsTouched(name);
  };

  // Component state ready for rendering

  return (
    <div className="space-y-6 sm:space-y-7 md:space-y-8">
        {/* Model */}
        <div className="space-y-2 sm:space-y-3">
          <label className="text-[#e3e3e3] text-lg sm:text-xl md:text-2xl font-light">
            Model
            {isFieldRequired("model") && <RequiredMarker />}
          </label>
          <select
            className={`w-full h-[45px] sm:h-[50px] md:h-[60px] bg-transparent border rounded-lg px-3 sm:px-4 text-[#e3e3e3] focus:outline-none appearance-none custom-select ${
              shouldShowError("model") ? "border-red-500" : "border-[#333333] focus:border-[#640064]"
            }`}
            name="model"
            value={formData.vehicleDetails?.model || ""}
            onChange={handleChange}
            onBlur={handleBlur}
          >
            <option value="" disabled>Select your Model</option>
            <option value="model1">Model 1</option>
            <option value="model2">Model 2</option>
            <option value="model3">Model 3</option>
          </select>
        </div>

        {/* Colour & Finance Mode */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 sm:gap-8">
          {/* Colour */}
          <div className="space-y-2 sm:space-y-3">
            <label className="text-[#e3e3e3] text-lg sm:text-xl md:text-2xl font-light">
              Colour
              {isFieldRequired("colour") && <RequiredMarker />}
            </label>
            <select
              className={`w-full h-[45px] sm:h-[50px] md:h-[60px] bg-transparent border rounded-lg px-3 sm:px-4 text-[#e3e3e3] focus:outline-none appearance-none custom-select ${
                shouldShowError("colour") ? "border-red-500" : "border-[#333333] focus:border-[#640064]"
              }`}
              name="colour"
              value={formData.vehicleDetails?.colour || ""}
              onChange={handleChange}
              onBlur={handleBlur}
            >
              <option value="" disabled>Select your Colour</option>
              <option value="red">Red</option>
              <option value="blue">Blue</option>
              <option value="black">Black</option>
            </select>
          </div>

          {/* Finance Mode */}
          <div className="space-y-2 sm:space-y-3">
            <label className="text-[#e3e3e3] text-lg sm:text-xl md:text-2xl font-light">
              <span className="whitespace-nowrap">Finance Mode</span>
              {isFieldRequired("financeMode") && <RequiredMarker />}
            </label>
            <select
              className={`w-full h-[45px] sm:h-[50px] md:h-[60px] bg-transparent border rounded-lg px-3 sm:px-4 text-[#e3e3e3] focus:outline-none appearance-none custom-select ${
                shouldShowError("financeMode") ? "border-red-500" : "border-[#333333] focus:border-[#640064]"
              }`}
              name="financeMode"
              value={formData.vehicleDetails?.financeMode || ""}
              onChange={handleChange}
              onBlur={handleBlur}
            >
              <option value="" disabled>Select your Payment Method</option>
              <option value="cash">Net Cash</option>
              <option value="finance">Finance</option>
              <option value="hp">H.P</option>
            </select>
          </div>
        </div>
    </div>
  );
};
