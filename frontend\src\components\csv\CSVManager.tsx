import React, { useState, useEffect } from 'react';
import { Download, FileText, Calendar, BarChart3, RefreshCw, Trash2, Eye } from 'lucide-react';

interface CSVFile {
  name: string;
  size: number;
  created: string;
  modified: string;
  type: 'detailed' | 'summary' | 'analytics' | 'unknown';
}

interface CSVStats {
  queueSize: number;
  isProcessing: boolean;
  batchSize: number;
  batchTimeout: number;
  retryAttempts: number;
  uptime: number;
  memoryUsage: {
    rss: number;
    heapTotal: number;
    heapUsed: number;
    external: number;
  };
}

const CSVManager: React.FC = () => {
  const [files, setFiles] = useState<CSVFile[]>([]);
  const [stats, setStats] = useState<CSVStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [selectedType, setSelectedType] = useState<'daily' | 'monthly' | 'archive'>('daily');
  const [selectedDate, setSelectedDate] = useState<string>('');
  const [archiveDates, setArchiveDates] = useState<string[]>([]);

  const API_BASE = process.env.REACT_APP_API_URL || 'http://localhost:10000';

  useEffect(() => {
    fetchFiles();
    fetchStats();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(() => {
      fetchStats();
    }, 30000);

    return () => clearInterval(interval);
  }, [selectedType, selectedDate]);

  const fetchFiles = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      const params = new URLSearchParams({
        type: selectedType,
        ...(selectedDate && { date: selectedDate })
      });

      const response = await fetch(`${API_BASE}/api/csv/files?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();
      if (data.success) {
        setFiles(data.data.files || []);
        
        // If archive type and no date selected, get available dates
        if (selectedType === 'archive' && !selectedDate && data.data.directories) {
          setArchiveDates(data.data.directories.map((dir: any) => dir.name));
        }
      }
    } catch (error) {
      console.error('Error fetching CSV files:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE}/api/csv/stats`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();
      if (data.success) {
        setStats(data.data);
      }
    } catch (error) {
      console.error('Error fetching CSV stats:', error);
    }
  };

  const downloadFile = async (filename: string) => {
    try {
      const token = localStorage.getItem('token');
      const params = new URLSearchParams({
        type: selectedType,
        ...(selectedDate && { date: selectedDate })
      });

      const response = await fetch(`${API_BASE}/api/csv/download/${filename}?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('Error downloading file:', error);
    }
  };

  const deleteFile = async (filename: string) => {
    if (!confirm(`Are you sure you want to delete ${filename}?`)) return;

    try {
      const token = localStorage.getItem('token');
      const params = new URLSearchParams({
        type: selectedType,
        ...(selectedDate && { date: selectedDate })
      });

      const response = await fetch(`${API_BASE}/api/csv/files/${filename}?${params}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();
      if (data.success) {
        fetchFiles(); // Refresh file list
      }
    } catch (error) {
      console.error('Error deleting file:', error);
    }
  };

  const forceProcessBatch = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE}/api/csv/force-process`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();
      if (data.success) {
        fetchStats(); // Refresh stats
        setTimeout(fetchFiles, 2000); // Refresh files after processing
      }
    } catch (error) {
      console.error('Error forcing batch process:', error);
    }
  };

  const bulkExport = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE}/api/csv/bulk-export`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();
      if (data.success) {
        alert(`Bulk export initiated: ${data.data.vehiclesProcessed} vehicles processed`);
        fetchStats();
        setTimeout(fetchFiles, 3000);
      }
    } catch (error) {
      console.error('Error initiating bulk export:', error);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getFileTypeIcon = (type: string) => {
    switch (type) {
      case 'detailed': return <FileText className="w-4 h-4 text-blue-500" />;
      case 'summary': return <BarChart3 className="w-4 h-4 text-green-500" />;
      case 'analytics': return <BarChart3 className="w-4 h-4 text-purple-500" />;
      default: return <FileText className="w-4 h-4 text-gray-500" />;
    }
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800">CSV Export Manager</h2>
        <div className="flex gap-2">
          <button
            onClick={bulkExport}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-2"
          >
            <Download className="w-4 h-4" />
            Bulk Export
          </button>
          <button
            onClick={forceProcessBatch}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center gap-2"
          >
            <RefreshCw className="w-4 h-4" />
            Process Queue
          </button>
        </div>
      </div>

      {/* Statistics Panel */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="text-sm font-medium text-blue-800">Queue Size</h3>
            <p className="text-2xl font-bold text-blue-600">{stats.queueSize}</p>
          </div>
          <div className="bg-green-50 p-4 rounded-lg">
            <h3 className="text-sm font-medium text-green-800">Processing</h3>
            <p className="text-2xl font-bold text-green-600">
              {stats.isProcessing ? 'Yes' : 'No'}
            </p>
          </div>
          <div className="bg-purple-50 p-4 rounded-lg">
            <h3 className="text-sm font-medium text-purple-800">Batch Size</h3>
            <p className="text-2xl font-bold text-purple-600">{stats.batchSize}</p>
          </div>
          <div className="bg-orange-50 p-4 rounded-lg">
            <h3 className="text-sm font-medium text-orange-800">Memory Usage</h3>
            <p className="text-2xl font-bold text-orange-600">
              {formatFileSize(stats.memoryUsage.heapUsed)}
            </p>
          </div>
        </div>
      )}

      {/* File Type Selector */}
      <div className="flex gap-4 mb-6">
        <div className="flex gap-2">
          {['daily', 'monthly', 'archive'].map((type) => (
            <button
              key={type}
              onClick={() => {
                setSelectedType(type as any);
                setSelectedDate('');
              }}
              className={`px-4 py-2 rounded-lg capitalize ${
                selectedType === type
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              {type}
            </button>
          ))}
        </div>

        {/* Archive Date Selector */}
        {selectedType === 'archive' && archiveDates.length > 0 && (
          <select
            value={selectedDate}
            onChange={(e) => setSelectedDate(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg"
          >
            <option value="">Select Date</option>
            {archiveDates.map((date) => (
              <option key={date} value={date}>{date}</option>
            ))}
          </select>
        )}
      </div>

      {/* Files List */}
      <div className="overflow-x-auto">
        <table className="w-full border-collapse border border-gray-300">
          <thead>
            <tr className="bg-gray-50">
              <th className="border border-gray-300 px-4 py-2 text-left">Type</th>
              <th className="border border-gray-300 px-4 py-2 text-left">File Name</th>
              <th className="border border-gray-300 px-4 py-2 text-left">Size</th>
              <th className="border border-gray-300 px-4 py-2 text-left">Created</th>
              <th className="border border-gray-300 px-4 py-2 text-left">Modified</th>
              <th className="border border-gray-300 px-4 py-2 text-left">Actions</th>
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr>
                <td colSpan={6} className="border border-gray-300 px-4 py-8 text-center">
                  Loading...
                </td>
              </tr>
            ) : files.length === 0 ? (
              <tr>
                <td colSpan={6} className="border border-gray-300 px-4 py-8 text-center text-gray-500">
                  No CSV files found
                </td>
              </tr>
            ) : (
              files.map((file) => (
                <tr key={file.name} className="hover:bg-gray-50">
                  <td className="border border-gray-300 px-4 py-2">
                    <div className="flex items-center gap-2">
                      {getFileTypeIcon(file.type)}
                      <span className="capitalize">{file.type}</span>
                    </div>
                  </td>
                  <td className="border border-gray-300 px-4 py-2 font-mono text-sm">
                    {file.name}
                  </td>
                  <td className="border border-gray-300 px-4 py-2">
                    {formatFileSize(file.size)}
                  </td>
                  <td className="border border-gray-300 px-4 py-2">
                    {formatDate(file.created)}
                  </td>
                  <td className="border border-gray-300 px-4 py-2">
                    {formatDate(file.modified)}
                  </td>
                  <td className="border border-gray-300 px-4 py-2">
                    <div className="flex gap-2">
                      <button
                        onClick={() => downloadFile(file.name)}
                        className="p-1 text-blue-600 hover:bg-blue-100 rounded"
                        title="Download"
                      >
                        <Download className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => deleteFile(file.name)}
                        className="p-1 text-red-600 hover:bg-red-100 rounded"
                        title="Delete"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default CSVManager;
