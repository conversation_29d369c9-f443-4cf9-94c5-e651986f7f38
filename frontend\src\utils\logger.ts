/**
 * Production-safe logging utility for frontend
 * Provides controlled logging that respects environment settings
 */

// Environment configuration
const isDevelopment = import.meta.env.DEV;
const isProduction = import.meta.env.PROD;

// Log levels
enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3
}

// Current log level based on environment
const currentLogLevel = isDevelopment ? LogLevel.DEBUG : LogLevel.ERROR;

/**
 * Sanitize data to remove sensitive information
 */
const sanitizeData = (data: any): any => {
  if (!data || typeof data !== 'object') {
    return data;
  }

  const sensitiveFields = [
    'password', 'token', 'authorization', 'auth',
    'phone', 'email', 'altPhone', 'pincode',
    'name', 'houseNo', 'street', 'city'
  ];

  const sanitized = { ...data };

  // Remove or mask sensitive fields
  sensitiveFields.forEach(field => {
    if (sanitized[field]) {
      if (typeof sanitized[field] === 'string') {
        // Mask the value but keep some characters for debugging
        const value = sanitized[field];
        if (value.length > 4) {
          sanitized[field] = value.substring(0, 2) + '***' + value.substring(value.length - 2);
        } else {
          sanitized[field] = '***';
        }
      } else {
        sanitized[field] = '[REDACTED]';
      }
    }
  });

  // Recursively sanitize nested objects
  Object.keys(sanitized).forEach(key => {
    if (typeof sanitized[key] === 'object' && sanitized[key] !== null) {
      sanitized[key] = sanitizeData(sanitized[key]);
    }
  });

  return sanitized;
};

/**
 * Format log message with timestamp and context
 */
const formatMessage = (level: string, message: string, context?: any): string => {
  const timestamp = new Date().toISOString();
  let formatted = `[${timestamp}] [${level}] ${message}`;
  
  if (context && isDevelopment) {
    const sanitizedContext = sanitizeData(context);
    formatted += `\nContext: ${JSON.stringify(sanitizedContext, null, 2)}`;
  }
  
  return formatted;
};

/**
 * Production-safe logger
 */
class Logger {
  /**
   * Log error messages (always logged in production)
   */
  error(message: string, error?: Error | any, context?: any): void {
    if (currentLogLevel >= LogLevel.ERROR) {
      const errorInfo = error instanceof Error ? {
        name: error.name,
        message: error.message,
        // Only include stack trace in development
        ...(isDevelopment && { stack: error.stack })
      } : error;

      const logContext = {
        ...context,
        ...(errorInfo && { error: errorInfo })
      };

      console.error(formatMessage('ERROR', message, logContext));
    }
  }

  /**
   * Log warning messages (only in development)
   */
  warn(message: string, context?: any): void {
    if (currentLogLevel >= LogLevel.WARN && isDevelopment) {
      console.warn(formatMessage('WARN', message, context));
    }
  }

  /**
   * Log info messages (only in development)
   */
  info(message: string, context?: any): void {
    if (currentLogLevel >= LogLevel.INFO && isDevelopment) {
      console.info(formatMessage('INFO', message, context));
    }
  }

  /**
   * Log debug messages (only in development)
   */
  debug(message: string, context?: any): void {
    if (currentLogLevel >= LogLevel.DEBUG && isDevelopment) {
      console.log(formatMessage('DEBUG', message, context));
    }
  }

  /**
   * Log API requests (development only)
   */
  apiRequest(method: string, url: string, data?: any): void {
    if (isDevelopment) {
      this.debug(`API Request: ${method} ${url}`, {
        method,
        url,
        data: sanitizeData(data)
      });
    }
  }

  /**
   * Log API responses (development only)
   */
  apiResponse(method: string, url: string, status: number, data?: any): void {
    if (isDevelopment) {
      this.debug(`API Response: ${method} ${url} - ${status}`, {
        method,
        url,
        status,
        data: sanitizeData(data)
      });
    }
  }

  /**
   * Log form validation errors (development only)
   */
  validationError(section: string, errors: any): void {
    if (isDevelopment) {
      this.debug(`Validation Error in ${section}`, {
        section,
        errors: sanitizeData(errors)
      });
    }
  }

  /**
   * Log navigation events (development only)
   */
  navigation(from: string, to: string, context?: any): void {
    if (isDevelopment) {
      this.debug(`Navigation: ${from} -> ${to}`, {
        from,
        to,
        ...sanitizeData(context)
      });
    }
  }

  /**
   * Log authentication events (minimal logging even in production)
   */
  auth(event: string, success: boolean, context?: any): void {
    const message = `Auth ${event}: ${success ? 'Success' : 'Failed'}`;
    
    if (success) {
      this.info(message, sanitizeData(context));
    } else {
      // Log auth failures even in production (but sanitized)
      this.error(message, undefined, sanitizeData(context));
    }
  }

  /**
   * Log performance metrics (development only)
   */
  performance(operation: string, duration: number, context?: any): void {
    if (isDevelopment) {
      this.debug(`Performance: ${operation} took ${duration}ms`, {
        operation,
        duration,
        ...sanitizeData(context)
      });
    }
  }
}

// Export singleton instance
export const logger = new Logger();

// Export for testing
export { LogLevel, sanitizeData, formatMessage };

// Default export
export default logger;
