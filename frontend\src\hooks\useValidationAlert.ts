import { useState, useCallback } from 'react';

export interface ValidationError {
  field: string;
  message: string;
}

export const useValidationAlert = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);

  const showAlert = useCallback((errorMessages: string[]) => {
    if (errorMessages.length > 0) {
      setErrors(errorMessages);
      setIsVisible(true);
    }
  }, []);

  const hideAlert = useCallback(() => {
    setIsVisible(false);
    setErrors([]);
  }, []);

  return {
    isVisible,
    errors,
    showAlert,
    hideAlert
  };
};
