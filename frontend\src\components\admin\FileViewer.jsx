import React, { useState, useEffect } from 'react';
import api from '../../services/api';
import { DocumentIcon, PhotoIcon, DocumentTextIcon, DocumentDownloadIcon, EyeIcon } from '@heroicons/react/outline';

/**
 * Component for viewing and downloading files stored on the server or database
 */
const FileViewer = ({ vehicleId }) => {
  const [vehicle, setVehicle] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [fileStatus, setFileStatus] = useState({});

  useEffect(() => {
    const fetchVehicleData = async () => {
      try {
        setLoading(true);
        const response = await api.get(`/vehicle/${vehicleId}`);
        setVehicle(response.data.data);

        // Initialize file status for each document
        if (response.data.data.documents) {
          const initialStatus = {};
          Object.keys(response.data.data.documents).forEach(key => {
            initialStatus[key] = { loading: false, error: null };
          });
          setFileStatus(initialStatus);
        }

        setLoading(false);
      } catch (err) {
        setError('Failed to load vehicle data');
        setLoading(false);
      }
    };

    if (vehicleId) {
      fetchVehicleData();
    }
  }, [vehicleId]);

  // Function to get file URL
  const getFileUrl = (documentType, download = false) => {
    return `${api.defaults.baseURL}/vehicle/${vehicleId}/documents/${documentType}/file${download ? '?download=true' : ''}`;
  };

  // Function to view a file in a new tab
  const viewFile = (documentType) => {
    // Update status to loading
    setFileStatus(prev => ({
      ...prev,
      [documentType]: { ...prev[documentType], loading: true, error: null }
    }));

    // Create a hidden iframe to test if the file is accessible
    const testFrame = document.createElement('iframe');
    testFrame.style.display = 'none';
    testFrame.src = getFileUrl(documentType);

    // Set a timeout to detect if the file fails to load
    const timeoutId = setTimeout(() => {
      // If we reach this point, the file might not be accessible
      setFileStatus(prev => ({
        ...prev,
        [documentType]: {
          ...prev[documentType],
          loading: false,
          error: 'File access timed out. Opening anyway...'
        }
      }));

      // Open the file anyway
      window.open(getFileUrl(documentType), '_blank');

      // Clean up
      document.body.removeChild(testFrame);
    }, 3000);

    // Handle successful load
    testFrame.onload = () => {
      clearTimeout(timeoutId);
      setFileStatus(prev => ({
        ...prev,
        [documentType]: { ...prev[documentType], loading: false, error: null }
      }));

      // Open the file in a new tab
      window.open(getFileUrl(documentType), '_blank');

      // Clean up
      document.body.removeChild(testFrame);
    };

    // Handle load error
    testFrame.onerror = () => {
      clearTimeout(timeoutId);
      setFileStatus(prev => ({
        ...prev,
        [documentType]: {
          ...prev[documentType],
          loading: false,
          error: 'Error accessing file. Trying anyway...'
        }
      }));

      // Try to open the file anyway
      window.open(getFileUrl(documentType), '_blank');

      // Clean up
      document.body.removeChild(testFrame);
    };

    // Add the test frame to the document
    document.body.appendChild(testFrame);
  };

  // Function to download a file
  const downloadFile = (documentType) => {
    // Update status to loading
    setFileStatus(prev => ({
      ...prev,
      [documentType]: { ...prev[documentType], loading: true, error: null }
    }));

    // Create a link element to download the file
    const link = document.createElement('a');
    link.href = getFileUrl(documentType, true);
    link.target = '_blank';
    link.download = vehicle.documents[documentType].fileName || `${documentType}-file`;

    // Append to the document and click
    document.body.appendChild(link);
    link.click();

    // Clean up
    document.body.removeChild(link);

    // Update status
    setFileStatus(prev => ({
      ...prev,
      [documentType]: { ...prev[documentType], loading: false, error: null }
    }));
  };

  if (loading) return <div className="p-6 bg-gray-800 text-white rounded-lg">Loading documents...</div>;
  if (error) return <div className="p-6 bg-gray-800 text-white rounded-lg text-red-500">Error: {error}</div>;
  if (!vehicle) return <div className="p-6 bg-gray-800 text-white rounded-lg">No vehicle data found</div>;

  // Check if documents exist
  const hasDocuments = vehicle.documents && Object.keys(vehicle.documents).length > 0;

  return (
    <div className="p-6 bg-gray-800 text-white rounded-lg">
      <h2 className="text-2xl font-bold mb-4">Documents for {vehicle.customer?.name || 'Unknown Customer'}</h2>

      {!hasDocuments && <p>No documents found for this vehicle.</p>}

      {hasDocuments && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Object.entries(vehicle.documents).map(([key, doc]) => {
            // Skip entries that don't have either filePath or fileData
            if (!doc.filePath && !doc.fileData) return null;

            // Determine document type
            const isPdf = doc.fileType === 'application/pdf' ||
                         doc.fileName?.toLowerCase().endsWith('.pdf');

            const isImage = doc.fileType?.startsWith('image/') ||
                           ['jpg', 'jpeg', 'png', 'gif'].some(ext =>
                             doc.fileName?.toLowerCase().endsWith(`.${ext}`)
                           );

            const isText = doc.fileType?.startsWith('text/') ||
                          ['txt', 'csv', 'json', 'xml'].some(ext =>
                            doc.fileName?.toLowerCase().endsWith(`.${ext}`)
                          );

            // Get status for this document
            const status = fileStatus[key] || { loading: false, error: null };

            return (
              <div key={key} className="bg-gray-700 p-4 rounded-lg">
                <h3 className="text-xl font-semibold mb-2">{key.charAt(0).toUpperCase() + key.slice(1)}</h3>
                <p className="text-sm mb-2 truncate" title={doc.fileName}>{doc.fileName}</p>

                {/* Document type icon */}
                <div className="mb-3">
                  <div className={`p-4 rounded flex flex-col items-center justify-center ${
                    isPdf ? 'bg-red-800' :
                    isImage ? 'bg-blue-800' :
                    isText ? 'bg-green-800' : 'bg-gray-600'
                  }`}>
                    {isPdf && <DocumentTextIcon className="h-8 w-8 text-white mb-2" />}
                    {isImage && <PhotoIcon className="h-8 w-8 text-white mb-2" />}
                    {!isPdf && !isImage && <DocumentIcon className="h-8 w-8 text-white mb-2" />}

                    <span className="text-white text-sm">
                      {isPdf ? 'PDF Document' :
                       isImage ? 'Image' :
                       isText ? 'Text Document' : 'Document'}
                    </span>

                    {doc.fileSize && (
                      <span className="text-white text-xs mt-1">
                        {(doc.fileSize / 1024).toFixed(1)} KB
                      </span>
                    )}

                    {doc.filePath && (
                      <span className="text-green-300 text-xs mt-1">
                        Stored on server
                      </span>
                    )}

                    {doc.fileData && !doc.filePath && (
                      <span className="text-blue-300 text-xs mt-1">
                        Stored in database
                      </span>
                    )}
                  </div>
                </div>

                {/* Status message */}
                {status.loading && (
                  <div className="text-yellow-400 text-sm mb-2">
                    Loading file...
                  </div>
                )}

                {status.error && (
                  <div className="text-red-400 text-sm mb-2">
                    {status.error}
                  </div>
                )}

                {/* Action buttons */}
                <div className="flex space-x-2">
                  <button
                    onClick={() => viewFile(key)}
                    disabled={status.loading}
                    className={`flex items-center space-x-1 px-3 py-2 rounded ${
                      status.loading
                        ? 'bg-gray-500 cursor-not-allowed'
                        : 'bg-blue-600 hover:bg-blue-700'
                    } text-white`}
                  >
                    <EyeIcon className="h-4 w-4" />
                    <span>View</span>
                  </button>

                  <button
                    onClick={() => downloadFile(key)}
                    disabled={status.loading}
                    className={`flex items-center space-x-1 px-3 py-2 rounded ${
                      status.loading
                        ? 'bg-gray-500 cursor-not-allowed'
                        : 'bg-green-600 hover:bg-green-700'
                    } text-white`}
                  >
                    <DocumentDownloadIcon className="h-4 w-4" />
                    <span>Download</span>
                  </button>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default FileViewer;
