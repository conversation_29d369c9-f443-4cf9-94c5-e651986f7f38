import mongoose from 'mongoose';

// Customer schema
const customerSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Name is required'],
    trim: true
  },
  phone: {
    type: String,
    required: [true, 'Phone number is required'],
    trim: true
  },
  altPhone: {
    type: String,
    trim: true
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    trim: true,
    lowercase: true
  },
  address: {
    houseNo: {
      type: String,
      required: false, // Made optional for initial customer creation
      trim: true
    },
    street: {
      type: String,
      required: false, // Made optional for initial customer creation
      trim: true
    },
    city: {
      type: String,
      required: false, // Made optional for initial customer creation
      trim: true
    },
    state: {
      type: String,
      required: false, // Made optional for initial customer creation
      trim: true
    },
    pincode: {
      type: String,
      required: false, // Made optional for initial customer creation
      trim: true
    },
    fullAddress: {
      type: String,
      trim: true
    }
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  // Reference to vehicles owned by this customer
  vehicles: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Vehicle'
  }]
});

// Generate full address before saving
customerSchema.pre('save', function(next) {
  // Update the updatedAt field
  this.updatedAt = Date.now();

  // Generate full address
  const address = this.address;
  if (address && address.houseNo && address.street && address.city && address.state && address.pincode) {
    this.address.fullAddress = `${address.houseNo}, ${address.street}, ${address.city}, ${address.state} ${address.pincode}`;
  }

  next();
});

// Create compound indexes for advanced filtering and sorting
customerSchema.index({ createdAt: -1 }); // Date sorting (primary)
customerSchema.index({ updatedAt: -1 }); // Last modified sorting
customerSchema.index({ name: 1, createdAt: -1 }); // Name + Date sorting
customerSchema.index({ phone: 1 }, { unique: true }); // Unique phone constraint
customerSchema.index({ email: 1 }, { unique: true }); // Unique email constraint
customerSchema.index({ 'address.city': 1, createdAt: -1 }); // City + Date
customerSchema.index({ 'address.state': 1, createdAt: -1 }); // State + Date

// Create a text index for search functionality
customerSchema.index({
  name: 'text',
  email: 'text',
  phone: 'text',
  'address.fullAddress': 'text'
});

const Customer = mongoose.model('Customer', customerSchema);

export default Customer;
