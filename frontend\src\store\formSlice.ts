import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { validateField, validateFormSection, validateFiles, validationRules } from "../utils/validation";

interface FormDataState {
  // Form data
  formData: {
    // Customer Information
    name: string;
    phone: string;
    email: string;
    altPhone: string;
    houseNo: string;
    street: string;
    city: string;
    state: string;
    pincode: string;

    // Vehicle Details
    vehicleDetails: {
      model: string;
      colour: string;
      financeMode: string;
    };

    // Registration Requirements
    hasSpecialNumber: string;
    expectedSpecialNumber: string;
    registrationType: string;
    splNumber: string;

    // Exchange Vehicle Details
    hasExchange: string;
    exchangeModel: string;
    exchangeYear: string;
    exchangeKmsDriven: string;

    // Insurance Details
    nomineeName: string;
    nomineeAge: string;
    nomineeRelationship: string;

    // Documents
    documents: Record<string, any>;
  };

  // Validation errors
  errors: {
    [key: string]: string;
  };

  // Track which fields have been touched by the user
  touchedFields: {
    [key: string]: boolean;
  };

  // Current form section validation status
  currentSection: {
    name: string;
    isValid: boolean;
  };
}

const initialState: FormDataState = {
  formData: {
    // Customer Information
    name: "",
    phone: "",
    email: "",
    altPhone: "",
    houseNo: "",
    street: "",
    city: "",
    state: "",
    pincode: "",

    // Vehicle Details
    vehicleDetails: {
      model: "",
      colour: "",
      financeMode: ""
    },

    // Registration Requirements
    hasSpecialNumber: "no",
    expectedSpecialNumber: "",
    registrationType: "permanent",
    splNumber: "",

    // Exchange Vehicle Details
    hasExchange: "",
    exchangeModel: "",
    exchangeYear: "",
    exchangeKmsDriven: "",

    // Insurance Details
    insuranceType: "bumperToBumper",
    nomineeName: "",
    nomineeAge: "",
    nomineeRelationship: "",

    // Documents
    documents: {},
  },
  errors: {},
  touchedFields: {}, // Fields that have been interacted with by the user
  currentSection: {
    name: "customer",
    isValid: false,
  },
};

const formSlice = createSlice({
  name: "form",
  initialState,
  reducers: {
    // Update a single form field
    updateFormField: (
      state,
      action: PayloadAction<{ field: string; value: any }>
    ) => {
      const { field, value } = action.payload;

      // Update the form data
      if (field.includes('.')) {
        // Handle nested fields (e.g., vehicleDetails.model)
        const [parent, child] = field.split('.');

        // Make sure the parent object exists
        if (!state.formData[parent]) {
          state.formData[parent] = {};
        }

        // If we're updating the entire object
        if (!child) {
          state.formData[parent] = value;
        } else {
          // Update a specific field in the object
          state.formData[parent][child] = value;
        }

        // Only validate if we're updating a specific field (not the entire object)
        if (child) {
          // Get validation rules for this field
          let rules = {};
          if (parent === 'vehicleDetails' && validationRules.vehicleDetails[child]) {
            rules = validationRules.vehicleDetails[child];
          }

          // Validate the field
          const result = validateField(value, rules, child);

          // Update errors with the full path as the key
          if (!result.isValid) {
            state.errors[field] = result.error || `Invalid ${child}`;
          } else {
            delete state.errors[field];
          }
        }
      } else {
        // Handle top-level fields
        state.formData[field] = value;

        // Find the appropriate validation rules
        let rules = {};
        // Check each section for this field
        Object.values(validationRules).forEach(sectionRules => {
          if (field in sectionRules) {
            rules = sectionRules[field as keyof typeof sectionRules];
          }
        });

        // Validate the field
        const result = validateField(value, rules, field);

        // Update errors
        if (!result.isValid) {
          state.errors[field] = result.error || `Invalid ${field}`;
        } else {
          delete state.errors[field];
        }
      }
    },

    // Validate an entire form section
    validateSection: (state, action: PayloadAction<string>) => {
      const section = action.payload;
      let sectionData: any = {};

      // Extract relevant data for the section
      switch (section) {
        case "customer":
          sectionData = {
            name: state.formData.name,
            phone: state.formData.phone,
            email: state.formData.email,
            altPhone: state.formData.altPhone,
            houseNo: state.formData.houseNo,
            street: state.formData.street,
            city: state.formData.city,
            state: state.formData.state,
            pincode: state.formData.pincode,
          };
          break;
        case "vehicleDetails":
          // Make sure vehicleDetails exists and has all required fields
          if (!state.formData.vehicleDetails || typeof state.formData.vehicleDetails !== 'object') {
            state.formData.vehicleDetails = {
              model: "",
              colour: "",
              financeMode: ""
            };
          }

          // Create a clean object with only the required fields
          sectionData = {
            model: state.formData.vehicleDetails.model || "",
            colour: state.formData.vehicleDetails.colour || "",
            financeMode: state.formData.vehicleDetails.financeMode || "",
          };



          // Update errors with the correct field paths for nested fields
          const { isValid, errors } = validateFormSection(sectionData, section);

          // Update state
          state.currentSection = {
            name: section,
            isValid,
          };

          // Map errors to their full paths for nested fields
          const mappedErrors: Record<string, string> = {};
          Object.entries(errors).forEach(([field, error]) => {
            mappedErrors[`vehicleDetails.${field}`] = error;
          });

          // Clear any existing registration errors that might be affecting this section
          Object.keys(state.errors).forEach(key => {
            if (key.startsWith('hasSpecialNumber') || key.startsWith('expectedSpecialNumber') || key.startsWith('registrationType')) {
              delete state.errors[key];
            }
          });

          // Update errors
          state.errors = { ...state.errors, ...mappedErrors };

          // Return early since we've already handled validation
          return;
        case "exchangeVehicle":
          sectionData = {
            hasExchange: state.formData.hasExchange,
            exchangeModel: state.formData.exchangeModel,
            exchangeYear: state.formData.exchangeYear,
            exchangeKmsDriven: state.formData.exchangeKmsDriven,
          };
          break;
        case "registration":
          sectionData = {
            hasSpecialNumber: state.formData.hasSpecialNumber,
            expectedSpecialNumber: state.formData.expectedSpecialNumber,
            registrationType: state.formData.registrationType,
          };
          break;
        case "insurance":
          sectionData = {
            insuranceType: state.formData.insuranceType,
            nomineeName: state.formData.nomineeName,
            nomineeAge: state.formData.nomineeAge,
            nomineeRelationship: state.formData.nomineeRelationship,
          };
          break;
        case "documents":
          // Special handling for documents
          const validationResult = validateFiles(state.formData.documents);
          state.currentSection = {
            name: section,
            isValid: validationResult.isValid,
          };

          // Set attemptedSubmit flag to true when validating documents section
          if (!validationResult.isValid) {
            validationResult.errors.attemptedSubmit = 'true';
          }

          state.errors = { ...state.errors, ...validationResult.errors };
          return;
      }

      // Validate the section
      const { isValid, errors } = validateFormSection(sectionData, section);

      // Update state
      state.currentSection = {
        name: section,
        isValid,
      };

      // Update errors
      state.errors = { ...state.errors, ...errors };
    },

    // Set document data
    setDocumentData: (
      state,
      action: PayloadAction<{ docType: string; data: any }>
    ) => {
      const { docType, data } = action.payload;
      state.formData.documents[docType] = data;
    },

    // Reset form data
    resetFormData: (state) => {
      // Reset all state properties to initial values
      Object.assign(state, initialState);

      // Explicitly reset all form fields to ensure they're cleared
      state.formData = {
        // Customer Information
        name: "",
        phone: "",
        email: "",
        altPhone: "",
        houseNo: "",
        street: "",
        city: "",
        state: "",
        pincode: "",

        // Vehicle Details
        vehicleDetails: {
          model: "",
          colour: "",
          financeMode: ""
        },

        // Registration Requirements
        hasSpecialNumber: "no",
        expectedSpecialNumber: "",
        registrationType: "permanent",
        splNumber: "",

        // Exchange Vehicle Details
        hasExchange: "",
        exchangeModel: "",
        exchangeYear: "",
        exchangeKmsDriven: "",

        // Insurance Details
        insuranceType: "bumperToBumper",
        nomineeName: "",
        nomineeAge: "",
        nomineeRelationship: "",

        // Documents
        documents: {},
      };

      // Clear all errors and touched fields
      state.errors = {};
      state.touchedFields = {};

      // Reset current section to the first section
      state.currentSection = {
        name: "customer",
        isValid: false,
        isCompleted: false
      };



      return state;
    },

    // Reset a specific section of the form
    resetSection: (state, action: PayloadAction<string>) => {
      const section = action.payload;

      switch (section) {
        case "customer":
          state.formData.name = initialState.formData.name;
          state.formData.phone = initialState.formData.phone;
          state.formData.email = initialState.formData.email;
          state.formData.altPhone = initialState.formData.altPhone;
          state.formData.houseNo = initialState.formData.houseNo;
          state.formData.street = initialState.formData.street;
          state.formData.city = initialState.formData.city;
          state.formData.state = initialState.formData.state;
          state.formData.pincode = initialState.formData.pincode;
          break;
        case "vehicleDetails":
          state.formData.vehicleDetails = initialState.formData.vehicleDetails;
          break;
        case "exchangeVehicle":
          state.formData.hasExchange = initialState.formData.hasExchange;
          state.formData.exchangeModel = initialState.formData.exchangeModel;
          state.formData.exchangeYear = initialState.formData.exchangeYear;
          state.formData.exchangeKmsDriven = initialState.formData.exchangeKmsDriven;
          break;
        case "registration":
          state.formData.hasSpecialNumber = initialState.formData.hasSpecialNumber;
          state.formData.expectedSpecialNumber = initialState.formData.expectedSpecialNumber;
          state.formData.registrationType = initialState.formData.registrationType;
          break;
        case "insurance":
          state.formData.nomineeName = initialState.formData.nomineeName;
          state.formData.nomineeAge = initialState.formData.nomineeAge;
          state.formData.nomineeRelationship = initialState.formData.nomineeRelationship;
          break;
        case "documents":
          state.formData.documents = {};
          break;
      }

      // Clear errors related to this section
      const sectionRules = validationRules[section as keyof typeof validationRules];
      if (sectionRules) {
        Object.keys(sectionRules).forEach(field => {
          delete state.errors[field];
        });
      }
    },

    // Clear validation errors
    clearErrors: (state) => {
      state.errors = {};
    },

    // Clear errors for a specific section
    clearSectionErrors: (state, action: PayloadAction<string>) => {
      const section = action.payload;
      const sectionRules = validationRules[section as keyof typeof validationRules];

      if (sectionRules) {
        Object.keys(sectionRules).forEach(field => {
          delete state.errors[field];
        });
      }
    },

    // Clear error for a specific field
    clearFieldError: (state, action: PayloadAction<string>) => {
      const field = action.payload;
      if (state.errors && state.errors[field]) {
        delete state.errors[field];
      }
    },

    // Mark a field as touched
    setFieldTouched: (state, action: PayloadAction<string>) => {
      const field = action.payload;
      state.touchedFields[field] = true;
    },

    // Mark all fields in a section as touched (useful for form submission)
    setAllFieldsTouched: (state, action: PayloadAction<string>) => {
      const section = action.payload;
      const sectionRules = validationRules[section as keyof typeof validationRules];

      if (sectionRules) {
        Object.keys(sectionRules).forEach(field => {
          // Handle nested fields
          if (field.includes('.')) {
            state.touchedFields[field] = true;
          } else {
            state.touchedFields[field] = true;
          }
        });
      }
    },

    // Legacy actions for backward compatibility
    updateFormData: (state, action: PayloadAction<{ key: string; value: any }>) => {
      const { key, value } = action.payload;
      if (key.includes('.')) {
        const [parent, child] = key.split('.');
        state.formData[parent][child] = value;
      } else {
        state.formData[key] = value;
      }
    },

    updateMultipleFormData: (state, action: PayloadAction<{ [key: string]: any }>) => {
      Object.entries(action.payload).forEach(([key, value]) => {
        if (key.includes('.')) {
          const [parent, child] = key.split('.');
          state.formData[parent][child] = value;
        } else {
          state.formData[key] = value;
        }
      });
    },
  },
});

// Utility function for standardized validation
// This can be used in form components to ensure consistent validation behavior
export const validateField = (field: string, value: any, section: string) => (dispatch: any) => {
  // First update the field value
  dispatch(updateFormField({ field, value }));

  // Mark the field as touched
  dispatch(setFieldTouched(field));

  // Then validate the section
  setTimeout(() => {
    dispatch(validateSection(section));
  }, 0);
};

export const {
  updateFormField,
  validateSection,
  setDocumentData,
  resetFormData,
  resetSection,
  clearErrors,
  clearSectionErrors,
  clearFieldError,
  setFieldTouched,
  setAllFieldsTouched,
  updateFormData,
  updateMultipleFormData,
} = formSlice.actions;

export default formSlice.reducer;