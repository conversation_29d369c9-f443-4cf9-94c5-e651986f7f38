import Vehicle from '../models/Vehicle.js';
import logger from './logger.js';

/**
 * Utility function to clean up pending vehicle records that are older than a specified time
 * @param {number} hours - Number of hours to consider a record stale (default: 24)
 * @returns {Promise<number>} - Number of records deleted
 */
export const cleanupPendingRecords = async (hours = 24) => {
  try {
    // Calculate the cutoff date
    const cutoffDate = new Date();
    cutoffDate.setHours(cutoffDate.getHours() - hours);

    // Find and delete pending records older than the cutoff date
    const result = await Vehicle.deleteMany({
      status: 'pending',
      updatedAt: { $lt: cutoffDate }
    });

    logger.info(`Cleaned up ${result.deletedCount} stale pending records`);
    return result.deletedCount;
  } catch (error) {
    logger.error('Error cleaning up pending records:', error);
    throw error;
  }
};

export default cleanupPendingRecords;
