import mongoose from 'mongoose';
import Customer from '../models/Customer.js';
import Vehicle from '../models/Vehicle.js';
import { isMongoConnected } from '../config/db.js';
import logger from '../utils/logger.js';

/**
 * Get dashboard statistics
 */
export const getDashboardStats = async (req, res) => {
  try {
    if (!isMongoConnected()) {
      return res.status(200).json({
        success: true,
        data: {
          totalCustomers: 0,
          totalVehicles: 0,
          pendingVehicles: 0,
          completedVehicles: 0,
          recentCustomers: [],
          recentVehicles: []
        },
        message: 'MongoDB not connected, using default values'
      });
    }

    // Get counts - only show customers with completed vehicles in dashboard
    const allCustomersWithVehicles = await Customer.find()
      .populate('vehicles', 'status');

    const customersWithCompletedVehicles = allCustomersWithVehicles.filter(customer => {
      if (!customer.vehicles || customer.vehicles.length === 0) return false;
      return customer.vehicles.some(vehicle => vehicle.status === 'completed');
    });

    const totalCustomers = customersWithCompletedVehicles.length;
    const totalVehicles = await Vehicle.countDocuments();
    const pendingVehicles = await Vehicle.countDocuments({ status: 'pending' });
    const completedVehicles = await Vehicle.countDocuments({ status: 'completed' });

    // Get recent customers - only those with completed vehicles
    const recentCustomers = customersWithCompletedVehicles
      .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
      .slice(0, 5);

    // Get recent completed vehicles only
    const recentVehicles = await Vehicle.find({ status: 'completed' })
      .sort({ createdAt: -1 })
      .limit(5);

    res.status(200).json({
      success: true,
      data: {
        totalCustomers,
        totalVehicles,
        pendingVehicles,
        completedVehicles,
        recentCustomers,
        recentVehicles
      }
    });
  } catch (error) {
    logger.error('Error getting dashboard stats:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting dashboard statistics',
      error: error.message
    });
  }
};

/**
 * Get all customers with advanced filtering, pagination, search, sorting, and date queries
 */
export const getAllCustomers = async (req, res) => {
  try {
    if (!isMongoConnected()) {
      return res.status(200).json({
        success: true,
        data: {
          customers: [],
          totalCount: 0,
          page: 1,
          limit: 10,
          totalPages: 0
        },
        message: 'MongoDB not connected, using default values'
      });
    }

    // Parse query parameters with advanced filtering
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    const search = req.query.search || '';
    const sortField = req.query.sortField || 'createdAt';
    const sortOrder = req.query.sortOrder === 'asc' ? 1 : -1;

    // Advanced filter parameters
    const {
      dateFrom,
      dateTo,
      dateRange, // 'lastWeek', 'lastMonth'
      model,
      color,
      status,
      city,
      state
    } = req.query;

    // Build base query
    let query = {};

    // Text search functionality
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { phone: { $regex: search, $options: 'i' } },
        { 'address.city': { $regex: search, $options: 'i' } },
        { 'address.state': { $regex: search, $options: 'i' } }
      ];
    }

    // Production-ready date range filters with proper timezone handling
    if (dateFrom || dateTo || dateRange) {
      const dateFilter = {};

      if (dateRange) {
        const now = new Date();

        switch (dateRange) {
          case 'today':
            // Today: 00:00:00 to 23:59:59 in UTC to match database timestamps
            const todayStart = new Date();
            todayStart.setUTCHours(0, 0, 0, 0);
            const todayEnd = new Date();
            todayEnd.setUTCHours(23, 59, 59, 999);
            dateFilter.$gte = todayStart;
            dateFilter.$lte = todayEnd;
            break;

          case 'lastWeek':
            // Previous complete week (Sunday to Saturday) - Industry standard
            const today = new Date();
            const currentDayOfWeek = today.getUTCDay(); // 0 = Sunday, 1 = Monday, etc.

            // Calculate last week's Sunday (start of previous week)
            const lastWeekStart = new Date(today);
            lastWeekStart.setUTCDate(today.getUTCDate() - currentDayOfWeek - 7);
            lastWeekStart.setUTCHours(0, 0, 0, 0);

            // Calculate last week's Saturday (end of previous week)
            const lastWeekEnd = new Date(lastWeekStart);
            lastWeekEnd.setUTCDate(lastWeekStart.getUTCDate() + 6);
            lastWeekEnd.setUTCHours(23, 59, 59, 999);

            dateFilter.$gte = lastWeekStart;
            dateFilter.$lte = lastWeekEnd;
            break;

          case 'lastMonth':
            // Previous complete month (1st to last day) - Industry standard
            const currentDate = new Date();

            // Get first day of previous month using UTC
            const lastMonthStart = new Date();
            lastMonthStart.setUTCFullYear(currentDate.getUTCFullYear());
            lastMonthStart.setUTCMonth(currentDate.getUTCMonth() - 1);
            lastMonthStart.setUTCDate(1);
            lastMonthStart.setUTCHours(0, 0, 0, 0);

            // Get last day of previous month using UTC
            const lastMonthEnd = new Date();
            lastMonthEnd.setUTCFullYear(currentDate.getUTCFullYear());
            lastMonthEnd.setUTCMonth(currentDate.getUTCMonth());
            lastMonthEnd.setUTCDate(0); // This sets to last day of previous month
            lastMonthEnd.setUTCHours(23, 59, 59, 999);

            dateFilter.$gte = lastMonthStart;
            dateFilter.$lte = lastMonthEnd;
            break;
        }
      } else {
        // Custom date range with proper time boundaries
        if (dateFrom) {
          const fromDate = new Date(dateFrom);
          // Set to start of day (00:00:00)
          fromDate.setHours(0, 0, 0, 0);
          dateFilter.$gte = fromDate;
        }
        if (dateTo) {
          const toDate = new Date(dateTo);
          // Set to end of day (23:59:59)
          toDate.setHours(23, 59, 59, 999);
          dateFilter.$lte = toDate;
        }
      }

      if (Object.keys(dateFilter).length > 0) {
        query.createdAt = dateFilter;

        // Log the applied date filter for debugging
        logger.info('Applied date filter:', {
          dateRange,
          dateFrom,
          dateTo,
          filter: {
            from: dateFilter.$gte?.toISOString(),
            to: dateFilter.$lte?.toISOString()
          }
        });
      }
    }

    // Address filters
    if (city) {
      query['address.city'] = { $regex: city, $options: 'i' };
    }
    if (state) {
      query['address.state'] = { $regex: state, $options: 'i' };
    }

    // Build aggregation pipeline to filter customers with completed vehicles at database level
    const pipeline = [
      // Match customers based on basic query
      { $match: query },

      // Lookup vehicles for each customer
      {
        $lookup: {
          from: 'vehicles',
          localField: 'vehicles',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },

      // Filter customers who have at least one completed vehicle
      {
        $match: {
          'vehicleDetails': {
            $elemMatch: {
              'status': 'completed'
            }
          }
        }
      },

      // Apply vehicle-based filters if specified
      ...(model || color || status ? [{
        $match: {
          'vehicleDetails': {
            $elemMatch: {
              'status': 'completed',
              ...(model && { 'vehicleDetails.model': { $regex: model, $options: 'i' } }),
              ...(color && { 'vehicleDetails.colour': { $regex: color, $options: 'i' } }),
              ...(status && { 'status': status })
            }
          }
        }
      }] : []),

      // Add total count for pagination
      {
        $facet: {
          data: [
            { $sort: { [sortField]: sortOrder } },
            { $skip: skip },
            { $limit: limit },
            {
              $lookup: {
                from: 'vehicles',
                localField: 'vehicles',
                foreignField: '_id',
                as: 'vehicles',
                pipeline: [
                  {
                    $project: {
                      vehicleDetails: 1,
                      status: 1
                    }
                  }
                ]
              }
            }
          ],
          totalCount: [
            { $count: 'count' }
          ]
        }
      }
    ];

    // Execute aggregation
    const result = await Customer.aggregate(pipeline);
    const customers = result[0]?.data || [];
    const totalCount = result[0]?.totalCount[0]?.count || 0;
    const totalPages = Math.ceil(totalCount / limit);

    // Debug logging for date filter results
    if (dateRange) {
      logger.info(`Date filter results for ${dateRange}:`, {
        totalCount,
        customersReturned: customers.length,
        dateRange,
        appliedQuery: JSON.stringify(query)
      });
    }

    res.status(200).json({
      success: true,
      data: {
        customers,
        totalCount,
        page,
        limit,
        totalPages,
        appliedFilters: {
          search,
          dateFrom,
          dateTo,
          dateRange,
          model,
          color,
          status,
          city,
          state
        }
      }
    });
  } catch (error) {
    logger.error('Error getting customers:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting customers',
      error: error.message
    });
  }
};

/**
 * Get filter options for advanced filtering
 */
export const getFilterOptions = async (req, res) => {
  try {
    if (!isMongoConnected()) {
      return res.status(200).json({
        success: true,
        data: {
          models: ['Model 1', 'Model 2', 'Model 3'],
          colors: ['Red', 'Blue', 'Black'],
          statuses: ['pending', 'completed'],
          cities: [],
          states: []
        },
        message: 'MongoDB not connected, using default values'
      });
    }

    // Get unique vehicle models
    const models = await Vehicle.distinct('vehicleDetails.model', {
      'vehicleDetails.model': { $exists: true, $ne: null, $ne: '' }
    });

    // Get unique vehicle colors
    const colors = await Vehicle.distinct('vehicleDetails.colour', {
      'vehicleDetails.colour': { $exists: true, $ne: null, $ne: '' }
    });

    // Get unique vehicle statuses
    const statuses = await Vehicle.distinct('status', {
      status: { $exists: true, $ne: null, $ne: '' }
    });

    // Get unique cities from customers
    const cities = await Customer.distinct('address.city', {
      'address.city': { $exists: true, $ne: null, $ne: '' }
    });

    // Get unique states from customers
    const states = await Customer.distinct('address.state', {
      'address.state': { $exists: true, $ne: null, $ne: '' }
    });

    res.status(200).json({
      success: true,
      data: {
        models: models.filter(Boolean).sort(),
        colors: colors.filter(Boolean).sort(),
        statuses: statuses.filter(Boolean).sort(),
        cities: cities.filter(Boolean).sort(),
        states: states.filter(Boolean).sort()
      }
    });
  } catch (error) {
    logger.error('Error getting filter options:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting filter options',
      error: error.message
    });
  }
};

/**
 * Get customer by ID with their vehicles
 */
export const getCustomerById = async (req, res) => {
  try {
    if (!isMongoConnected()) {
      return res.status(404).json({
        success: false,
        message: 'MongoDB not connected, cannot retrieve customer'
      });
    }

    const customer = await Customer.findById(req.params.id)
      .populate('vehicles');

    if (!customer) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found'
      });
    }

    res.status(200).json({
      success: true,
      data: customer
    });
  } catch (error) {
    logger.error('Error getting customer:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting customer',
      error: error.message
    });
  }
};

/**
 * Create a new customer
 */
export const createCustomer = async (req, res) => {
  try {
    if (!isMongoConnected()) {
      return res.status(500).json({
        success: false,
        message: 'MongoDB not connected, cannot create customer'
      });
    }

    // Extract customer data from request body
    const {
      name,
      phone,
      altPhone,
      email,
      address
    } = req.body;

    // Create new customer
    const customer = new Customer({
      name,
      phone,
      altPhone,
      email,
      address: {
        houseNo: address?.houseNo || '',
        street: address?.street || '',
        city: address?.city || '',
        state: address?.state || '',
        pincode: address?.pincode || ''
      }
    });

    // Save customer
    await customer.save();

    res.status(201).json({
      success: true,
      data: customer
    });
  } catch (error) {
    logger.error('Error creating customer:', error);
    res.status(500).json({
      success: false,
      message: 'Error creating customer',
      error: error.message
    });
  }
};

/**
 * Update customer by ID
 */
export const updateCustomer = async (req, res) => {
  try {
    if (!isMongoConnected()) {
      return res.status(500).json({
        success: false,
        message: 'MongoDB not connected, cannot update customer'
      });
    }

    // Find customer
    const customer = await Customer.findById(req.params.id);

    if (!customer) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found'
      });
    }

    // Update customer fields
    const {
      name,
      phone,
      altPhone,
      email,
      address
    } = req.body;

    if (name) customer.name = name;
    if (phone) customer.phone = phone;
    if (altPhone !== undefined) customer.altPhone = altPhone;
    if (email) customer.email = email;

    if (address) {
      if (address.houseNo) customer.address.houseNo = address.houseNo;
      if (address.street) customer.address.street = address.street;
      if (address.city) customer.address.city = address.city;
      if (address.state) customer.address.state = address.state;
      if (address.pincode) customer.address.pincode = address.pincode;
    }

    // Save updated customer
    await customer.save();

    res.status(200).json({
      success: true,
      data: customer
    });
  } catch (error) {
    logger.error('Error updating customer:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating customer',
      error: error.message
    });
  }
};

/**
 * Delete customer by ID
 */
export const deleteCustomer = async (req, res) => {
  try {
    if (!isMongoConnected()) {
      return res.status(500).json({
        success: false,
        message: 'MongoDB not connected, cannot delete customer'
      });
    }

    // Find customer
    const customer = await Customer.findById(req.params.id);

    if (!customer) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found'
      });
    }

    // Delete customer
    await customer.remove();

    res.status(200).json({
      success: true,
      message: 'Customer deleted successfully'
    });
  } catch (error) {
    logger.error('Error deleting customer:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting customer',
      error: error.message
    });
  }
};

/**
 * Get all vehicles with pagination and filtering
 */
export const getAllVehicles = async (req, res) => {
  try {
    if (!isMongoConnected()) {
      return res.status(200).json({
        success: true,
        data: {
          vehicles: [],
          totalCount: 0,
          page: 1,
          limit: 10,
          totalPages: 0
        },
        message: 'MongoDB not connected, using default values'
      });
    }

    // Parse query parameters
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    const search = req.query.search || '';
    const status = req.query.status || '';
    const model = req.query.model || '';
    const sortField = req.query.sortField || 'createdAt';
    const sortOrder = req.query.sortOrder === 'asc' ? 1 : -1;

    // Build query
    let query = {};

    // Add filters
    if (status) {
      query.status = status;
    }

    if (model) {
      query['vehicleDetails.model'] = { $regex: model, $options: 'i' };
    }

    // Add search functionality
    if (search) {
      // Create a more complex query for searching across multiple fields
      const searchQuery = {
        $or: [
          { 'customer.name': { $regex: search, $options: 'i' } },
          { 'customer.phone': { $regex: search, $options: 'i' } },
          { 'customer.email': { $regex: search, $options: 'i' } },
          { 'vehicleDetails.model': { $regex: search, $options: 'i' } },
          { 'vehicleDetails.colour': { $regex: search, $options: 'i' } }
        ]
      };

      // Combine with existing query
      query = { ...query, ...searchQuery };
    }

    // Execute query with pagination
    const vehicles = await Vehicle.find(query)
      .sort({ [sortField]: sortOrder })
      .skip(skip)
      .limit(limit);

    // Get total count for pagination
    const totalCount = await Vehicle.countDocuments(query);
    const totalPages = Math.ceil(totalCount / limit);

    res.status(200).json({
      success: true,
      data: {
        vehicles,
        totalCount,
        page,
        limit,
        totalPages
      }
    });
  } catch (error) {
    logger.error('Error getting vehicles:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting vehicles',
      error: error.message
    });
  }
};

/**
 * Get vehicle by ID
 */
export const getVehicleById = async (req, res) => {
  try {
    if (!isMongoConnected()) {
      return res.status(404).json({
        success: false,
        message: 'MongoDB not connected, cannot retrieve vehicle'
      });
    }

    const vehicle = await Vehicle.findById(req.params.id);

    if (!vehicle) {
      return res.status(404).json({
        success: false,
        message: 'Vehicle not found'
      });
    }

    res.status(200).json({
      success: true,
      data: vehicle
    });
  } catch (error) {
    logger.error('Error getting vehicle:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting vehicle',
      error: error.message
    });
  }
};

/**
 * Update vehicle status
 */
export const updateVehicleStatus = async (req, res) => {
  try {
    if (!isMongoConnected()) {
      return res.status(500).json({
        success: false,
        message: 'MongoDB not connected, cannot update vehicle status'
      });
    }

    const { status } = req.body;

    if (!status) {
      return res.status(400).json({
        success: false,
        message: 'Status is required'
      });
    }

    // Find vehicle
    const vehicle = await Vehicle.findById(req.params.id);

    if (!vehicle) {
      return res.status(404).json({
        success: false,
        message: 'Vehicle not found'
      });
    }

    // Update status
    vehicle.status = status;
    vehicle.updatedAt = Date.now();

    // Save updated vehicle
    await vehicle.save();

    res.status(200).json({
      success: true,
      data: vehicle
    });
  } catch (error) {
    logger.error('Error updating vehicle status:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating vehicle status',
      error: error.message
    });
  }
};

/**
 * Link customer to vehicle
 */
export const linkCustomerToVehicle = async (req, res) => {
  try {
    if (!isMongoConnected()) {
      return res.status(500).json({
        success: false,
        message: 'MongoDB not connected, cannot link customer to vehicle'
      });
    }

    const { customerId, vehicleId } = req.body;

    if (!customerId || !vehicleId) {
      return res.status(400).json({
        success: false,
        message: 'Customer ID and Vehicle ID are required'
      });
    }

    // Find customer and vehicle
    const customer = await Customer.findById(customerId);
    const vehicle = await Vehicle.findById(vehicleId);

    if (!customer) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found'
      });
    }

    if (!vehicle) {
      return res.status(404).json({
        success: false,
        message: 'Vehicle not found'
      });
    }

    // Add vehicle to customer's vehicles array if not already there
    if (!customer.vehicles.includes(vehicleId)) {
      customer.vehicles.push(vehicleId);
      await customer.save();
    }

    // Update vehicle's customer information
    vehicle.customer = {
      name: customer.name,
      phone: customer.phone,
      email: customer.email,
      altPhone: customer.altPhone,
      houseNo: customer.address.houseNo,
      street: customer.address.street,
      city: customer.address.city,
      state: customer.address.state,
      pincode: customer.address.pincode
    };

    await vehicle.save();

    res.status(200).json({
      success: true,
      message: 'Customer linked to vehicle successfully',
      data: {
        customer,
        vehicle
      }
    });
  } catch (error) {
    logger.error('Error linking customer to vehicle:', error);
    res.status(500).json({
      success: false,
      message: 'Error linking customer to vehicle',
      error: error.message
    });
  }
};

/**
 * Get customer vehicles
 */
export const getCustomerVehicles = async (req, res) => {
  try {
    if (!isMongoConnected()) {
      return res.status(404).json({
        success: false,
        message: 'MongoDB not connected, cannot retrieve customer vehicles'
      });
    }

    const customer = await Customer.findById(req.params.id)
      .populate('vehicles');

    if (!customer) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found'
      });
    }

    res.status(200).json({
      success: true,
      data: customer.vehicles
    });
  } catch (error) {
    logger.error('Error getting customer vehicles:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting customer vehicles',
      error: error.message
    });
  }
};
