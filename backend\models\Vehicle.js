import mongoose from 'mongoose';

// Customer information schema
const customerSchema = new mongoose.Schema({
  name: { type: String, required: true },
  phone: { type: String, required: true },
  email: { type: String },
  altPhone: { type: String },
  address: {
    houseNo: { type: String },
    street: { type: String },
    city: { type: String },
    state: { type: String },
    pincode: { type: String }
  }
}, { _id: false });

// Vehicle details schema
const vehicleDetailsSchema = new mongoose.Schema({
  model: { type: String, required: false }, // Made optional for initial customer-only records
  colour: { type: String },
  financeMode: { type: String }
}, { _id: false });

// Exchange vehicle schema
const exchangeVehicleSchema = new mongoose.Schema({
  hasExchange: { type: String, enum: ['yes', 'no'], default: 'no' },
  model: { type: String },
  year: { type: String },
  kmsDriven: { type: String }
}, { _id: false });

// Registration schema
const registrationSchema = new mongoose.Schema({
  hasSpecialNumber: { type: String, enum: ['yes', 'no'], default: 'no' },
  expectedSpecialNumber: { type: String },
  type: { type: String, enum: ['permanent', 'temporary'], default: 'permanent' }
}, { _id: false });

// Insurance nominee schema
const nomineeSchema = new mongoose.Schema({
  name: { type: String },
  age: { type: String },
  relationship: { type: String }
}, { _id: false });

// Insurance schema
const insuranceSchema = new mongoose.Schema({
  type: { type: String, enum: ['bumperToBumper', 'normal', 'none'], default: 'bumperToBumper' },
  nominee: nomineeSchema
}, { _id: false });

// Document schema - S3 Cloud Storage
const documentSchema = new mongoose.Schema({
  // File metadata
  fileName: String,        // Original filename for display
  fileType: String,        // MIME type
  fileSize: Number,        // File size in bytes
  uploadDate: Date,        // Upload timestamp
  fileSelected: Boolean,   // UI state tracking

  // S3 Storage fields (production cloud storage)
  storageType: { type: String, enum: ['s3'], default: 's3' },
  s3Key: String,           // S3 object key (file path in bucket)
  s3Location: String,      // Full S3 URL
  s3ETag: String          // S3 ETag for file integrity
}, { _id: false });

const documentsSchema = new mongoose.Schema({
  aadhar: documentSchema,
  pan: documentSchema,
  photo: documentSchema,
  license: documentSchema,
  other: documentSchema
}, { _id: false });

// Main vehicle schema with clean nested structure
const vehicleSchema = new mongoose.Schema({
  // Customer information
  customer: {
    type: customerSchema,
    required: true
  },

  // Reference to the Customer model
  customerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Customer'
  },

  // Vehicle details
  vehicleDetails: {
    type: vehicleDetailsSchema,
    required: false // Allow customer-only records initially
  },

  // Exchange vehicle information
  exchangeVehicle: {
    type: exchangeVehicleSchema,
    default: () => ({ hasExchange: 'no' })
  },

  // Registration information
  registration: {
    type: registrationSchema,
    default: () => ({ hasSpecialNumber: 'no', type: 'permanent' })
  },

  // Insurance information
  insurance: {
    type: insuranceSchema,
    default: () => ({ type: 'bumperToBumper' })
  },

  // Documents
  documents: {
    type: documentsSchema,
    default: () => ({})
  },

  // Metadata
  status: {
    type: String,
    enum: ['pending', 'completed', 'cancelled'],
    default: 'pending'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  strict: true, // Enforce schema structure
  timestamps: { updatedAt: 'updatedAt' } // Auto-update updatedAt
});

// Update the updatedAt field and set customerId on save
vehicleSchema.pre('save', async function(next) {
  this.updatedAt = Date.now();

  // If we have customer data but no customerId, try to find or create a customer
  if (this.customer && this.customer._id && !this.customerId) {
    try {
      // Set the customerId to the customer._id if it exists
      this.customerId = this.customer._id;
      console.log(`Set customerId to ${this.customerId} from customer._id`);
    } catch (error) {
      console.error('Error setting customerId:', error);
      // Continue anyway - this is not critical
    }
  }

  next();
});

// Create compound indexes for advanced filtering and sorting
vehicleSchema.index({ status: 1, createdAt: -1 }); // Status + Date sorting
vehicleSchema.index({ 'vehicleDetails.model': 1, createdAt: -1 }); // Model + Date
vehicleSchema.index({ 'vehicleDetails.colour': 1, createdAt: -1 }); // Color + Date
vehicleSchema.index({ 'vehicleDetails.model': 1, 'vehicleDetails.colour': 1, createdAt: -1 }); // Model + Color + Date
vehicleSchema.index({ 'customer.name': 1, createdAt: -1 }); // Name + Date sorting
vehicleSchema.index({ 'customer.phone': 1 }); // Phone lookup
vehicleSchema.index({ 'customer.email': 1 }); // Email lookup
vehicleSchema.index({ customerId: 1 }); // Customer reference
vehicleSchema.index({ createdAt: -1 }); // Date sorting (primary)
vehicleSchema.index({ updatedAt: -1 }); // Last modified sorting

// Text search index for full-text search
vehicleSchema.index({
  'customer.name': 'text',
  'customer.phone': 'text',
  'customer.email': 'text',
  'vehicleDetails.model': 'text'
});

const Vehicle = mongoose.model('Vehicle', vehicleSchema);

export default Vehicle;
