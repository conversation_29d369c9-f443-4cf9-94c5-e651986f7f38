import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import config from '../config/config';

// Define types for the API responses
export interface CustomerResponse {
  _id: string;
  name: string;
  phone: string;
  altPhone?: string;
  email: string;
  address: {
    houseNo: string;
    street: string;
    city: string;
    state: string;
    pincode: string;
    fullAddress?: string;
  };
  vehicles: string[];
  createdAt: string;
  updatedAt: string;
}

export interface VehicleResponse {
  _id: string;
  customer: {
    _id: string;
    name: string;
  };
  customerId: string;
  vehicleDetails: {
    model: string;
    colour: string;
    financeMode: string;
  };
  exchangeVehicle?: {
    hasExchange?: string;
    model?: string;
    year?: string;
    kmsDriven?: string;
  };
  registration?: {
    hasSpecialNumber?: string;
    expectedSpecialNumber?: string;
    type?: string;
  };
  insurance?: {
    type?: string;
    nominee?: {
      name?: string;
      age?: string;
      relationship?: string;
    };
  };
  documents?: Record<string, any>;
  status: 'pending' | 'completed';
  createdAt: string;
  updatedAt: string;
}

export interface PaginatedCustomersResponse {
  success: boolean;
  data: {
    customers: CustomerResponse[];
    totalCount: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

export interface CustomerDetailResponse {
  success: boolean;
  data: CustomerResponse;
}

export interface VehicleDetailResponse {
  success: boolean;
  data: VehicleResponse;
}

// Filter options interface
export interface FilterOptions {
  models: string[];
  colors: string[];
  statuses: string[];
  cities: string[];
  states: string[];
}

export interface FilterOptionsResponse {
  success: boolean;
  data: FilterOptions;
}

// Enhanced query parameters interface
export interface CustomerQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  sortField?: string;
  sortOrder?: string;
  // Date filters
  dateFrom?: string;
  dateTo?: string;
  dateRange?: 'today' | 'lastWeek' | 'lastMonth';
  // Vehicle filters
  model?: string;
  color?: string;
}

// Create the API slice
export const customerApiSlice = createApi({
  reducerPath: 'customerApi',
  baseQuery: fetchBaseQuery({
    baseUrl: `${config.api.baseUrl}/admin`,
    timeout: config.api.timeout,
    credentials: 'include',
    prepareHeaders: (headers) => {
      const token = localStorage.getItem(config.auth.tokenKey);
      if (token) {
        headers.set('Authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  endpoints: (builder) => ({
    // Get paginated customers with advanced filtering
    getCustomers: builder.query<PaginatedCustomersResponse['data'], CustomerQueryParams>({
      query: (params) => {
        const {
          page = 1,
          limit = 16,
          search = '',
          sortField = 'createdAt',
          sortOrder = 'desc',
          dateFrom,
          dateTo,
          dateRange,
          model,
          color,
          status,
          city,
          state
        } = params;

        // Build query parameters, only include non-empty values
        const queryParams: any = {
          page,
          limit,
          search,
          sortField,
          sortOrder,
          _t: Date.now() // Cache busting
        };

        // Add optional filters only if they have values
        if (dateFrom) queryParams.dateFrom = dateFrom;
        if (dateTo) queryParams.dateTo = dateTo;
        if (dateRange) queryParams.dateRange = dateRange;
        if (model) queryParams.model = model;
        if (color) queryParams.color = color;
        if (status) queryParams.status = status;
        if (city) queryParams.city = city;
        if (state) queryParams.state = state;

        return {
          url: '/customers',
          params: queryParams,
        };
      },
      transformResponse: (response: PaginatedCustomersResponse) => {
        if (response && response.success && response.data) {
          return response.data;
        }
        throw new Error('Invalid response format from server');
      },
      // Add retry logic
      extraOptions: {
        maxRetries: 3,
      },
      // Keep data for 10 seconds to ensure fresh data for date filters
      keepUnusedDataFor: 10,
    }),

    // Get customer by ID
    getCustomerById: builder.query<CustomerResponse, string>({
      query: (id) => ({
        url: `/customers/${id}`,
        params: { _t: Date.now() },
      }),
      transformResponse: (response: CustomerDetailResponse) => {
        if (response && response.success && response.data) {
          return response.data;
        }
        throw new Error('Invalid response format from server');
      },
      // Keep data for 5 minutes
      keepUnusedDataFor: 300,
    }),

    // Get customer vehicles
    getCustomerVehicles: builder.query<VehicleResponse[], string>({
      query: (customerId) => ({
        url: `/customers/${customerId}/vehicles`,
        params: { _t: Date.now() },
      }),
      transformResponse: (response: { success: boolean; data: VehicleResponse[] }) => {
        if (response && response.success && response.data) {
          return response.data;
        }
        throw new Error('Invalid response format from server');
      },
      // Keep data for 5 minutes
      keepUnusedDataFor: 300,
    }),

    // Get vehicle by ID
    getVehicleById: builder.query<VehicleResponse, string>({
      query: (id) => ({
        url: `/vehicles/${id}`,
        params: { _t: Date.now() },
      }),
      transformResponse: (response: VehicleDetailResponse) => {
        if (response && response.success && response.data) {
          return response.data;
        }
        throw new Error('Invalid response format from server');
      },
      // Keep data for 5 minutes
      keepUnusedDataFor: 300,
    }),

    // Get filter options
    getFilterOptions: builder.query<FilterOptions, void>({
      query: () => ({
        url: '/filter-options',
        params: { _t: Date.now() },
      }),
      transformResponse: (response: FilterOptionsResponse) => {
        if (response && response.success && response.data) {
          return response.data;
        }
        throw new Error('Invalid response format from server');
      },
      // Keep data for 10 minutes (filter options don't change often)
      keepUnusedDataFor: 600,
    }),
  }),
});

// Export the auto-generated hooks
export const {
  useGetCustomersQuery,
  useGetCustomerByIdQuery,
  useGetCustomerVehiclesQuery,
  useGetVehicleByIdQuery,
  useGetFilterOptionsQuery,
} = customerApiSlice;
