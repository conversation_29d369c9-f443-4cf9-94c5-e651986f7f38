import fetch from 'node-fetch';

class RecaptchaService {
  constructor() {
    this.verifyUrl = 'https://www.google.com/recaptcha/api/siteverify';
  }

  getSecretKey() {
    return process.env.RECAPTCHA_SECRET_KEY;
  }

  async verifyToken(token, remoteip = null) {
    try {
      if (!token) {
        return {
          success: false,
          error: 'No reCAPTCHA token provided'
        };
      }

      const secretKey = this.getSecretKey();
      if (!secretKey) {
        console.error('reCAPTCHA secret key not configured');
        return {
          success: false,
          error: 'reCAPTCHA not properly configured'
        };
      }

      console.log('Starting reCAPTCHA verification:', {
        tokenLength: token.length,
        remoteip: remoteip,
        environment: process.env.NODE_ENV
      });

      const params = new URLSearchParams({
        secret: secretKey,
        response: token
      });

      if (remoteip) {
        params.append('remoteip', remoteip);
      }

      const response = await fetch(this.verifyUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: params.toString()
      });

      if (!response.ok) {
        console.error('reCAPTCHA API request failed:', response.status, response.statusText);
        return {
          success: false,
          error: 'reCAPTCHA verification service unavailable'
        };
      }

      const result = await response.json();

      console.log('reCAPTCHA verification:', {
        success: result.success,
        score: result.score,
        action: result.action,
        hostname: result.hostname,
        errorCodes: result['error-codes'] || [],
        timestamp: new Date().toISOString()
      });

      if (result.success) {
        if (result.score !== undefined) {
          console.log('reCAPTCHA v3 score:', result.score);
          if (result.score < 0.3) {
            console.warn('reCAPTCHA v3 score too low:', result.score);
            return {
              success: false,
              error: `reCAPTCHA verification failed due to low score (${result.score})`,
              score: result.score,
              action: result.action
            };
          }
        }

        return {
          success: true,
          score: result.score || null,
          action: result.action || null,
          challenge_ts: result.challenge_ts || null,
          hostname: result.hostname || null,
          version: result.score !== undefined ? 'v3' : 'v2'
        };
      } else {
        const errorCodes = result['error-codes'] || [];
        let errorMessage = 'reCAPTCHA verification failed';

        if (errorCodes.includes('missing-input-secret')) {
          errorMessage = 'reCAPTCHA secret key missing';
        } else if (errorCodes.includes('invalid-input-secret')) {
          errorMessage = 'reCAPTCHA secret key invalid';
        } else if (errorCodes.includes('missing-input-response')) {
          errorMessage = 'reCAPTCHA token missing';
        } else if (errorCodes.includes('invalid-input-response')) {
          errorMessage = 'reCAPTCHA token invalid or expired';
        } else if (errorCodes.includes('bad-request')) {
          errorMessage = 'reCAPTCHA request malformed';
        } else if (errorCodes.includes('timeout-or-duplicate')) {
          errorMessage = 'reCAPTCHA token expired or already used';
        }

        return {
          success: false,
          error: errorMessage,
          errorCodes
        };
      }

    } catch (error) {
      console.error('reCAPTCHA verification error:', error.message);
      return {
        success: false,
        error: 'reCAPTCHA verification service error'
      };
    }
  }

  isConfigured() {
    const secretKey = this.getSecretKey();
    const siteKey = process.env.RECAPTCHA_SITE_KEY;
    console.log('reCAPTCHA configuration check:', {
      secretKey: secretKey ? 'SET' : 'NOT SET',
      siteKey: siteKey ? 'SET' : 'NOT SET'
    });
    return !!(secretKey && siteKey);
  }
}

const recaptchaService = new RecaptchaService();
export default recaptchaService;
