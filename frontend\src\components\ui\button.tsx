import React from 'react';

interface ButtonProps {
  variant: 'next' | 'back';
  onClick: () => void;
  children: React.ReactNode;
  disabled?: boolean;
}

export const Button = ({ variant, onClick, children, disabled = false }: ButtonProps) => {
  const baseStyles = "w-full h-[50px] sm:h-[60px] md:h-[70px] rounded-[12px] sm:rounded-[16px] md:rounded-[20px] font-light flex items-center justify-center text-white text-[18px] sm:text-[22px] md:text-[28px] transition-all duration-300 ease-in-out hover:opacity-90 focus:ring-2 focus:ring-[#640064] focus:ring-opacity-50 outline-none";
  const variantStyles = {
    next: "bg-[#640064] shadow-lg hover:bg-[#7a007a]",
    back: "bg-transparent border-2 border-[#640064] hover:bg-[#640064]/10"
  };

  return (
    <button
      className={`${baseStyles} ${variantStyles[variant]} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </button>
  );
};
