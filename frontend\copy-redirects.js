import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Source and destination paths
const redirectsSource = path.join(__dirname, 'public', '_redirects');
const redirectsDest = path.join(__dirname, 'dist', '_redirects');

// Copy _redirects file
if (fs.existsSync(redirectsSource)) {
  fs.copyFileSync(redirectsSource, redirectsDest);
  // _redirects file copied successfully
} else {
  // _redirects file not found - continuing without it
}

// Create index.html copies for SPA routing
const indexPath = path.join(__dirname, 'dist', 'index.html');
if (fs.existsSync(indexPath)) {
  const content = fs.readFileSync(indexPath);
  fs.writeFileSync(path.join(__dirname, 'dist', '200.html'), content);
  fs.writeFileSync(path.join(__dirname, 'dist', '404.html'), content);
  // SPA routing files created successfully
} else {
  // Warning: index.html not found in dist directory
}

// File copying complete
