# Simple CSV Export System

## Overview

This is a simplified CSV export system that automatically exports completed vehicle registrations to a single monthly CSV file for admin auditing purposes.

## Features

- **Single Monthly File**: All completed vehicles are exported to `vehicle-registrations-YYYY-MM.csv`
- **Real-time Export**: Vehicles are automatically added to CSV when status becomes "completed"
- **Simple Structure**: No complex batching, queuing, or multiple file types
- **Admin Friendly**: CSV files can be opened directly in Excel/Tally for auditing

## File Structure

```
backend/csv-exports/
└── vehicle-registrations-2025-05.csv  # Current month's file
```

## CSV Headers

The CSV file contains the following columns:
- Timestamp, Vehicle ID, Status
- Customer Name, Phone, Email, Alt Phone
- House No, Street, City, State, Pincode
- Vehicle Model, Vehicle Colour, Finance Mode
- Has Special Number, Expected Special Number, Registration Type
- Has Exchange, Exchange Model, Exchange Year, Exchange KMs Driven
- Insurance Type, Nominee Name, Nominee Age, Nominee Relationship
- Documents Uploaded, Documents List
- Created At, Updated At, Completed At, Processing Time (seconds)

## API Endpoints

### Export Single Vehicle
```
POST /api/csv/export/:vehicleId
```

### Bulk Export
```
POST /api/csv/bulk-export?startDate=2025-01-01&endDate=2025-01-31&limit=1000
```

### List CSV Files
```
GET /api/csv/files
```

### Download CSV File
```
GET /api/csv/download/:filename
```

### Preview CSV File
```
GET /api/csv/preview/:filename?rows=100
```

### Get Statistics
```
GET /api/csv/stats
```

## Configuration

The system uses a simple configuration in `config/csvConfig.js`:

```javascript
export const csvConfig = {
  enabled: process.env.CSV_ENABLED !== 'false',
  files: {
    baseDir: 'csv-exports',
    monthlyPattern: 'vehicle-registrations-{year}-{month}.csv'
  },
  headers: [/* CSV column headers */]
};
```

## How It Works

1. **Automatic Export**: When a vehicle status changes to "completed", the CSV middleware automatically captures the data
2. **Monthly Files**: Each month gets its own CSV file (e.g., `vehicle-registrations-2025-05.csv`)
3. **Simple Append**: New records are simply appended to the current month's file
4. **No Batching**: No complex queuing or batch processing - data is written immediately

## Usage

The system works automatically. When vehicles are marked as completed through the normal application flow, they are automatically exported to the CSV file.

Admins can:
- Download monthly CSV files via the API
- Open CSV files directly in Excel/Tally for auditing
- Preview CSV content through the web interface

## Maintenance

- CSV files are created automatically each month
- Old files remain in the directory for historical access
- No complex cleanup or archiving needed
- Files can be manually deleted if needed via the API

## Dependencies

- No external CSV libraries required
- Uses native Node.js `fs` module for file operations
- Simple and lightweight implementation
