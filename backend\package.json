{"name": "q5xt1-backend", "version": "1.0.0", "description": "Backend server for Q5XT1 vehicle registration application", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1", "cleanup": "node scripts/cleanup.js", "verify-atlas": "node scripts/verify-atlas-connection.js", "create-admin": "node scripts/createAdminUser.js", "test-s3": "node scripts/testS3Connection.js", "build": "npm ci --omit=dev", "start:prod": "NODE_ENV=production node server.js"}, "dependencies": {"@aws-sdk/client-s3": "^3.821.0", "@aws-sdk/s3-request-presigner": "^3.821.0", "bcryptjs": "^3.0.2", "compression": "^1.8.0", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "form-data": "^4.0.2", "helmet": "^7.1.0", "hpp": "^0.2.3", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-fetch": "^3.3.2", "uuid": "^9.0.1", "winston": "^3.17.0", "winston-daily-rotate-file": "^4.7.1", "xss-clean": "^0.1.4"}, "devDependencies": {"nodemon": "^3.0.2"}}