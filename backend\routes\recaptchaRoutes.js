import express from 'express';
import recaptchaService from '../services/recaptchaService.js';

const router = express.Router();

router.post('/verify-recaptcha', async (req, res) => {
  try {
    console.log('reCAPTCHA verification request received:', {
      body: req.body,
      hasToken: !!req.body?.recaptchaToken,
      tokenLength: req.body?.recaptchaToken?.length || 0
    });

    const { recaptchaToken } = req.body;

    if (!recaptchaService.isConfigured()) {
      console.error('reCAPTCHA service not configured');
      return res.status(500).json({
        success: false,
        message: 'reCAPTCHA service not properly configured'
      });
    }

    if (!recaptchaToken) {
      return res.status(400).json({
        success: false,
        message: 'reCAPTCHA token is required'
      });
    }

    const userIP = req.ip || req.connection.remoteAddress || req.socket.remoteAddress;
    const verificationResult = await recaptchaService.verifyToken(recaptchaToken, userIP);

    if (verificationResult.success) {
      console.log('reCAPTCHA verification successful:', {
        ip: userIP,
        userAgent: req.get('User-Agent'),
        timestamp: new Date().toISOString()
      });

      return res.status(200).json({
        success: true,
        message: 'reCAPTCHA verification successful'
      });
    } else {
      console.warn('reCAPTCHA verification failed:', {
        error: verificationResult.error,
        errorCodes: verificationResult.errorCodes,
        ip: userIP,
        userAgent: req.get('User-Agent'),
        timestamp: new Date().toISOString()
      });

      return res.status(400).json({
        success: false,
        message: verificationResult.error || 'reCAPTCHA verification failed'
      });
    }

  } catch (error) {
    console.error('reCAPTCHA verification endpoint error:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });
    return res.status(500).json({
      success: false,
      message: 'Internal server error during reCAPTCHA verification'
    });
  }
});

router.get('/recaptcha-status', (req, res) => {
  try {
    const isConfigured = recaptchaService.isConfigured();

    res.status(200).json({
      success: true,
      configured: isConfigured,
      siteKey: process.env.RECAPTCHA_SITE_KEY ? 'configured' : 'missing',
      secretKey: process.env.RECAPTCHA_SECRET_KEY ? 'configured' : 'missing'
    });
  } catch (error) {
    console.error('reCAPTCHA status check error:', error);
    res.status(500).json({
      success: false,
      message: 'Unable to check reCAPTCHA status'
    });
  }
});

export default router;
