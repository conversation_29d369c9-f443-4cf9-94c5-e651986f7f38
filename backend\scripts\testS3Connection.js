/**
 * S3 Connection Test Script
 * Tests S3 configuration and connectivity
 */

import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import s3Service from '../services/s3Service.js';
import logger from '../utils/logger.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
const envFile = process.env.NODE_ENV === 'production' ? '.env.production' : '.env';
dotenv.config({ path: join(dirname(__dirname), envFile) });

/**
 * Test S3 connection and configuration
 */
const testS3Connection = async () => {
  try {
    logger.info('=== S3 Connection Test ===');

    // Check environment variables
    const requiredVars = [
      'AWS_ACCESS_KEY_ID',
      'AWS_SECRET_ACCESS_KEY',
      'AWS_REGION',
      'AWS_S3_BUCKET_NAME'
    ];

    logger.info('Checking environment variables...');
    const missingVars = requiredVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length > 0) {
      logger.error(`Missing environment variables: ${missingVars.join(', ')}`);
      return false;
    }

    logger.info('✓ All required environment variables are set');

    // Initialize S3 service
    logger.info('Initializing S3 service...');
    const initialized = await s3Service.initialize();
    
    if (!initialized) {
      logger.error('✗ Failed to initialize S3 service');
      return false;
    }

    logger.info('✓ S3 service initialized successfully');

    // Test file upload
    logger.info('Testing file upload...');
    const testContent = Buffer.from('This is a test file for S3 connectivity', 'utf8');
    
    try {
      const uploadResult = await s3Service.uploadFile(
        testContent,
        'test-phone',
        'test-document',
        'test-file.txt',
        'text/plain'
      );

      logger.info('✓ Test file uploaded successfully');
      logger.info(`  S3 Key: ${uploadResult.s3Key}`);
      logger.info(`  Location: ${uploadResult.location}`);

      // Test file existence
      logger.info('Testing file existence check...');
      const exists = await s3Service.fileExists(uploadResult.s3Key);
      
      if (exists) {
        logger.info('✓ File existence check passed');
      } else {
        logger.error('✗ File existence check failed');
        return false;
      }

      // Test presigned URL generation
      logger.info('Testing presigned URL generation...');
      const presignedUrl = await s3Service.getPresignedUrl(uploadResult.s3Key, 300);
      
      if (presignedUrl) {
        logger.info('✓ Presigned URL generated successfully');
        logger.info(`  URL: ${presignedUrl.substring(0, 100)}...`);
      } else {
        logger.error('✗ Failed to generate presigned URL');
        return false;
      }

      // Test file metadata
      logger.info('Testing file metadata retrieval...');
      const metadata = await s3Service.getFileMetadata(uploadResult.s3Key);
      
      if (metadata) {
        logger.info('✓ File metadata retrieved successfully');
        logger.info(`  Content Type: ${metadata.contentType}`);
        logger.info(`  Content Length: ${metadata.contentLength}`);
      } else {
        logger.error('✗ Failed to retrieve file metadata');
        return false;
      }

      // Clean up test file
      logger.info('Cleaning up test file...');
      await s3Service.deleteFile(uploadResult.s3Key);
      logger.info('✓ Test file deleted successfully');

      logger.info('=== S3 Connection Test PASSED ===');
      return true;

    } catch (uploadError) {
      logger.error('✗ File upload test failed:', uploadError.message);
      return false;
    }

  } catch (error) {
    logger.error('S3 connection test failed:', error);
    return false;
  }
};

/**
 * Print configuration summary
 */
const printConfig = () => {
  logger.info('=== S3 Configuration ===');
  logger.info(`Region: ${process.env.AWS_REGION || 'Not set'}`);
  logger.info(`Bucket: ${process.env.AWS_S3_BUCKET_NAME || 'Not set'}`);
  logger.info(`Access Key: ${process.env.AWS_ACCESS_KEY_ID ? process.env.AWS_ACCESS_KEY_ID.substring(0, 8) + '...' : 'Not set'}`);
  logger.info(`S3 Storage Enabled: ${process.env.USE_S3_STORAGE || 'false'}`);
  logger.info('========================');
};

// Run test
const runTest = async () => {
  printConfig();
  
  const success = await testS3Connection();
  
  if (success) {
    logger.info('S3 is ready for use!');
    process.exit(0);
  } else {
    logger.error('S3 configuration needs attention');
    process.exit(1);
  }
};

runTest();
