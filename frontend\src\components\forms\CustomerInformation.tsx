import { Input } from "../ui/input";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../store/store";
import { updateFormField, setFieldTouched, validateSection } from "../../store/formSlice";
import { validationRules } from "../../utils/validation";
import { useEffect, useState } from "react";
import DuplicateCheck from "../ui/duplicate-check";

export const CustomerInformation = () => {
  const dispatch = useDispatch();
  const { formData, errors, touchedFields } = useSelector((state: RootState) => state.form);
  const { duplicateCheck, vehicleId } = useSelector((state: RootState) => state.api);
  const [hasDuplicate, setHasDuplicate] = useState(false);
  const [phoneDuplicateError, setPhoneDuplicateError] = useState<string | null>(null);
  const [emailDuplicateError, setEmailDuplicateError] = useState<string | null>(null);

  // Initialize component and clear errors on mount
  useEffect(() => {
    // Clear any existing errors for this section
    dispatch({ type: 'form/clearSectionErrors', payload: 'customer' });

    // Check if all required fields are filled
    const requiredFields = ['name', 'phone', 'email', 'houseNo', 'street', 'city', 'state', 'pincode'];
    const allFieldsFilled = requiredFields.every(field =>
      formData[field] && formData[field].toString().trim() !== ''
    );

    // If all fields are filled, validate the section
    if (allFieldsFilled) {
      setTimeout(() => {
        dispatch(validateSection("customer"));
      }, 0);
    }
  }, [dispatch, formData]);

  // No automatic validation on load - only validate on field blur and form navigation
  // This ensures validation errors only appear after user interaction

  // Base input styles - consistent style for all inputs
  const inputStyles = "h-[45px] sm:h-[50px] md:h-[60px] w-full px-3 sm:px-4 rounded-lg border-[1px] border-[#333333] bg-[#0e0e0e] text-[#e3e3e3] text-sm sm:text-base placeholder:text-[#818181] focus:border-[#640064] focus:ring-1 focus:ring-[#640064] transition-colors";

  // Select styles for dropdowns
  const selectStyles = `${inputStyles} appearance-none cursor-pointer custom-select`;

  // Function to check if a field is required
  const isFieldRequired = (fieldName: string) => {
    const rules = validationRules.customer[fieldName as keyof typeof validationRules.customer];
    return rules && rules.required;
  };

  // Required field marker component
  const RequiredMarker = () => (
    <span className="text-red-500 ml-1">*</span>
  );

  // List of Indian states for dropdown
  const indianStates = [
    "Andhra Pradesh", "Arunachal Pradesh", "Assam", "Bihar", "Chhattisgarh", "Goa", "Gujarat",
    "Haryana", "Himachal Pradesh", "Jharkhand", "Karnataka", "Kerala", "Madhya Pradesh",
    "Maharashtra", "Manipur", "Meghalaya", "Mizoram", "Nagaland", "Odisha", "Punjab",
    "Rajasthan", "Sikkim", "Tamil Nadu", "Telangana", "Tripura", "Uttar Pradesh",
    "Uttarakhand", "West Bengal", "Andaman and Nicobar Islands", "Chandigarh",
    "Dadra and Nagar Haveli and Daman and Diu", "Delhi", "Jammu and Kashmir",
    "Ladakh", "Lakshadweep", "Puducherry"
  ];

  // Helper function to mark a field as touched
  const markFieldAsTouched = (name: string) => {
    dispatch(setFieldTouched(name));
  };

  // Check if a field should show validation error
  const shouldShowError = (fieldName: string) => {
    return touchedFields && touchedFields[fieldName] && errors && errors[fieldName];
  };

  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    dispatch(updateFormField({ field: name, value }));

    // Clear any existing errors for this field if it has a value
    if (value && value.trim() !== '' && errors && errors[name]) {
      dispatch({
        type: 'form/clearFieldError',
        payload: name
      });
    }

    // Check if all required fields are filled
    const requiredFields = ['name', 'phone', 'email', 'houseNo', 'street', 'city', 'state', 'pincode'];
    const updatedFormData = { ...formData, [name]: value };
    const allFieldsFilled = requiredFields.every(field =>
      (field === name ? value : updatedFormData[field]) &&
      (field === name ? value : updatedFormData[field]).toString().trim() !== ''
    );

    // If all fields are filled, clear all section errors
    if (allFieldsFilled) {
      dispatch({ type: 'form/clearSectionErrors', payload: 'customer' });
    }

    // Don't mark as touched on change to prevent showing errors while typing
  };

  // Handle blur event to mark field as touched and validate
  const handleBlur = (e: React.FocusEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name } = e.target;
    markFieldAsTouched(name);

    // Validate the section after field blur
    setTimeout(() => {
      dispatch(validateSection("customer"));
    }, 0);
  };

  // Handle phone number input - only allow 10 digits
  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    // Remove any non-digit characters
    const digitsOnly = value.replace(/\D/g, '');
    // Limit to 10 digits
    const limitedValue = digitsOnly.slice(0, 10);
    dispatch(updateFormField({ field: name, value: limitedValue }));

    // Clear any existing errors for this field if it has a valid value (10 digits)
    if (limitedValue.length === 10 && errors && errors[name]) {
      dispatch({
        type: 'form/clearFieldError',
        payload: name
      });
    }

    // Check if all required fields are filled
    if (name === 'phone' && limitedValue.length === 10) {
      const requiredFields = ['name', 'phone', 'email', 'houseNo', 'street', 'city', 'state', 'pincode'];
      const updatedFormData = { ...formData, [name]: limitedValue };
      const allFieldsFilled = requiredFields.every(field =>
        (field === name ? limitedValue : updatedFormData[field]) &&
        (field === name ? limitedValue : updatedFormData[field]).toString().trim() !== ''
      );

      // If all fields are filled, clear all section errors
      if (allFieldsFilled) {
        dispatch({ type: 'form/clearSectionErrors', payload: 'customer' });
      }
    }

    // Don't mark as touched on change to prevent showing errors while typing
  };

  // Handle pincode input - format as 000 000 and limit to 6 digits
  const handlePincodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    // Remove any non-digit characters
    const digitsOnly = value.replace(/\D/g, '');
    // Limit to 6 digits
    const limitedValue = digitsOnly.slice(0, 6);

    // Format as 000 000 if we have more than 3 digits
    let formattedValue = limitedValue;
    if (limitedValue.length > 3) {
      formattedValue = `${limitedValue.slice(0, 3)} ${limitedValue.slice(3)}`;
    }

    dispatch(updateFormField({ field: name, value: formattedValue }));

    // Clear any existing errors for this field if it has a valid value (6 digits)
    if (limitedValue.length === 6 && errors && errors[name]) {
      dispatch({
        type: 'form/clearFieldError',
        payload: name
      });
    }

    // Check if all required fields are filled
    if (limitedValue.length === 6) {
      const requiredFields = ['name', 'phone', 'email', 'houseNo', 'street', 'city', 'state', 'pincode'];
      const updatedFormData = { ...formData, [name]: formattedValue };
      const allFieldsFilled = requiredFields.every(field =>
        (field === name ? formattedValue : updatedFormData[field]) &&
        (field === name ? formattedValue : updatedFormData[field]).toString().trim() !== ''
      );

      // If all fields are filled, clear all section errors
      if (allFieldsFilled) {
        dispatch({ type: 'form/clearSectionErrors', payload: 'customer' });
      }
    }

    // Don't mark as touched on change to prevent showing errors while typing
  };

  return (
    <div className="space-y-4 sm:space-y-5 md:space-y-6">
      {/* Name */}
      <div className="space-y-1 sm:space-y-1.5">
        <label className="text-[#e3e3e3] text-lg sm:text-xl md:text-2xl font-light">
          Name
          {isFieldRequired("name") && <RequiredMarker />}
        </label>
        <Input
          type="text"
          className={`${inputStyles} ${shouldShowError("name") ? "border-red-500" : ""}`}
          placeholder="Enter your Name"
          name="name"
          value={formData.name}
          onChange={handleChange}
          onBlur={handleBlur}
        />
      </div>

      {/* Phone and Alt Phone */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
        <div className="space-y-1 sm:space-y-1.5">
          <label className="text-[#e3e3e3] text-lg sm:text-xl md:text-2xl font-light">
            Phone Number
            {isFieldRequired("phone") && <RequiredMarker />}
          </label>
          <Input
  type="tel"
  className={`${inputStyles} ${(shouldShowError("phone") || phoneDuplicateError) ? "border-red-500" : "border-[#333333]"}`}
  placeholder={phoneDuplicateError ? "Phone number already exists" : "Enter your Phone Number (10 digits)"}
  name="phone"
  value={phoneDuplicateError && !formData.phone ? "" : formData.phone}
  onChange={e => {
    handlePhoneChange(e);
    if (phoneDuplicateError) setPhoneDuplicateError(null);
  }}
  onBlur={handleBlur}
  maxLength={10}
  inputMode="numeric"
/>
        </div>
        <div className="space-y-1 sm:space-y-1.5">
          <label className="text-[#e3e3e3] text-lg sm:text-xl md:text-2xl font-light">
            Alt Phone Number
          </label>
          <Input
            type="tel"
            className={`${inputStyles} ${shouldShowError("altPhone") ? "border-red-500" : ""}`}
            placeholder="Enter Alternative Phone Number (10 digits)"
            name="altPhone"
            value={formData.altPhone}
            onChange={handlePhoneChange}
            onBlur={handleBlur}
            maxLength={10}
            inputMode="numeric"
          />
        </div>
      </div>

      {/* Email */}
      <div className="space-y-1 sm:space-y-1.5">
        <label className="text-[#e3e3e3] text-lg sm:text-xl md:text-2xl font-light">
          Email
          {isFieldRequired("email") && <RequiredMarker />}
        </label>
        <Input
  type="email"
  className={`${inputStyles} ${(shouldShowError("email") || emailDuplicateError) ? "border-red-500" : "border-[#333333]"}`}
  placeholder={emailDuplicateError ? "Email already exists" : "Enter your Email"}
  name="email"
  value={emailDuplicateError && !formData.email ? "" : formData.email}
  onChange={e => {
    handleChange(e);
    if (emailDuplicateError) setEmailDuplicateError(null);
  }}
  onBlur={handleBlur}
/>
      </div>

      {/* Address */}
      <div className="space-y-2 sm:space-y-3 md:space-y-4">
        <label className="text-[#e3e3e3] text-lg sm:text-xl md:text-2xl font-light">Address</label>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
          <div className="space-y-1 sm:space-y-1.5">
            <label className="text-[#e3e3e3] text-sm sm:text-base font-light">
              House No
              {isFieldRequired("houseNo") && <RequiredMarker />}
            </label>
            <Input
              type="text"
              className={`${inputStyles} ${shouldShowError("houseNo") ? "border-red-500" : ""}`}
              placeholder="Enter your House No"
              name="houseNo"
              value={formData.houseNo}
              onChange={handleChange}
              onBlur={handleBlur}
            />
          </div>
          <div className="space-y-1 sm:space-y-1.5">
            <label className="text-[#e3e3e3] text-sm sm:text-base font-light">
              Street
              {isFieldRequired("street") && <RequiredMarker />}
            </label>
            <Input
              type="text"
              className={`${inputStyles} ${shouldShowError("street") ? "border-red-500" : ""}`}
              placeholder="Enter your Street"
              name="street"
              value={formData.street}
              onChange={handleChange}
              onBlur={handleBlur}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 sm:gap-6">
          <div className="space-y-1 sm:space-y-1.5">
            <label className="text-[#e3e3e3] text-sm sm:text-base font-light">
              City
              {isFieldRequired("city") && <RequiredMarker />}
            </label>
            <Input
              type="text"
              className={`${inputStyles} ${shouldShowError("city") ? "border-red-500" : ""}`}
              placeholder="Enter your City"
              name="city"
              value={formData.city}
              onChange={handleChange}
              onBlur={handleBlur}
            />
          </div>
          <div className="space-y-1 sm:space-y-1.5">
            <label className="text-[#e3e3e3] text-sm sm:text-base font-light">
              State
              {isFieldRequired("state") && <RequiredMarker />}
            </label>
            <select
              className={`${selectStyles} ${shouldShowError("state") ? "border-red-500" : ""}`}
              name="state"
              value={formData.state}
              onChange={handleChange}
              onBlur={handleBlur}
            >
              <option value="" disabled>Select your State</option>
              {indianStates.map((state) => (
                <option key={state} value={state}>{state}</option>
              ))}
            </select>
          </div>
          <div className="sm:col-span-2 md:col-span-1 space-y-1 sm:space-y-1.5">
            <label className="text-[#e3e3e3] text-sm sm:text-base font-light">
              Pincode
              {isFieldRequired("pincode") && <RequiredMarker />}
            </label>
            <Input
              type="text"
              className={`${inputStyles} ${shouldShowError("pincode") ? "border-red-500" : ""}`}
              placeholder="Enter your Pincode (000 000)"
              name="pincode"
              value={formData.pincode}
              onChange={handlePincodeChange}
              onBlur={handleBlur}
              maxLength={7} // 6 digits + 1 space
              inputMode="numeric"
            />
          </div>
        </div>
      </div>

      {/* Duplicate Check Component */}
      {(formData.phone || formData.email) && (
        <div className="mt-6">
          <DuplicateCheck
            phone={formData.phone}
            email={formData.email}
            vehicleId={vehicleId}
            onDuplicateFound={(customer) => {
              setHasDuplicate(true);
              // Set error for phone/email if they match
              if (customer.phone === formData.phone) {
                setPhoneDuplicateError("Phone number already exists");
              } else {
                setPhoneDuplicateError(null);
              }
              if (customer.email === formData.email) {
                setEmailDuplicateError("Email already exists");
              } else {
                setEmailDuplicateError(null);
              }
            }}
            onNoDuplicate={() => {
              setHasDuplicate(false);
              setPhoneDuplicateError(null);
              setEmailDuplicateError(null);
            }}
            // Hide green notification if any duplicate error exists
            showSuccess={!phoneDuplicateError && !emailDuplicateError}
          />
        </div>
      )}
    </div>
  );
};
