/* Custom select styling */
.custom-select {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23e3e3e3' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 1.5em;
}

.custom-select option {
  background-color: #0e0e0e;
  color: #e3e3e3;
  padding: 8px;
}

.custom-select:focus {
  outline: none;
  border-color: #C524C5;
}

/* Text animation for long labels */
@keyframes marquee {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-100%);
  }
}

.animated-text {
  white-space: nowrap;
  position: relative;
  display: inline-block;
}

.animated-text.long-text {
  animation: marquee 10s linear infinite;
  animation-delay: 2s;
  padding-right: 50px;
}

.label-container {
  overflow: hidden;
  width: 100%;
  position: relative;
}

/* Add a gradient fade effect at the edges */
.label-container::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  width: 20px;
  background: linear-gradient(to right, transparent, #0e0e0e);
  z-index: 1;
}

/* File upload box styles for consistent alignment */
.file-upload-box {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}

/* File upload animation */
@keyframes fadeIn {
  0% { opacity: 0; transform: scale(0.9); }
  100% { opacity: 1; transform: scale(1); }
}

.file-upload-success-animation {
  animation: fadeIn 0.3s ease-in-out;
}

/* Ensure file upload boxes maintain consistent height */
@media (max-width: 640px) {
  /* Mobile view */
  .file-upload-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 12px;
  }

  /* Ensure full width for all grid items */
  .file-upload-grid-top > div,
  .file-upload-grid-bottom > div {
    width: 100%;
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  /* Tablet view */
  .file-upload-grid-top {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .file-upload-grid-bottom {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  /* Ensure equal width for bottom grid items */
  .file-upload-grid-bottom > div {
    width: 100%;
  }
}

@media (min-width: 769px) {
  /* Desktop view */
  .file-upload-grid-top {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
  }

  .file-upload-grid-bottom {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
  }

  /* Ensure equal width for bottom grid items */
  .file-upload-grid-bottom > div {
    width: 100%;
  }
}

.animate-shake {
  animation: shake 0.3s linear;
}
@keyframes shake {
  0% { transform: translateX(0); }
  20% { transform: translateX(-4px); }
  40% { transform: translateX(4px); }
  60% { transform: translateX(-4px); }
  80% { transform: translateX(4px); }
  100% { transform: translateX(0); }
}

/* Fix for grid layout to maintain consistent sizes */
.file-upload-grid-top,
.file-upload-grid-bottom {
  display: grid;
  width: 100%;
}

.file-upload-grid-top > div,
.file-upload-grid-bottom > div {
  min-width: 0; /* Prevents flex items from overflowing */
}