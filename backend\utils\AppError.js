/**
 * Custom error class for application errors
 * Allows setting status code and operational status
 */
class AppError extends Error {
  constructor(message, statusCode) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = true; // Indicates this is a known operational error

    // Capture stack trace
    Error.captureStackTrace(this, this.constructor);
  }
}

export default AppError;
