import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import logger from '../utils/logger.js';
import csvConfig from '../config/csvConfig.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Simple Monthly CSV Storage Service
 * Features:
 * - Single monthly CSV file for admin auditing
 * - Real-time updates when vehicles are completed
 * - Simple file writing without complex batching
 * - Automatic monthly archiving
 *
 * File Structure:
 * - Current: vehicle-registrations-YYYY-MM.csv
 * - Archive: archive/vehicle-registrations-YYYY-MM.csv
 *
 * IMPORTANT: Only exports vehicles with status "completed"
 */
class CSVStorageService {
  constructor() {
    this.csvDir = path.join(path.dirname(__dirname), 'csv-exports');
    this.currentMonth = null;
    this.currentFilePath = null;
    this.currentFilename = null;

    // Initialize CSV storage
    this.initializeCSVStorage();
  }

  /**
   * Initialize CSV storage directories and files
   */
  initializeCSVStorage() {
    try {
      // Create directory structure
      if (!fs.existsSync(this.csvDir)) {
        fs.mkdirSync(this.csvDir, { recursive: true });
      }

      // Initialize current month CSV file
      this.initializeMonthlyCSVFile();
    } catch (error) {
      logger.error('Failed to initialize CSV Storage Service:', error);
      throw error;
    }
  }

  /**
   * Initialize monthly CSV file with headers
   */
  initializeMonthlyCSVFile() {
    const currentDate = new Date();
    const year = currentDate.getFullYear();
    const month = String(currentDate.getMonth() + 1).padStart(2, '0');

    this.currentMonth = `${year}-${month}`;
    this.currentFilename = `vehicle-registrations-${year}-${month}.csv`;
    this.currentFilePath = path.join(this.csvDir, this.currentFilename);

    // Create file with headers if it doesn't exist
    if (!fs.existsSync(this.currentFilePath)) {
      const headerRow = csvConfig.headers.join(',') + '\n';
      fs.writeFileSync(this.currentFilePath, headerRow);
    }
  }

  /**
   * Add vehicle data to CSV file
   */
  async addVehicleData(vehicleData) {
    try {
      // Check if we need to create a new monthly file
      this.checkMonthlyFile();

      // Transform vehicle data to CSV row
      const csvRow = this.transformVehicleData(vehicleData);

      // Append to CSV file
      await this.appendToCSV(csvRow);
    } catch (error) {
      logger.error('Error adding vehicle data to CSV:', error);
      throw error;
    }
  }

  /**
   * Check if we need to create a new monthly file
   */
  checkMonthlyFile() {
    const currentDate = new Date();
    const year = currentDate.getFullYear();
    const month = String(currentDate.getMonth() + 1).padStart(2, '0');
    const monthKey = `${year}-${month}`;

    if (this.currentMonth !== monthKey) {
      this.initializeMonthlyCSVFile();
    }
  }

  /**
   * Append CSV row to file
   */
  async appendToCSV(csvRow) {
    try {
      fs.appendFileSync(this.currentFilePath, csvRow + '\n');
    } catch (error) {
      logger.error('Error appending to CSV file:', error);
      throw error;
    }
  }

  /**
   * Transform vehicle data to CSV row
   */
  transformVehicleData(vehicleData) {
    // Format address
    const formatAddress = (customer) => {
      if (!customer?.address) return '';
      const addr = customer.address;
      return `${addr.houseNo || ''} ${addr.street || ''} ${addr.city || ''} ${addr.state || ''} ${addr.pincode || ''}`.trim();
    };

    // Get documents count and list
    const getDocumentsInfo = (documents) => {
      if (!documents) return { count: 0, list: '' };
      const uploaded = Object.keys(documents).filter(key => documents[key] && documents[key].hasFilePath);
      return {
        count: uploaded.length,
        list: uploaded.join(', ')
      };
    };

    const docsInfo = getDocumentsInfo(vehicleData.documents);
    const processingTime = vehicleData.completedAt && vehicleData.createdAt
      ? Math.round((new Date(vehicleData.completedAt) - new Date(vehicleData.createdAt)) / 1000)
      : '';

    // Create CSV row data array matching the headers
    const rowData = [
      new Date().toISOString(), // Timestamp
      vehicleData._id || '', // Vehicle ID
      vehicleData.status || '', // Status
      vehicleData.customer?.name || '', // Customer Name
      vehicleData.customer?.phone || '', // Phone
      vehicleData.customer?.email || '', // Email
      vehicleData.customer?.altPhone || '', // Alt Phone
      vehicleData.customer?.address?.houseNo || '', // House No
      vehicleData.customer?.address?.street || '', // Street
      vehicleData.customer?.address?.city || '', // City
      vehicleData.customer?.address?.state || '', // State
      vehicleData.customer?.address?.pincode || '', // Pincode
      vehicleData.vehicleDetails?.model || '', // Vehicle Model
      vehicleData.vehicleDetails?.colour || '', // Vehicle Colour
      vehicleData.vehicleDetails?.financeMode || '', // Finance Mode
      vehicleData.registration?.hasSpecialNumber || 'no', // Has Special Number
      vehicleData.registration?.expectedSpecialNumber || '', // Expected Special Number
      vehicleData.registration?.type || 'permanent', // Registration Type
      vehicleData.exchangeVehicle?.hasExchange || 'no', // Has Exchange
      vehicleData.exchangeVehicle?.model || '', // Exchange Model
      vehicleData.exchangeVehicle?.year || '', // Exchange Year
      vehicleData.exchangeVehicle?.kmsDriven || '', // Exchange KMs Driven
      vehicleData.insurance?.type || '', // Insurance Type
      vehicleData.insurance?.nominee?.name || '', // Nominee Name
      vehicleData.insurance?.nominee?.age || '', // Nominee Age
      vehicleData.insurance?.nominee?.relationship || '', // Nominee Relationship
      docsInfo.count, // Documents Uploaded
      docsInfo.list, // Documents List
      vehicleData.createdAt ? new Date(vehicleData.createdAt).toISOString() : '', // Created At
      vehicleData.updatedAt ? new Date(vehicleData.updatedAt).toISOString() : '', // Updated At
      vehicleData.completedAt ? new Date(vehicleData.completedAt).toISOString() : '', // Completed At
      processingTime // Processing Time (seconds)
    ];

    // Escape CSV values that contain commas, quotes, or newlines
    return rowData.map(value => {
      const stringValue = String(value || '');
      if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
        return `"${stringValue.replace(/"/g, '""')}"`;
      }
      return stringValue;
    }).join(',');
  }

  /**
   * Get basic statistics
   */
  getStatistics() {
    return {
      currentFile: this.currentFilename,
      currentMonth: this.currentMonth,
      csvDirectory: this.csvDir
    };
  }
}

export default CSVStorageService;
