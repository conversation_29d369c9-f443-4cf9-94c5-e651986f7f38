import express from 'express';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';
import dotenv from 'dotenv';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables based on NODE_ENV
const envFile = process.env.NODE_ENV === 'production' ? '.env.production' : '.env';
dotenv.config({ path: path.join(__dirname, envFile) });

const app = express();
const PORT = process.env.PORT || 10000;

// Check if dist directory exists
const distDir = path.join(__dirname, 'dist');
if (!fs.existsSync(distDir)) {
  console.error('Error: dist directory not found. Please run npm run build first.');
  process.exit(1);
}

// Set cache control headers
app.use((req, res, next) => {
  // No cache for HTML files
  if (req.path.endsWith('.html')) {
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
  } else if (req.path.includes('/assets/')) {
    // Long cache for assets (1 year)
    res.setHeader('Cache-Control', 'public, max-age=********');
  } else {
    // Default cache (1 day)
    res.setHeader('Cache-Control', 'public, max-age=86400');
  }
  next();
});

// Serve static files from the dist directory
app.use(express.static(distDir));

// Serve index.html for all routes (for client-side routing)
app.get('*', (req, res) => {
  res.sendFile(path.join(distDir, 'index.html'));
});

// Start the server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`Access the application at http://localhost:${PORT}`);
});
