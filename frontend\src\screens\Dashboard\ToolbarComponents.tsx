import React, { useState, useRef, useEffect } from 'react';
import { CalendarDays, ChevronDown, X, RefreshCcw, Filter, SortAsc } from 'lucide-react';
import { useGetFilterOptionsQuery } from '../../store/customerApiSlice';

// Interface for filter state
export interface FilterState {
  search: string;
  sortField: string;
  sortOrder: string;
  dateFrom?: string;
  dateTo?: string;
  dateRange?: 'today' | 'lastWeek' | 'lastMonth';
  model?: string;
  color?: string;
}

// Interface for toolbar component props
interface ToolbarComponentProps {
  isOpen: boolean;
  onToggle: () => void;
  onClose: () => void;
  onApply: (filters: Partial<FilterState>) => void;
  currentFilters: FilterState;
}

// Date Picker Component
export const DatePicker = ({ isOpen, onToggle, onClose, onApply, currentFilters }: ToolbarComponentProps) => {
  const [dateFrom, setDateFrom] = useState<string>(currentFilters.dateFrom || '');
  const [dateTo, setDateTo] = useState<string>(currentFilters.dateTo || '');
  const [selectedRange, setSelectedRange] = useState<string>(currentFilters.dateRange || '');
  const datePickerRef = useRef<HTMLDivElement>(null);

  // Close when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (datePickerRef.current && !datePickerRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  const handleQuickRange = (range: 'lastWeek' | 'lastMonth') => {
    setSelectedRange(range);
    setDateFrom('');
    setDateTo('');
  };

  const handleCustomDate = () => {
    setSelectedRange('');
  };

  const applyDateFilter = () => {
    const filters: Partial<FilterState> = {};

    if (selectedRange) {
      filters.dateRange = selectedRange as 'today' | 'lastWeek' | 'lastMonth';
      filters.dateFrom = undefined;
      filters.dateTo = undefined;
    } else if (dateFrom || dateTo) {
      filters.dateFrom = dateFrom || undefined;
      filters.dateTo = dateTo || undefined;
      filters.dateRange = undefined;
    }

    onApply(filters);
    onClose();
  };

  const clearFilters = () => {
    setDateFrom('');
    setDateTo('');
    setSelectedRange('');
    onApply({ dateFrom: undefined, dateTo: undefined, dateRange: undefined });
    onClose();
  };

  const hasActiveFilters = currentFilters.dateFrom || currentFilters.dateTo || currentFilters.dateRange;

  return (
    <div className="relative">
      <button
        onClick={(e) => {
          e.stopPropagation();
          onToggle();
        }}
        className={`flex items-center justify-center gap-2 px-3 py-2 border transition-all duration-200 ${
          isOpen
            ? 'border-[#ff00ff] bg-[#ff00ff]/5'
            : hasActiveFilters
              ? 'border-[#ff00ff] bg-[#ff00ff]/10'
              : 'border-gray-300 bg-white hover:border-gray-400'
        } rounded-md text-sm font-medium w-full min-h-[38px]`}
      >
        <CalendarDays className="w-4 h-4 flex-shrink-0" />
        <span className="truncate">{hasActiveFilters ? 'Date Active' : 'Date'}</span>
        <ChevronDown className={`w-4 h-4 flex-shrink-0 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div
          ref={datePickerRef}
          className="absolute top-full left-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg p-4 z-50 w-80"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="flex justify-between items-center mb-3">
            <h3 className="font-medium">Date Filter</h3>
            <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
              <X size={16} />
            </button>
          </div>

          {/* Quick Range Options */}
          <div className="mb-4">
            <label className="block text-sm font-medium mb-2">Quick Ranges</label>
            <div className="grid grid-cols-2 gap-2">
              {[
                { value: 'lastWeek', label: 'Last Week' },
                { value: 'lastMonth', label: 'Last Month' }
              ].map((range) => (
                <button
                  key={range.value}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleQuickRange(range.value as 'lastWeek' | 'lastMonth');
                  }}
                  className={`px-3 py-2 text-sm border rounded-md transition-colors ${
                    selectedRange === range.value
                      ? 'border-[#640064] bg-[#640064]/10 text-[#640064]'
                      : 'border-gray-300 hover:border-gray-400 hover:bg-gray-50'
                  }`}
                >
                  {range.label}
                </button>
              ))}
            </div>
          </div>

          {/* Custom Date Range */}
          <div className="mb-4">
            <label className="block text-sm font-medium mb-2">Custom Range</label>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <label className="block text-xs text-gray-500 mb-1">From</label>
                <input
                  type="date"
                  className="w-full p-2 border border-gray-300 rounded-md text-sm focus:border-[#640064] focus:ring-1 focus:ring-[#640064]/20"
                  value={dateFrom}
                  onChange={(e) => {
                    setDateFrom(e.target.value);
                    handleCustomDate();
                  }}
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-1">To</label>
                <input
                  type="date"
                  className="w-full p-2 border border-gray-300 rounded-md text-sm focus:border-[#640064] focus:ring-1 focus:ring-[#640064]/20"
                  value={dateTo}
                  onChange={(e) => {
                    setDateTo(e.target.value);
                    handleCustomDate();
                  }}
                />
              </div>
            </div>
          </div>

          <div className="flex justify-between mt-4">
            <button
              onClick={(e) => {
                e.stopPropagation();
                clearFilters();
              }}
              className="px-3 py-1 text-sm text-red-500 hover:text-red-700"
            >
              Clear
            </button>
            <div className="flex gap-2">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onClose();
                }}
                className="px-3 py-1 text-sm border border-gray-300 rounded-md"
              >
                Cancel
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  applyDateFilter();
                }}
                className="px-3 py-1 text-sm bg-[#640064] text-white rounded-md hover:bg-[#7a007a]"
              >
                Apply
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Sort Component
export const SortBy = ({ isOpen, onToggle, onClose, onApply, currentFilters }: ToolbarComponentProps) => {
  const [sortField, setSortField] = useState<string>(currentFilters.sortField);
  const [sortOrder, setSortOrder] = useState<string>(currentFilters.sortOrder);
  const sortRef = useRef<HTMLDivElement>(null);

  // Close when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (sortRef.current && !sortRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  const sortOptions = [
    { value: 'newest', label: 'Newest First', field: 'createdAt', order: 'desc' },
    { value: 'oldest', label: 'Oldest First', field: 'createdAt', order: 'asc' },
    { value: 'name-asc', label: 'Name (A-Z)', field: 'name', order: 'asc' },
    { value: 'name-desc', label: 'Name (Z-A)', field: 'name', order: 'desc' },
    { value: 'updated', label: 'Recently Updated', field: 'updatedAt', order: 'desc' }
  ];

  const getCurrentSortValue = () => {
    const current = sortOptions.find(opt => opt.field === sortField && opt.order === sortOrder);
    return current?.value || 'newest';
  };

  const handleSortChange = (value: string) => {
    const option = sortOptions.find(opt => opt.value === value);
    if (option) {
      setSortField(option.field);
      setSortOrder(option.order);
    }
  };

  const applySorting = () => {
    onApply({
      sortField,
      sortOrder
    });
    onClose();
  };

  const currentSortValue = getCurrentSortValue();
  const currentSortLabel = sortOptions.find(opt => opt.value === currentSortValue)?.label || 'Sort By';

  return (
    <div className="relative">
      <button
        onClick={(e) => {
          e.stopPropagation();
          onToggle();
        }}
        className={`flex items-center justify-center gap-2 px-3 py-2 border transition-all duration-200 ${
          isOpen
            ? 'border-[#ff00ff] bg-[#ff00ff]/5'
            : 'border-gray-300 bg-white hover:border-gray-400'
        } rounded-md text-sm font-medium w-full min-h-[38px]`}
      >
        <SortAsc className="w-4 h-4 flex-shrink-0" />
        <span className="truncate">{currentSortValue !== 'newest' ? currentSortLabel : 'Sort By'}</span>
        <ChevronDown className={`w-4 h-4 flex-shrink-0 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div
          ref={sortRef}
          className="absolute top-full left-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg p-4 z-50 w-56"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="flex justify-between items-center mb-3">
            <h3 className="font-medium">Sort By</h3>
            <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
              <X size={16} />
            </button>
          </div>
          <div className="space-y-2">
            {sortOptions.map((option) => (
              <label key={option.value} className="flex items-center gap-2 cursor-pointer">
                <input
                  type="radio"
                  name="sort"
                  value={option.value}
                  checked={currentSortValue === option.value}
                  onChange={() => handleSortChange(option.value)}
                  className="accent-[#640064]"
                />
                <span>{option.label}</span>
              </label>
            ))}
          </div>
          <div className="flex justify-end mt-3">
            <button
              onClick={(e) => {
                e.stopPropagation();
                applySorting();
              }}
              className="px-3 py-1 text-sm bg-[#640064] text-white rounded-md hover:bg-[#7a007a]"
            >
              Apply
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

// Filters Component
export const Filters = ({ isOpen, onToggle, onClose, onApply, currentFilters }: ToolbarComponentProps) => {
  const [model, setModel] = useState<string>(currentFilters.model || '');
  const [color, setColor] = useState<string>(currentFilters.color || '');
  const filtersRef = useRef<HTMLDivElement>(null);

  // Fetch filter options from API
  const { data: filterOptions, isLoading: isLoadingOptions } = useGetFilterOptionsQuery();

  // Close when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (filtersRef.current && !filtersRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  const resetFilters = () => {
    setModel('');
    setColor('');
  };

  const applyFilters = () => {
    onApply({
      model: model || undefined,
      color: color || undefined,
    });
    onClose();
  };

  const clearFilters = () => {
    resetFilters();
    onApply({
      model: undefined,
      color: undefined,
    });
    onClose();
  };

  const hasActiveFilters = currentFilters.model || currentFilters.color;

  return (
    <div className="relative">
      <button
        onClick={(e) => {
          e.stopPropagation();
          onToggle();
        }}
        className={`flex items-center justify-center gap-2 px-3 py-2 border transition-all duration-200 ${
          isOpen
            ? 'border-[#ff00ff] bg-[#ff00ff]/5'
            : hasActiveFilters
              ? 'border-[#ff00ff] bg-[#ff00ff]/10'
              : 'border-gray-300 bg-white hover:border-gray-400'
        } rounded-md text-sm font-medium w-full min-h-[38px]`}
      >
        <Filter className="w-4 h-4 flex-shrink-0" />
        <span className="truncate">{hasActiveFilters ? 'Filters Active' : 'Filters'}</span>
        <ChevronDown className={`w-4 h-4 flex-shrink-0 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div
          ref={filtersRef}
          className="absolute top-full right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg p-4 z-50 w-64"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="flex justify-between items-center mb-3">
            <h3 className="font-medium">Filters</h3>
            <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
              <X size={16} />
            </button>
          </div>

          {isLoadingOptions ? (
            <div className="text-center py-4">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#640064] mx-auto"></div>
              <p className="text-sm text-gray-500 mt-2">Loading options...</p>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Vehicle Model Filter */}
              <div>
                <label className="block text-sm font-medium mb-1">Model</label>
                <select
                  className="w-full p-2 border border-gray-300 rounded-md text-sm focus:border-[#640064] focus:ring-1 focus:ring-[#640064]/20"
                  value={model}
                  onChange={(e) => setModel(e.target.value)}
                >
                  <option value="">All Models</option>
                  {filterOptions?.models?.map((modelOption) => (
                    <option key={modelOption} value={modelOption}>
                      {modelOption}
                    </option>
                  ))}
                </select>
              </div>

              {/* Vehicle Color Filter */}
              <div>
                <label className="block text-sm font-medium mb-1">Color</label>
                <select
                  className="w-full p-2 border border-gray-300 rounded-md text-sm focus:border-[#640064] focus:ring-1 focus:ring-[#640064]/20"
                  value={color}
                  onChange={(e) => setColor(e.target.value)}
                >
                  <option value="">All Colors</option>
                  {filterOptions?.colors?.map((colorOption) => (
                    <option key={colorOption} value={colorOption}>
                      {colorOption}
                    </option>
                  ))}
                </select>
              </div>

            </div>
          )}

          <div className="flex justify-between mt-4">
            <button
              onClick={(e) => {
                e.stopPropagation();
                clearFilters();
              }}
              className="px-3 py-1 text-sm text-red-500 hover:text-red-700"
            >
              Clear All
            </button>
            <div className="flex gap-2">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onClose();
                }}
                className="px-3 py-1 text-sm border border-gray-300 rounded-md"
              >
                Cancel
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  applyFilters();
                }}
                className="px-3 py-1 text-sm bg-[#640064] text-white rounded-md hover:bg-[#7a007a]"
              >
                Apply
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Total Customers Component
export const TotalCustomers = ({
  totalCount,
  onRefresh
}: {
  totalCount: number;
  onRefresh: () => Promise<any> | void
}) => {
  const [isUpdating, setIsUpdating] = useState(false);

  const handleRefresh = async () => {
    setIsUpdating(true);
    try {
      await onRefresh();
    } finally {
      // Reset the updating state after a short delay to show the animation
      setTimeout(() => {
        setIsUpdating(false);
      }, 800);
    }
  };

  return (
    <div className="flex flex-row items-center bg-white rounded-md shadow-sm border border-gray-300 transition-all duration-300 w-full h-[38px] overflow-hidden">
      <div className="text-gray-600 text-sm px-3 sm:px-4 flex items-center h-full flex-1 min-w-0">
        <span className="truncate">
          Total Customers: <span className="font-medium ml-1">{totalCount}</span>
        </span>
      </div>
      <button
        onClick={handleRefresh}
        disabled={isUpdating}
        className={`${
          isUpdating ? 'bg-[#e600e6]' : 'bg-[#ff00ff]'
        } text-white px-3 sm:px-5 h-[38px] text-sm font-medium transition-all duration-300 ease-in-out flex items-center justify-center min-w-[80px] sm:min-w-[100px] border-l border-[#ff00ff]/20 hover:bg-[#e600e6] flex-shrink-0`}
      >
        {isUpdating ? (
          <>
            <RefreshCcw className="animate-spin h-4 w-4 mr-2" />
            <span className="whitespace-nowrap">Updating...</span>
          </>
        ) : (
          'Update'
        )}
      </button>
    </div>
  );
};