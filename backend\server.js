import express from 'express';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import dotenv from 'dotenv';
import morgan from 'morgan';
import fs from 'fs';
import mongoose from 'mongoose';
import connectDB from './config/db.js';
import configureSecurityMiddleware from './middleware/securityMiddleware.js';
import configurePerformanceMiddleware from './middleware/performanceMiddleware.js';
import errorHandler from './middleware/errorHandler.js';
import logger from './utils/logger.js';
import { requestLogger, errorLogger } from './middleware/requestLogger.js';
import cleanupService from './services/cleanupService.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Async function to start the server
async function startServer() {
  const envFile = process.env.NODE_ENV === 'production' ? '.env.production' : '.env';
  dotenv.config({ path: join(__dirname, envFile) });



  // Import routes AFTER environment variables are loaded using dynamic imports
  const { default: vehicleRoutes } = await import('./routes/vehicleRoutes.js');
  const { default: adminRoutes } = await import('./routes/adminRoutes.js');
  const { default: dashboardRoutes } = await import('./routes/dashboardRoutes.js');
  const { default: searchRoutes } = await import('./routes/searchRoutes.js');
  const { default: authRoutes } = await import('./routes/authRoutes.js');
  const { default: csvRoutes } = await import('./routes/csvRoutes.js');
  const { default: recaptchaRoutes } = await import('./routes/recaptchaRoutes.js');

  const app = express();
  const PORT = process.env.PORT || 10000;
  const isProduction = process.env.NODE_ENV === 'production';

  if (isProduction) {
    app.set('trust proxy', 1);
  }

  // S3 cloud storage is used for file uploads (no local uploads directory needed)

  configureSecurityMiddleware(app);

  app.use('/api/vehicle', (req, res, next) => {
    const requestOrigin = req.headers.origin;
    const getCorsOrigins = () => {
      const corsOrigins = process.env.CORS_ORIGINS;
      if (!corsOrigins) {
        return process.env.FRONTEND_URL ? [process.env.FRONTEND_URL] : [];
      }
      return corsOrigins.split(',').map(origin => origin.trim()).filter(Boolean);
    };

    const allowedOrigins = getCorsOrigins();
    if (allowedOrigins.includes(requestOrigin)) {
      res.header('Access-Control-Allow-Origin', requestOrigin);
    }

    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
    res.header('Access-Control-Allow-Credentials', 'true');

    if (req.method === 'OPTIONS') {
      return res.status(200).end();
    }

    next();
  });

  configurePerformanceMiddleware(app);
  app.use(requestLogger);
  app.use(express.json({ limit: '50mb' }));
  app.use(express.urlencoded({ extended: true, limit: '50mb' }));

  if (isProduction) {
    app.use(morgan('combined', { stream: logger.stream }));
  } else {
    app.use(morgan('dev', { stream: logger.stream }));
  }

  app.use('/uploads', express.static(join(__dirname, 'uploads')));

  app.use('/api/vehicle', vehicleRoutes);
  app.use('/api/admin', adminRoutes);
  app.use('/api/dashboard', dashboardRoutes);
  app.use('/api/search', searchRoutes);
  app.use('/api/auth', authRoutes);
  app.use('/api/csv', csvRoutes);
  app.use('/api', recaptchaRoutes);

  app.get('/', (req, res) => {
    res.send('Q5XT1 Backend API is running');
  });

  app.get('/health', (req, res) => {
    const healthStatus = {
      status: 'UP',
      timestamp: new Date(),
      environment: process.env.NODE_ENV,
      mongodb: mongoose.connection.readyState === 1 ? 'Connected' : 'Disconnected'
    };

    logger.info('Health check accessed', healthStatus);
    res.status(200).json(healthStatus);
  });

  app.use(errorLogger);
  app.use(errorHandler);

  app.use('*', (req, res) => {
    logger.warn('Route not found', {
      url: req.originalUrl,
      method: req.method,
      ip: req.ip,
      requestId: req.id
    });

    res.status(404).json({
      success: false,
      message: `Route not found: ${req.originalUrl}`
    });
  });

  const server = app.listen(PORT, () => {
    logger.info(`Server running on port ${PORT}`);

    connectDB().then((connected) => {
      if (connected) {
        logger.info('Database connected');

        // Start cleanup service after database connection
        cleanupService.start();
      } else {
        logger.warn('Database connection failed');
      }
    }).catch((error) => {
      logger.error('Database connection error:', error);
    });
  });

  process.on('unhandledRejection', (err) => {
    logger.error('UNHANDLED REJECTION! Shutting down...', {
      name: err.name,
      message: err.message,
      stack: err.stack
    });

    server.close(() => {
      cleanupService.stop();
      process.exit(1);
    });
  });

  process.on('uncaughtException', (err) => {
    logger.error('UNCAUGHT EXCEPTION! Shutting down...', {
      name: err.name,
      message: err.message,
      stack: err.stack
    });
    cleanupService.stop();
    process.exit(1);
  });
}

// Start the server
startServer().catch((error) => {
  console.error('Failed to start server:', error);
  process.exit(1);
});
