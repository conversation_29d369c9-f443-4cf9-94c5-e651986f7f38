# Q5XT1 Frontend

Modern React.js frontend for the Q5XT1 Vehicle Registration Management System.

## 🏗️ Architecture Overview

```
frontend/src/
├── components/       # Reusable UI components
├── screens/         # Page-level components
├── services/        # API services and utilities
├── hooks/           # Custom React hooks
├── utils/           # Utility functions
├── assets/          # Static assets (images, icons)
├── styles/          # Global styles and themes
└── types/           # TypeScript type definitions
```

## 🚀 Quick Start

### Prerequisites
- **Node.js** v18+ (LTS recommended)
- **npm** or **yarn**
- **Backend API** running (see backend README)

### Installation

```bash
# Clone and navigate to frontend
cd frontend

# Install dependencies
npm install

# Copy environment template
cp .env.example .env

# Configure your environment variables
nano .env
```

### Environment Configuration

Create `.env` file with these variables:

```bash
# API Configuration
VITE_API_BASE_URL=http://localhost:5000/api
VITE_BACKEND_URL=http://localhost:5000

# Environment
VITE_NODE_ENV=development

# Features
VITE_ENABLE_MOCK_API=false
VITE_ENABLE_DEBUG=true
```

### Development Server

```bash
# Start development server with hot reload
npm run dev

# Start with specific port
npm run dev -- --port 3000

# Build for production
npm run build

# Preview production build
npm run preview
```

## 🎨 Tech Stack

### Core Technologies
- **React 18** - Modern React with hooks and concurrent features
- **TypeScript** - Type-safe JavaScript development
- **Vite** - Fast build tool and development server
- **Tailwind CSS** - Utility-first CSS framework
- **React Router** - Client-side routing
- **Axios** - HTTP client for API communication

### UI Components
- **Lucide React** - Modern icon library
- **React Hook Form** - Performant form handling
- **React Hot Toast** - Elegant toast notifications
- **Custom Components** - Reusable UI component library

### Development Tools
- **ESLint** - Code linting and quality
- **Prettier** - Code formatting
- **TypeScript** - Static type checking
- **Vite** - Fast development and build

## 🔧 Features

### Core Features
- **Multi-Step Form** - Progressive vehicle registration
- **Real-time Validation** - Instant form validation
- **File Upload** - Document upload with preview
- **Responsive Design** - Mobile-first responsive UI
- **Authentication** - Secure login/logout system
- **Admin Dashboard** - Vehicle management interface
- **Progress Tracking** - Visual form completion progress
- **Auto-save** - Automatic form data persistence

### User Experience
- **Intuitive Navigation** - Clear step-by-step process
- **Visual Feedback** - Loading states and confirmations
- **Error Handling** - User-friendly error messages
- **Accessibility** - WCAG compliant interface
- **Performance** - Optimized for fast loading
- **Mobile Responsive** - Works on all device sizes

## 📱 Application Structure

### Public Routes
- **Landing Page** (`/`) - Application introduction
- **Login** (`/login`) - User authentication
- **Registration Form** (`/register`) - Vehicle registration process

### Protected Routes (Admin)
- **Dashboard** (`/dashboard`) - Vehicle management
- **Vehicle Details** (`/vehicle/:id`) - Individual vehicle view
- **Reports** (`/reports`) - Analytics and reporting

### Form Steps
1. **Customer Information** - Personal details and contact
2. **Vehicle Details** - Vehicle specifications
3. **Exchange Vehicle** - Trade-in information (optional)
4. **Registration** - Registration preferences
5. **Insurance** - Insurance details and nominee
6. **Documents** - File uploads and verification
7. **Review** - Final review and submission

## 🚀 Deployment

### Production Environment Variables

```bash
# Production API Configuration
VITE_API_BASE_URL=https://your-backend-domain.com/api
VITE_BACKEND_URL=https://your-backend-domain.com

# Environment
VITE_NODE_ENV=production

# Features (Production settings)
VITE_ENABLE_MOCK_API=false
VITE_ENABLE_DEBUG=false
```

### Deploy to Vercel

1. **Install Vercel CLI**
   ```bash
   npm install -g vercel
   ```

2. **Build and Deploy**
   ```bash
   # Build for production
   npm run build

   # Deploy to Vercel
   vercel --prod
   ```

3. **Configure Environment Variables** in Vercel dashboard

### Deploy to Netlify

1. **Build for Production**
   ```bash
   npm run build
   ```

2. **Deploy via Netlify CLI**
   ```bash
   # Install Netlify CLI
   npm install -g netlify-cli

   # Deploy
   netlify deploy --prod --dir=dist
   ```

3. **Or connect GitHub repository** in Netlify dashboard

### Deploy to GitHub Pages

```bash
# Install gh-pages
npm install -g gh-pages

# Build and deploy
npm run build
gh-pages -d dist
```

### Deploy to Firebase Hosting

```bash
# Install Firebase CLI
npm install -g firebase-tools

# Initialize Firebase
firebase init hosting

# Build and deploy
npm run build
firebase deploy
```

## 🧩 Component Library

### Core Components

#### Form Components
```typescript
// Input Field with validation
<InputField
  label="Customer Name"
  value={name}
  onChange={setName}
  error={errors.name}
  required
/>

// Select Dropdown
<SelectField
  label="Vehicle Model"
  options={vehicleModels}
  value={selectedModel}
  onChange={setSelectedModel}
/>

// File Upload
<FileUpload
  label="Aadhar Card"
  accept=".pdf,.jpg,.png"
  onFileSelect={handleFileSelect}
  maxSize={5 * 1024 * 1024} // 5MB
/>
```

#### UI Components
```typescript
// Button with loading state
<Button
  variant="primary"
  loading={isSubmitting}
  onClick={handleSubmit}
>
  Submit Application
</Button>

// Progress Indicator
<ProgressBar
  currentStep={3}
  totalSteps={7}
  completedSteps={2}
/>

// Status Badge
<StatusBadge
  status="completed"
  variant="success"
/>
```

#### Layout Components
```typescript
// Page Layout
<PageLayout
  title="Vehicle Registration"
  breadcrumbs={breadcrumbs}
>
  <FormContent />
</PageLayout>

// Card Container
<Card
  title="Customer Information"
  subtitle="Enter your personal details"
>
  <CustomerForm />
</Card>
```

### Custom Hooks

#### Form Management
```typescript
// Multi-step form hook
const {
  currentStep,
  formData,
  nextStep,
  prevStep,
  updateFormData,
  isValid
} = useMultiStepForm(initialData);

// API integration hook
const {
  data,
  loading,
  error,
  refetch
} = useApi('/api/vehicle', options);

// File upload hook
const {
  uploadFile,
  progress,
  uploading,
  error
} = useFileUpload();
```

## 🔌 API Integration

### API Service Configuration

```typescript
// src/services/api.ts
import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Request interceptor for auth
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('authToken');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('authToken');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export default api;
```

### API Methods

```typescript
// Vehicle API service
export const vehicleApi = {
  // Create new vehicle registration
  create: (data: VehicleData) =>
    api.post('/vehicle', data),

  // Get all vehicles
  getAll: (params?: QueryParams) =>
    api.get('/vehicle', { params }),

  // Get vehicle by ID
  getById: (id: string) =>
    api.get(`/vehicle/${id}`),

  // Update vehicle
  update: (id: string, data: Partial<VehicleData>) =>
    api.put(`/vehicle/${id}`, data),

  // Update specific form section
  updateSection: (id: string, section: string, data: any) =>
    api.put(`/vehicle/${id}/${section}`, data),

  // Upload documents
  uploadDocuments: (id: string, files: FormData) =>
    api.post(`/vehicle/${id}/documents`, files, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
};
```

## 🎨 Styling & Theming

### Tailwind CSS Configuration

```javascript
// tailwind.config.js
module.exports = {
  content: ['./src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#f0f9ff',
          500: '#5f2eff',
          600: '#4c1fd9',
          700: '#3b1aa3'
        },
        gray: {
          50: '#f9fafb',
          100: '#f3f4f6',
          500: '#6b7280',
          900: '#111827'
        }
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif']
      }
    }
  },
  plugins: []
};
```

### Custom CSS Variables

```css
/* src/styles/globals.css */
:root {
  --color-primary: #5f2eff;
  --color-primary-dark: #4c1fd9;
  --color-success: #10b981;
  --color-error: #ef4444;
  --color-warning: #f59e0b;

  --border-radius: 0.5rem;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
}
```

## 🔒 Security Features

### Authentication
- **JWT Token Management** - Secure token storage and refresh
- **Route Protection** - Private route guards
- **Auto Logout** - Session timeout handling
- **CSRF Protection** - Cross-site request forgery prevention

### Data Security
- **Input Sanitization** - XSS prevention
- **File Upload Validation** - Type and size restrictions
- **Environment Variables** - Secure configuration
- **API Security** - Request/response validation

## 📊 Performance Optimization

### Build Optimization
- **Code Splitting** - Lazy loading of routes and components
- **Tree Shaking** - Unused code elimination
- **Asset Optimization** - Image and bundle optimization
- **Caching Strategy** - Browser and CDN caching

### Runtime Performance
- **React.memo** - Component memoization
- **useMemo/useCallback** - Hook optimization
- **Virtual Scrolling** - Large list optimization
- **Debounced Inputs** - Reduced API calls

## 🧪 Testing

### Testing Setup
```bash
# Install testing dependencies
npm install -D @testing-library/react @testing-library/jest-dom vitest

# Run tests
npm run test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch
```

### Test Examples
```typescript
// Component test
import { render, screen } from '@testing-library/react';
import { Button } from '../components/Button';

test('renders button with text', () => {
  render(<Button>Click me</Button>);
  expect(screen.getByText('Click me')).toBeInTheDocument();
});

// Hook test
import { renderHook } from '@testing-library/react';
import { useMultiStepForm } from '../hooks/useMultiStepForm';

test('should initialize with first step', () => {
  const { result } = renderHook(() => useMultiStepForm());
  expect(result.current.currentStep).toBe(1);
});
```

## 🐛 Troubleshooting

### Common Issues

1. **API Connection Failed**
   ```bash
   # Check environment variables
   echo $VITE_API_BASE_URL

   # Verify backend is running
   curl http://localhost:5000/health
   ```

2. **Build Errors**
   ```bash
   # Clear node_modules and reinstall
   rm -rf node_modules package-lock.json
   npm install

   # Clear Vite cache
   rm -rf node_modules/.vite
   ```

3. **CORS Issues**
   ```bash
   # Check backend CORS configuration
   # Verify FRONTEND_URL in backend .env
   # Ensure API URLs match
   ```

### Debug Mode
```bash
# Enable debug logging
VITE_ENABLE_DEBUG=true npm run dev

# Check browser console for detailed logs
# Use React Developer Tools for component inspection
```

## 📚 Documentation

### Available Documentation
- **Component Library** - Storybook documentation (if implemented)
- **API Integration** - This README and backend documentation
- **Deployment Guide** - See deployment section above
- **Troubleshooting** - Common issues and solutions

### Code Documentation
```typescript
// Use JSDoc for component documentation
/**
 * Multi-step form component for vehicle registration
 * @param initialData - Initial form data
 * @param onSubmit - Callback when form is submitted
 * @param onStepChange - Callback when step changes
 */
export const VehicleRegistrationForm: React.FC<Props> = ({
  initialData,
  onSubmit,
  onStepChange
}) => {
  // Component implementation
};
```

## 🔧 Development Tools

### Recommended VS Code Extensions
- **ES7+ React/Redux/React-Native snippets**
- **Tailwind CSS IntelliSense**
- **TypeScript Importer**
- **Auto Rename Tag**
- **Prettier - Code formatter**
- **ESLint**

### Browser Extensions
- **React Developer Tools**
- **Redux DevTools** (if using Redux)
- **Axe DevTools** (accessibility testing)

## 📞 Support

### Getting Help
- **Documentation** - Check this README and component docs
- **Console Logs** - Enable debug mode for detailed logging
- **Network Tab** - Check API requests and responses
- **React DevTools** - Inspect component state and props

### Development Workflow
1. **Start Backend** - Ensure API server is running
2. **Start Frontend** - Run development server
3. **Check Console** - Monitor for errors and warnings
4. **Test Features** - Verify functionality works as expected
5. **Build & Deploy** - Test production build before deployment

---

**🚀 Modern React** | **🎨 Tailwind CSS** | **📱 Responsive** | **🔒 Secure** | **⚡ Fast**
