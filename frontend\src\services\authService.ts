import axios from 'axios';
import config from '../config/config';

const API_URL = `${config.api.baseUrl}/auth`;

// Types
interface LoginCredentials {
  email: string;
  password: string;
}

interface RegisterData {
  name: string;
  email: string;
  password: string;
  role?: string;
}

interface AuthResponse {
  success: boolean;
  token: string;
  admin: {
    id: string;
    name: string;
    email: string;
    role: string;
  };
}

// Create axios instance with interceptors
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json'
  },
  timeout: config.api.timeout,
  withCredentials: config.api.withCredentials // Enable cookies for cross-origin requests
});

// Add request interceptor to include token in headers
api.interceptors.request.use(
  (requestConfig) => {
    const token = localStorage.getItem(config.auth.tokenKey);
    if (token) {
      requestConfig.headers.Authorization = `Bearer ${token}`;
    }
    return requestConfig;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Authentication service
const authService = {
  // Login user
  login: async (credentials: LoginCredentials): Promise<AuthResponse> => {
    try {
      const response = await api.post<AuthResponse>('/login', credentials);

      if (response.data.success) {
        // Store token and user data in localStorage
        localStorage.setItem(config.auth.tokenKey, response.data.token);
        localStorage.setItem(config.auth.userKey, JSON.stringify(response.data.admin));
      }

      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Register user
  register: async (data: RegisterData): Promise<AuthResponse> => {
    try {
      const response = await api.post<AuthResponse>('/register', data);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Logout user
  logout: async (): Promise<void> => {
    try {
      await api.post('/logout');
      localStorage.removeItem(config.auth.tokenKey);
      localStorage.removeItem(config.auth.userKey);
    } catch (error) {
      // Still remove items from localStorage even if API call fails
      localStorage.removeItem(config.auth.tokenKey);
      localStorage.removeItem(config.auth.userKey);
    }
  },

  // Get current user profile
  getCurrentUser: async () => {
    try {
      const response = await api.get('/me');
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Check if user is authenticated
  isAuthenticated: (): boolean => {
    return !!localStorage.getItem(config.auth.tokenKey);
  },

  // Get user from localStorage
  getUser: () => {
    const user = localStorage.getItem(config.auth.userKey);
    return user ? JSON.parse(user) : null;
  },

  // Get token key from config
  getTokenKey: () => {
    return config.auth.tokenKey;
  },

  // Get user key from config
  getUserKey: () => {
    return config.auth.userKey;
  }
};

export default authService;
