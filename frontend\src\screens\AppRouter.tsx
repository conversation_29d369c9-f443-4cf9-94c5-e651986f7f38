import { Routes, Route, Link, Navigate } from 'react-router-dom';
import { Element } from './Element';
import Dashboard from './Dashboard';
import LoginPage from './Login';
import { ProtectedRoute } from '../components/auth/ProtectedRoute';

const AppRouter = () => {
  return (
    <Routes>
      <Route path="/" element={<Element />} />
      <Route path="/login" element={<LoginPage />} />

      {/* Protected Routes - Only accessible after login */}
      <Route element={<ProtectedRoute redirectPath="/login" />}>
        <Route path="/dashboard" element={<Dashboard />} />
      </Route>

      <Route path="*" element={<NotFound />} />
    </Routes>
  );
};

// Simple 404 page
const NotFound = () => {
  return (
    <div className="min-h-screen bg-[#1e1f20] flex flex-col items-center justify-center text-white">
      <h1 className="text-4xl mb-4">404 - Page Not Found</h1>
      <p className="mb-8">The page you are looking for does not exist.</p>
      <div className="flex gap-4">
        <Link to="/" className="bg-[#640064] text-white px-6 py-2 rounded-lg hover:bg-[#7a007a]">
          Go to Form
        </Link>
        <Link to="/dashboard" className="bg-[#5f2eff] text-white px-6 py-2 rounded-lg hover:bg-[#4a1ecc]">
          Go to Dashboard
        </Link>
      </div>
    </div>
  );
};

export default AppRouter;
