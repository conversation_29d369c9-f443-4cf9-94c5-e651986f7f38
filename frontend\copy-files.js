import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Files to copy from public to dist
const filesToCopy = [
  '_redirects',
  '_headers',
  'web.config',
  '200.html'
];

// Create dist directory if it doesn't exist
const distDir = path.join(__dirname, 'dist');
if (!fs.existsSync(distDir)) {
  fs.mkdirSync(distDir, { recursive: true });
}

// Copy each file
filesToCopy.forEach(file => {
  const sourcePath = path.join(__dirname, 'public', file);
  const destPath = path.join(distDir, file);

  if (fs.existsSync(sourcePath)) {
    fs.copyFileSync(sourcePath, destPath);
    // File copied successfully
  } else {
    // File not found - continuing without it
  }
});

// Copy index.html to 200.html and 404.html in dist
const indexPath = path.join(distDir, 'index.html');
if (fs.existsSync(indexPath)) {
  const content = fs.readFileSync(indexPath);
  fs.writeFileSync(path.join(distDir, '200.html'), content);
  fs.writeFileSync(path.join(distDir, '404.html'), content);
  // SPA routing files created successfully
} else {
  // Warning: index.html not found in dist directory
}

console.log('File copying complete');
