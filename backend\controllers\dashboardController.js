import mongoose from 'mongoose';
import Vehicle from '../models/Vehicle.js';
import Customer from '../models/Customer.js';
import { isMongoConnected } from '../config/db.js';
import logger from '../utils/logger.js';

/**
 * Get dashboard statistics
 */
export const getDashboardStats = async (req, res) => {
  try {
    if (!isMongoConnected()) {
      return res.status(200).json({
        success: true,
        data: {
          totalCustomers: 0,
          totalVehicles: 0,
          pendingVehicles: 0,
          completedVehicles: 0,
          vehiclesByModel: [],
          recentCustomers: []
        },
        message: 'MongoDB not connected, using default values'
      });
    }

    // Get counts - dashboard only shows customers with completed vehicles
    const allCustomersWithVehicles = await Customer.find()
      .populate('vehicles', 'status');

    const customersWithCompletedVehicles = allCustomersWithVehicles.filter(customer => {
      if (!customer.vehicles || customer.vehicles.length === 0) return false;
      return customer.vehicles.some(vehicle => vehicle.status === 'completed');
    });

    const totalCustomers = customersWithCompletedVehicles.length;
    const totalVehicles = await Vehicle.countDocuments();
    const pendingVehicles = await Vehicle.countDocuments({ status: 'pending' });
    const completedVehicles = await Vehicle.countDocuments({ status: 'completed' });

    // Get vehicle distribution by model - only completed vehicles
    const vehiclesByModel = await Vehicle.aggregate([
      {
        $match: { status: 'completed' }
      },
      {
        $group: {
          _id: '$vehicleDetails.model',
          count: { $sum: 1 }
        }
      },
      {
        $project: {
          model: '$_id',
          count: 1,
          _id: 0
        }
      },
      {
        $sort: { count: -1 }
      },
      {
        $limit: 5
      }
    ]);

    // Get recent customers - only those with completed vehicles
    const recentCustomers = customersWithCompletedVehicles
      .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
      .slice(0, 5);

    // Get recent vehicles - only completed vehicles
    const recentVehicles = await Vehicle.find({ status: 'completed' })
      .sort({ createdAt: -1 })
      .limit(5);

    res.status(200).json({
      success: true,
      data: {
        totalCustomers,
        totalVehicles,
        pendingVehicles,
        completedVehicles,
        vehiclesByModel,
        recentCustomers,
        recentVehicles
      }
    });
  } catch (error) {
    logger.error('Error getting dashboard stats:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting dashboard statistics',
      error: error.message
    });
  }
};

/**
 * Get vehicle status distribution
 */
export const getVehicleStatusDistribution = async (req, res) => {
  try {
    if (!isMongoConnected()) {
      return res.status(200).json({
        success: true,
        data: [],
        message: 'MongoDB not connected, using default values'
      });
    }

    const statusDistribution = await Vehicle.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      },
      {
        $project: {
          status: '$_id',
          count: 1,
          _id: 0
        }
      }
    ]);

    res.status(200).json({
      success: true,
      data: statusDistribution
    });
  } catch (error) {
    logger.error('Error getting vehicle status distribution:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting vehicle status distribution',
      error: error.message
    });
  }
};

/**
 * Get monthly vehicle registrations
 */
export const getMonthlyRegistrations = async (req, res) => {
  try {
    if (!isMongoConnected()) {
      return res.status(200).json({
        success: true,
        data: [],
        message: 'MongoDB not connected, using default values'
      });
    }

    // Get the current year
    const currentYear = new Date().getFullYear();

    // Get monthly registrations for the current year
    const monthlyRegistrations = await Vehicle.aggregate([
      {
        $match: {
          createdAt: {
            $gte: new Date(`${currentYear}-01-01`),
            $lte: new Date(`${currentYear}-12-31`)
          }
        }
      },
      {
        $group: {
          _id: { $month: '$createdAt' },
          count: { $sum: 1 }
        }
      },
      {
        $project: {
          month: '$_id',
          count: 1,
          _id: 0
        }
      },
      {
        $sort: { month: 1 }
      }
    ]);

    // Fill in missing months with zero counts
    const result = Array.from({ length: 12 }, (_, i) => {
      const existingMonth = monthlyRegistrations.find(item => item.month === i + 1);
      return {
        month: i + 1,
        count: existingMonth ? existingMonth.count : 0
      };
    });

    res.status(200).json({
      success: true,
      data: result
    });
  } catch (error) {
    logger.error('Error getting monthly registrations:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting monthly registrations',
      error: error.message
    });
  }
};
