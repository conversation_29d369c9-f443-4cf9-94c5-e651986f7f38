/**
 * Application configuration
 * This file contains environment-specific configuration
 */

// Determine the current environment
const isProduction = import.meta.env.PROD;

// API configuration
const API_CONFIG = {
  // Base URL for API requests
  baseUrl: isProduction
    ? 'https://backend-3xqm.onrender.com/api' // Render URL
    : 'http://localhost:5000/api',

  // Frontend URL for CORS configuration
  frontendUrl: isProduction
    ? 'https://q5-x-preview.vercel.app'
    : 'http://localhost:5173',

  // Timeout for API requests in milliseconds
  timeout: 15000,

  // Whether to include credentials in requests (for cookies)
  withCredentials: true
};

// Authentication configuration
const AUTH_CONFIG = {
  // Token storage key in localStorage
  tokenKey: 'token',

  // User storage key in localStorage
  userKey: 'user',

  // Token expiration time in milliseconds (matches backend JWT_EXPIRE)
  tokenExpire: isProduction ? 7 * 24 * 60 * 60 * 1000 : 24 * 60 * 60 * 1000
};

// Export configuration
export const config = {
  api: API_CONFIG,
  auth: AUTH_CONFIG,
  isProduction
};

export default config;
