/**
 * Utility functions for file handling
 */

/**
 * Check if a file is an image based on its type or extension
 */
export const isImageFile = (fileName?: string, fileType?: string): boolean => {
    // Check by MIME type first
    if (fileType && fileType.startsWith('image/')) {
        return true;
    }

    // Check by file extension
    if (fileName) {
        const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
        const extension = fileName.toLowerCase().split('.').pop();
        return imageExtensions.includes(extension || '');
    }

    return false;
};

/**
 * Check if a file is a PDF
 */
export const isPdfFile = (fileName?: string, fileType?: string): boolean => {
    if (fileType === 'application/pdf') {
        return true;
    }

    if (fileName && fileName.toLowerCase().endsWith('.pdf')) {
        return true;
    }

    return false;
};

/**
 * Get a human-readable file type description
 */
export const getFileTypeDescription = (fileName?: string, fileType?: string): string => {
    if (isImageFile(fileName, fileType)) {
        return 'Image';
    }

    if (isPdfFile(fileName, fileType)) {
        return 'PDF Document';
    }

    if (fileType?.startsWith('text/')) {
        return 'Text Document';
    }

    // Try to determine from extension
    if (fileName) {
        const extension = fileName.toLowerCase().split('.').pop();
        switch (extension) {
            case 'doc':
            case 'docx':
                return 'Word Document';
            case 'xls':
            case 'xlsx':
                return 'Excel Spreadsheet';
            case 'ppt':
            case 'pptx':
                return 'PowerPoint Presentation';
            case 'txt':
                return 'Text File';
            case 'csv':
                return 'CSV File';
            default:
                return 'Document';
        }
    }

    return 'Document';
};
