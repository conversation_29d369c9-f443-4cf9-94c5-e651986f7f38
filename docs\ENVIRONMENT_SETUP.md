# 🌍 Environment Configuration Guide

This document provides a comprehensive guide for setting up development and production environments for the Q5X BSEM application with reCAPTCHA v3 integration.

## 📋 Overview

The application uses environment-specific configuration files to manage different settings for development and production environments. This ensures proper security, CORS configuration, and reCAPTCHA functionality across all deployment scenarios.

## 🔧 Development Environment

### Backend Configuration (`backend/.env`)

```bash
# Server Configuration
PORT=5000
NODE_ENV=development

# Database
MONGODB_URI=mongodb+srv://zaidkhan4717:<EMAIL>/q5xt1?retryWrites=true&w=majority&appName=Cluster0

# Authentication
JWT_SECRET=q5xt1-dev-jwt-secret
JWT_EXPIRE=1d
JWT_COOKIE_EXPIRE=1

# File Upload
UPLOAD_PATH=uploads
FILE_CONTROLLER_TYPE=simple

# CORS Configuration - Development
FRONTEND_URL=http://localhost:5173
CORS_ORIGINS=http://localhost:5173,http://localhost:5174,http://localhost:3000

# reCAPTCHA Configuration
RECAPTCHA_SITE_KEY=6Le3yVErAAAAAGjTP9KwEzMyE5f5s_rsLUUHekl8
RECAPTCHA_SECRET_KEY=6Le3yVErAAAAAEyyeq51I3ECkRc-P0LBYtpCwgcD
```

### Frontend Configuration (`frontend/.env`)

```bash
# Environment
NODE_ENV=development

# Backend URL for development
VITE_BACKEND_URL=http://localhost:5000

# reCAPTCHA Configuration
VITE_RECAPTCHA_SITE_KEY=6Le3yVErAAAAAGjTP9KwEzMyE5f5s_rsLUUHekl8
```

## 🚀 Production Environment

### Backend Configuration (`backend/.env.production`)

```bash
# Server Configuration
PORT=10000
NODE_ENV=production

# Database
MONGODB_URI=mongodb+srv://zaidkhan4717:<EMAIL>/q5xt1?retryWrites=true&w=majority&appName=Cluster0

# Authentication
JWT_SECRET=q5xt1-secure-jwt-secret-for-production
JWT_EXPIRE=7d
JWT_COOKIE_EXPIRE=7

# File Upload
UPLOAD_PATH=uploads
FILE_CONTROLLER_TYPE=hybrid

# CORS Configuration - Production
FRONTEND_URL=https://q5-x-preview.vercel.app
CORS_ORIGINS=https://q5-x-preview.vercel.app,https://q5x-bsem-zaidgit26s-projects.vercel.app,https://q5x-bsem.vercel.app,https://frontend-rwwb.onrender.com

# reCAPTCHA Configuration
RECAPTCHA_SITE_KEY=6Le3yVErAAAAAGjTP9KwEzMyE5f5s_rsLUUHekl8
RECAPTCHA_SECRET_KEY=6Le3yVErAAAAAEyyeq51I3ECkRc-P0LBYtpCwgcD
```

### Frontend Configuration (`frontend/.env.production`)

```bash
# Environment
NODE_ENV=production

# Backend URL for production
VITE_BACKEND_URL=https://backend-3xqm.onrender.com

# reCAPTCHA Configuration
VITE_RECAPTCHA_SITE_KEY=6Le3yVErAAAAAGjTP9KwEzMyE5f5s_rsLUUHekl8
```

## 🔐 reCAPTCHA v3 Configuration

### Key Features
- **Single Key Pair**: Uses the same reCAPTCHA keys for both development and production
- **Domain Flexibility**: Keys are configured to work on localhost and production domains
- **v3 Only**: Configured for reCAPTCHA v3 with automatic retry logic
- **Seamless Integration**: No user interaction required for verification

### Key Details
- **Site Key**: `6Le3yVErAAAAAGjTP9KwEzMyE5f5s_rsLUUHekl8`
- **Secret Key**: `6Le3yVErAAAAAEyyeq51I3ECkRc-P0LBYtpCwgcD`
- **Domains**: Configured for localhost and all production domains

## 🌐 CORS Configuration

### Development CORS
- `http://localhost:5173` (Primary frontend port)
- `http://localhost:5174` (Alternative frontend port)
- `http://localhost:3000` (Alternative development port)

### Production CORS
- `https://q5x-bsem.vercel.app` (Primary production domain)
- `https://q5x-bsem-zaidgit26s-projects.vercel.app` (Vercel preview)
- `https://frontend-rwwb.onrender.com` (Render deployment)
- `https://q5-x-preview.vercel.app` (Preview deployment)

## 🔄 Environment Loading Logic

### Backend Environment Loading
```javascript
// Load environment variables based on NODE_ENV
const envFile = process.env.NODE_ENV === 'production' ? '.env.production' : '.env';
dotenv.config({ path: join(__dirname, envFile) });
```

### Frontend Environment Detection
```javascript
// Determine the current environment
const isProduction = import.meta.env.PROD;

// API configuration
const backendUrl = import.meta.env.VITE_BACKEND_URL ||
                  (import.meta.env.NODE_ENV === 'production'
                    ? 'https://backend-3xqm.onrender.com'
                    : 'http://localhost:5000');
```

## ✅ Environment Validation

### Validation Script
Run the environment validation script to ensure all configurations are correct:

```bash
node scripts/validate-environment.js
```

### Validation Checks
- ✅ Required environment variables present
- ✅ URL format validation
- ✅ CORS origin validation
- ✅ reCAPTCHA key consistency
- ✅ Environment-specific settings

## 🚨 Security Considerations

### Development Security
- Rate limiting disabled for development
- Debug logging enabled
- Relaxed CORS for local development
- Short JWT expiration (1 day)

### Production Security
- Rate limiting enabled (100 requests/15 minutes)
- Helmet security headers
- Strict CORS configuration
- Longer JWT expiration (7 days)
- Production-grade error handling

## 🔧 Deployment Configuration

### Vercel Configuration (`frontend/vercel.json`)
```json
{
  "env": {
    "NODE_ENV": "production",
    "VITE_BACKEND_URL": "https://backend-3xqm.onrender.com",
    "VITE_RECAPTCHA_SITE_KEY": "6Le3yVErAAAAAGjTP9KwEzMyE5f5s_rsLUUHekl8"
  }
}
```

### Render Configuration
Environment variables are set directly in the Render dashboard using the production values.

## 🐛 Troubleshooting

### Common Issues

1. **CORS Errors**
   - Verify frontend URL is in CORS_ORIGINS
   - Check environment file loading
   - Ensure proper protocol (http/https)

2. **reCAPTCHA Failures**
   - Verify site key matches between frontend and backend
   - Check domain configuration in Google reCAPTCHA console
   - Ensure network connectivity to Google services

3. **Environment Loading Issues**
   - Verify file paths and names
   - Check NODE_ENV setting
   - Ensure proper file permissions

### Debug Commands
```bash
# Check environment loading
node -e "console.log(process.env.NODE_ENV)"

# Validate configuration
node scripts/validate-environment.js

# Test reCAPTCHA endpoint
curl -X POST http://localhost:5000/api/recaptcha-status
```

## 📚 Related Documentation
- [reCAPTCHA Integration Guide](./RECAPTCHA_INTEGRATION.md)
- [Deployment Guide](../README.md#deployment)
- [Security Configuration](./SECURITY.md)
