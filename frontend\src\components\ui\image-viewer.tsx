import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import api from '../../services/api';

interface ImageViewerProps {
    imageUrl: string;
    isOpen: boolean;
    onClose: () => void;
    title?: string;
}

const ImageViewer: React.FC<ImageViewerProps> = ({ imageUrl, isOpen, onClose, title = "Document" }) => {
    const [modalRoot, setModalRoot] = useState<HTMLElement | null>(null);
    const [isClosing, setIsClosing] = useState(false);
    const [isLoading, setIsLoading] = useState(true);
    const [hasError, setHasError] = useState(false);
    const [zoom, setZoom] = useState(1);
    const [position, setPosition] = useState({ x: 0, y: 0 });
    const [isDragging, setIsDragging] = useState(false);
    const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
    const [authenticatedImageUrl, setAuthenticatedImageUrl] = useState<string>('');

    useEffect(() => {
        if (!isOpen) return;

        // Find or create the modal root element
        let root = document.getElementById("modal-root");
        if (!root) {
            root = document.createElement("div");
            root.id = "modal-root";
            document.body.appendChild(root);
        }
        setModalRoot(root);

        // Prevent body scrolling when modal is open
        document.body.style.overflow = "hidden";

        // Reset state when opening
        setZoom(1);
        setPosition({ x: 0, y: 0 });
        setIsLoading(true);
        setHasError(false);
        setAuthenticatedImageUrl('');

        // Load image with authentication
        if (imageUrl) {
            loadAuthenticatedImage(imageUrl);
        }

        // Cleanup function
        return () => {
            document.body.style.overflow = "auto";
            // Clean up blob URL if it exists
            if (authenticatedImageUrl && authenticatedImageUrl.startsWith('blob:')) {
                URL.revokeObjectURL(authenticatedImageUrl);
            }
        };
    }, [isOpen, imageUrl]);

    // Function to load image with authentication
    const loadAuthenticatedImage = async (url: string) => {
        try {
            setIsLoading(true);
            setHasError(false);

            // Extract the relative path from the full URL
            const relativePath = url.replace(api.defaults.baseURL || '', '');

            // Try to fetch the image with authentication
            const response = await api.get(relativePath, {
                responseType: 'blob',
                timeout: 10000 // 10 second timeout for images
            });

            // Create blob URL for the authenticated image
            const blob = response.data;
            const blobUrl = URL.createObjectURL(blob);

            setAuthenticatedImageUrl(blobUrl);
            setIsLoading(false);
        } catch (error) {
            console.error('Failed to load authenticated image:', error);

            // Fallback: try loading the image directly (for cases where auth might not be needed)
            setAuthenticatedImageUrl(url);
            // Let the img onError handler deal with any further errors
        }
    };

    const handleClose = () => {
        setIsClosing(true);
        setTimeout(() => {
            setIsClosing(false);
            onClose();
        }, 200);
    };

    const handleImageLoad = () => {
        setIsLoading(false);
        setHasError(false);
    };

    const handleImageError = () => {
        setIsLoading(false);
        setHasError(true);
    };

    const handleZoomIn = () => {
        setZoom(prev => Math.min(prev * 1.2, 3));
    };

    const handleZoomOut = () => {
        setZoom(prev => Math.max(prev / 1.2, 0.5));
    };

    const handleResetZoom = () => {
        setZoom(1);
        setPosition({ x: 0, y: 0 });
    };

    const handleMouseDown = (e: React.MouseEvent) => {
        if (zoom > 1) {
            setIsDragging(true);
            setDragStart({
                x: e.clientX - position.x,
                y: e.clientY - position.y
            });
        }
    };

    const handleMouseMove = (e: React.MouseEvent) => {
        if (isDragging && zoom > 1) {
            setPosition({
                x: e.clientX - dragStart.x,
                y: e.clientY - dragStart.y
            });
        }
    };

    const handleMouseUp = () => {
        setIsDragging(false);
    };

    // Handle keyboard shortcuts
    useEffect(() => {
        if (!isOpen) return;

        const handleKeyDown = (e: KeyboardEvent) => {
            switch (e.key) {
                case 'Escape':
                    handleClose();
                    break;
                case '+':
                case '=':
                    e.preventDefault();
                    handleZoomIn();
                    break;
                case '-':
                    e.preventDefault();
                    handleZoomOut();
                    break;
                case '0':
                    e.preventDefault();
                    handleResetZoom();
                    break;
            }
        };

        document.addEventListener('keydown', handleKeyDown);
        return () => document.removeEventListener('keydown', handleKeyDown);
    }, [isOpen]);

    // Cleanup blob URL when component unmounts
    useEffect(() => {
        return () => {
            if (authenticatedImageUrl && authenticatedImageUrl.startsWith('blob:')) {
                URL.revokeObjectURL(authenticatedImageUrl);
            }
        };
    }, [authenticatedImageUrl]);

    if (!isOpen || !modalRoot) return null;

    return createPortal(
        <div
            className={`fixed inset-0 z-50 flex items-center justify-center transition-opacity duration-200 ease-in-out ${
                isClosing ? 'opacity-0' : 'opacity-100'
            } bg-black bg-opacity-90`}
            onClick={handleClose}
        >
            <div
                className={`relative w-full h-full flex flex-col transition-all duration-200 ease-in-out ${
                    isClosing ? 'opacity-0 scale-95' : 'opacity-100 scale-100'
                }`}
                onClick={(e) => e.stopPropagation()}
            >
                {/* Header */}
                <div className="absolute top-0 left-0 right-0 z-10 bg-black bg-opacity-50 text-white p-4 flex justify-between items-center">
                    <h3 className="text-lg font-medium">{title}</h3>
                    <div className="flex items-center space-x-2">
                        {/* Zoom controls */}
                        <button
                            onClick={handleZoomOut}
                            className="p-2 hover:bg-white hover:bg-opacity-20 rounded transition-colors"
                            title="Zoom Out (-)"
                        >
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                            </svg>
                        </button>
                        <span className="text-sm min-w-[60px] text-center">{Math.round(zoom * 100)}%</span>
                        <button
                            onClick={handleZoomIn}
                            className="p-2 hover:bg-white hover:bg-opacity-20 rounded transition-colors"
                            title="Zoom In (+)"
                        >
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                            </svg>
                        </button>
                        <button
                            onClick={handleResetZoom}
                            className="p-2 hover:bg-white hover:bg-opacity-20 rounded transition-colors"
                            title="Reset Zoom (0)"
                        >
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                        </button>
                        <button
                            onClick={handleClose}
                            className="p-2 hover:bg-white hover:bg-opacity-20 rounded transition-colors"
                            title="Close (Esc)"
                        >
                            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                </div>

                {/* Image container */}
                <div
                    className="flex-1 flex items-center justify-center overflow-hidden cursor-move"
                    onMouseDown={handleMouseDown}
                    onMouseMove={handleMouseMove}
                    onMouseUp={handleMouseUp}
                    onMouseLeave={handleMouseUp}
                >
                    {isLoading && (
                        <div className="text-white text-center">
                            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
                            <p>Loading image...</p>
                        </div>
                    )}

                    {hasError && (
                        <div className="text-white text-center">
                            <svg className="w-16 h-16 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                            </svg>
                            <p>Failed to load image</p>
                            <p className="text-sm text-gray-400 mt-2">The image could not be displayed</p>
                        </div>
                    )}

                    {!hasError && authenticatedImageUrl && (
                        <img
                            src={authenticatedImageUrl}
                            alt={title}
                            className={`max-w-none transition-transform duration-200 ${isDragging ? 'cursor-grabbing' : zoom > 1 ? 'cursor-grab' : 'cursor-default'}`}
                            style={{
                                transform: `scale(${zoom}) translate(${position.x / zoom}px, ${position.y / zoom}px)`,
                                display: isLoading ? 'none' : 'block'
                            }}
                            onLoad={handleImageLoad}
                            onError={handleImageError}
                            draggable={false}
                        />
                    )}
                </div>

                {/* Footer with instructions */}
                <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white p-2 text-center text-sm">
                    <p>Use +/- to zoom, drag to pan, ESC to close</p>
                </div>
            </div>
        </div>,
        modalRoot
    );
};

export default ImageViewer;
