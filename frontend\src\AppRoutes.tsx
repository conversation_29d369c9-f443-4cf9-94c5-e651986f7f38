import {Routes, Route, Navigate} from "react-router-dom";
import Dashboard from "./screens/Dashboard/DashComponent";
import {FormPage} from "./screens/Element/FormPage";


const AppRoutes = () => {
    return (
        <Routes>
            <Route path="/" element={<Navigate to="/form" />} />
            <Route path="/form" element={<FormPage />} />
            <Route path="/dashboard" element={<Dashboard />} />
            <Route path="*" element={<div className="text-white"> 404 - Error not Found </div>} />
        </Routes>
    )
}



export default AppRoutes;