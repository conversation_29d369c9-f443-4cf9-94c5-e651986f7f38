import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store/store';
import { checkDuplicate, clearDuplicateCheck } from '../../store/apiSlice';

interface DuplicateCheckProps {
  phone?: string;
  email?: string;
  vehicleId?: string;
  onDuplicateFound?: (customer: any) => void;
  onNoDuplicate?: () => void;
}

export const DuplicateCheck: React.FC<DuplicateCheckProps> = ({
  phone,
  email,
  vehicleId,
  onDuplicateFound,
  onNoDuplicate
}) => {
  const dispatch = useDispatch();
  const { duplicateCheck } = useSelector((state: RootState) => state.api);

  useEffect(() => {
    // Clear previous check when component mounts
    dispatch(clearDuplicateCheck());
  }, [dispatch]);

  useEffect(() => {
    // Only check if we have phone or email and they're valid
    if ((phone && phone.length >= 10) || (email && email.includes('@'))) {
      const checkData: { phone?: string; email?: string; vehicleId?: string } = {};

      if (phone && phone.length >= 10) {
        checkData.phone = phone;
      }

      if (email && email.includes('@')) {
        checkData.email = email;
      }

      if (vehicleId) {
        checkData.vehicleId = vehicleId;
      }

      // Debounce the check by 500ms
      const timeoutId = setTimeout(() => {
        dispatch(checkDuplicate(checkData) as any);
      }, 500);

      return () => clearTimeout(timeoutId);
    }
  }, [phone, email, vehicleId, dispatch]);

  useEffect(() => {
    // Handle duplicate check results
    if (duplicateCheck?.isDuplicate && duplicateCheck?.customer) {
      onDuplicateFound?.(duplicateCheck.customer);
    } else if (!duplicateCheck?.isChecking && !duplicateCheck?.isDuplicate && !duplicateCheck?.error) {
      onNoDuplicate?.();
    }
  }, [duplicateCheck, onDuplicateFound, onNoDuplicate]);

  if (!phone && !email) {
    return null;
  }

  if (duplicateCheck?.isChecking) {
    return (
      <div className="flex items-center space-x-2 text-blue-400 text-sm">
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-400"></div>
        <span>Checking for existing customer...</span>
      </div>
    );
  }

  if (duplicateCheck?.error) {
    return (
      <div className="text-yellow-400 text-sm">
        <span>⚠️ Could not check for duplicates: {duplicateCheck.error}</span>
      </div>
    );
  }

  // No UI rendering for duplicate error; handled by parent via callback
  if (duplicateCheck?.isDuplicate && duplicateCheck?.customer) {
    // Only call the callback, don't render anything
    return null;
  }

  // Only show green notification if neither phone nor email is a duplicate
  if (!duplicateCheck?.isChecking && !duplicateCheck?.isDuplicate && (phone || email)) {
    return (
      <div className="flex items-center space-x-2 text-green-400 text-sm">
        <span className="text-green-500">✓</span>
        <span>No existing customer found - you can proceed</span>
      </div>
    );
  }

  return null;
};

export default DuplicateCheck;
