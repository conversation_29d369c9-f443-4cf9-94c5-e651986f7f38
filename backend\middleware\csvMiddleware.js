import CSVStorageService from '../services/csvStorageService.js';
import logger from '../utils/logger.js';

const csvService = new CSVStorageService();

export const csvCaptureMiddleware = (operation = 'update') => {
  return async (req, res, next) => {
    const originalJson = res.json;

    res.json = function(data) {
      const result = originalJson.call(this, data);

      setImmediate(async () => {
        try {
          if (res.statusCode >= 200 && res.statusCode < 300 && data.success && data.data) {
            const vehicleData = data.data;

            if (vehicleData.status === 'completed') {
              await csvService.addVehicleData(vehicleData);
            }
          }
        } catch (error) {
          logger.error('CSV Middleware Error:', error);
        }
      });

      return result;
    };

    next();
  };
};

export const csvCreateMiddleware = csvCaptureMiddleware('create');
export const csvUpdateMiddleware = csvCaptureMiddleware('update');
export const csvCompleteMiddleware = csvCaptureMiddleware('complete');

export const csvExportController = async (req, res) => {
  try {
    const { vehicleId } = req.params;
    const { operation = 'manual' } = req.body;

    const Vehicle = (await import('../models/Vehicle.js')).default;
    const vehicleData = await Vehicle.findById(vehicleId);

    if (!vehicleData) {
      return res.status(404).json({
        success: false,
        message: 'Vehicle not found'
      });
    }

    if (vehicleData.status !== 'completed') {
      return res.status(400).json({
        success: false,
        message: `Cannot export vehicle to CSV - status is "${vehicleData.status}", only "completed" vehicles can be exported`
      });
    }

    await csvService.addVehicleData(vehicleData);

    res.json({
      success: true,
      message: 'Vehicle data exported to CSV',
      data: { vehicleId }
    });
  } catch (error) {
    logger.error('CSV Export Controller Error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to export vehicle data to CSV',
      error: error.message
    });
  }
};

export const csvBulkExportController = async (req, res) => {
  try {
    const { startDate, endDate, limit = 1000 } = req.query;

    const query = { status: 'completed' };
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) query.createdAt.$gte = new Date(startDate);
      if (endDate) query.createdAt.$lte = new Date(endDate);
    }

    const Vehicle = (await import('../models/Vehicle.js')).default;
    const vehicles = await Vehicle.find(query)
      .limit(parseInt(limit))
      .sort({ createdAt: -1 });

    logger.info(`CSV Bulk Export: Processing ${vehicles.length} vehicles`);

    for (const vehicle of vehicles) {
      await csvService.addVehicleData(vehicle);
    }

    res.json({
      success: true,
      message: `${vehicles.length} vehicles exported to CSV`,
      data: {
        vehiclesProcessed: vehicles.length,
        query
      }
    });
  } catch (error) {
    logger.error('CSV Bulk Export Controller Error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to bulk export vehicles to CSV',
      error: error.message
    });
  }
};

export const csvStatsController = (req, res) => {
  try {
    const stats = csvService.getStatistics();

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    logger.error('CSV Stats Controller Error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get CSV statistics',
      error: error.message
    });
  }
};

export { csvService };

export default {
  csvCreateMiddleware,
  csvUpdateMiddleware,
  csvCompleteMiddleware,
  csvExportController,
  csvBulkExportController,
  csvStatsController,
  csvService
};
