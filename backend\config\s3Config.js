/**
 * AWS S3 Configuration
 * Centralized S3 settings for file storage
 */

import { S3Client } from '@aws-sdk/client-s3';
import logger from '../utils/logger.js';

// Validate required environment variables
const validateS3Config = () => {
  const required = [
    'AWS_ACCESS_KEY_ID',
    'AWS_SECRET_ACCESS_KEY',
    'AWS_REGION',
    'AWS_S3_BUCKET_NAME'
  ];

  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required S3 environment variables: ${missing.join(', ')}`);
  }
};

// S3 Configuration
export const s3Config = {
  region: process.env.AWS_REGION || 'us-east-1',
  bucketName: process.env.AWS_S3_BUCKET_NAME,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
  },
  
  // File upload settings
  upload: {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedMimeTypes: [
      'application/pdf',
      'image/jpeg',
      'image/png',
      'image/gif',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ],
    allowedExtensions: ['.pdf', '.jpg', '.jpeg', '.png', '.gif', '.doc', '.docx']
  },

  // S3 object settings
  object: {
    // Default ACL for uploaded files
    acl: 'private',
    
    // Cache control for files
    cacheControl: 'max-age=31536000', // 1 year
    
    // Server-side encryption
    serverSideEncryption: 'AES256'
  },

  // Folder structure in S3
  folders: {
    documents: 'documents',
    temp: 'temp',
    archive: 'archive'
  }
};

// Create S3 Client
let s3Client = null;

export const getS3Client = () => {
  if (!s3Client) {
    try {
      validateS3Config();
      
      s3Client = new S3Client({
        region: s3Config.region,
        credentials: s3Config.credentials
      });
      
      logger.info('S3 client initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize S3 client:', error);
      throw error;
    }
  }
  
  return s3Client;
};

// Helper function to generate S3 key (file path in bucket)
export const generateS3Key = (customerPhone, documentType, filename) => {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const extension = filename.split('.').pop();
  
  return `${s3Config.folders.documents}/customer_${customerPhone}/${documentType}_${timestamp}.${extension}`;
};

// Helper function to get file URL (for private files, this would be a presigned URL)
export const getFileUrl = (s3Key) => {
  return `https://${s3Config.bucketName}.s3.${s3Config.region}.amazonaws.com/${s3Key}`;
};

export default s3Config;
