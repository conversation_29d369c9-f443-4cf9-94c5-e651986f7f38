import dotenv from 'dotenv';
import connectDB from '../config/db.js';
import { cleanupPendingRecords } from '../utils/cleanupPendingRecords.js';
import logger from '../utils/logger.js';

// Load environment variables
dotenv.config();

// Connect to MongoDB
connectDB();

/**
 * <PERSON>ript to clean up stale pending records
 * Can be run manually or scheduled with a cron job
 */
const runCleanup = async () => {
  try {
    logger.info('Starting cleanup of stale pending records...');

    // Get hours from command line argument or use default (24)
    const hours = process.argv[2] ? parseInt(process.argv[2]) : 24;

    // Run the cleanup
    const deletedCount = await cleanupPendingRecords(hours);

    logger.info(`Cleanup complete. Deleted ${deletedCount} stale pending records.`);
    process.exit(0);
  } catch (error) {
    logger.error('Error during cleanup:', error);
    process.exit(1);
  }
};

// Run the cleanup
runCleanup();
